-- Country scope:
-- FR+
-- UK+
-- DE+
-- AT+
-- BE+
-- CA+
-- CH+
-- NL+
-- PL+
-- RO+
-- HU - 10 +
-- RS - 34 +



grant select on all tables in schema public to trino;


create temp table j_by_job_halfyear as
  SELECT j.date_created,
         j.date_expired,
         j.id,
         j.id_similar_group
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between '2025-01-01' and '2025-06-30'
Group by j.date_created,
         j.date_expired,
         j.id,
         j.id_similar_group
;

create temp table j_by_job_H123 as
SELECT   '2025H1'							as year_month,
         count(distinct j.id)               as jobs_cnt,
         count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_halfyear j
WHERE cast(j.date_created as date) between '2025-01-01' and '2025-06-30'
;

create temp table j_by_job_Q34 as
SELECT   '2025Q1'							as year_month,
         count(distinct j.id)               as jobs_cnt,
         count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_halfyear j
WHERE cast(j.date_created as date) between '2025-01-01' and '2025-03-31'
union all
SELECT   '2025Q2'							as year_month,
         count(distinct j.id)               as jobs_cnt,
         count(distinct j.id_similar_group) as unique_jobs_cnt
FROM j_by_job_halfyear j
WHERE cast(j.date_created as date) between '2025-04-01' and '2025-06-30'
;


-- final union with all segments
create temp table final_union as
        Select
           j1.year_month,
           null::integer                 as id_region,
           null::text                    as title,
           null::double precision        as salary_val1,
           null::double precision        as salary_val2,
           null::smallint                as id_currency,
           null::smallint                as id_salary_rate,
           null::smallint                as TOPNo,
           'jobs_by_halfyear'            as metric,
           'total'                       as metric_type,
           j1.jobs_cnt,
           j1.unique_jobs_cnt
    FROM j_by_job_H123 j1
union all
        Select
           j1.year_month,
           null::integer                 as id_region,
           null::text                    as title,
           null::double precision        as salary_val1,
           null::double precision        as salary_val2,
           null::smallint                as id_currency,
           null::smallint                as id_salary_rate,
           null::smallint                as TOPNo,
           'jobs_by_quarter'             as metric,
           'total'                       as metric_type,
           j1.jobs_cnt,
           j1.unique_jobs_cnt
    FROM j_by_job_Q34 j1
;


create table public.vnonv_agg_data_report_h1_2025 as
Select 34	as country_id,
       u.year_month,
       u.id_region		as region_id,
       u.title,
       u.salary_val1,
       u.salary_val2,
       u.id_currency	as currency_id,
       u.id_salary_rate as salary_rate_id,
       u.TOPNo			as top_num,
       u.metric,
       u.metric_type,
       u.jobs_cnt,
       u.unique_jobs_cnt,
	   'rs'		as country_code
FROM final_union u;




