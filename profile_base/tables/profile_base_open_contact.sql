select vp.database_source_id,
        e.id                     as employer_id,
        vp.profile_id,
        vp.action_datetime       as open_contact_datetime,
        vp.action_datediff       as open_contact_datediff,
        pcs.session_traffic_source_id,
        pcs.session_traffic_source_group_id,
        sp.packet_type_id,
        sp.packet_type_paid_result_id,
        case
            when jp.jcoin_paid_price_without_vat_uah is not null then jp.jcoin_paid_price_without_vat_uah
            else 0 end           as open_contact_price
from imp_employer.employer e
join employer.m_profile_open_contact_with_packet vp on vp.database_source_id = e.sources and e.id = vp.employer_id
join employer.m_subscription_packet sp on vp.database_source_id = sp.database_source_id and vp.subscription_id = sp.subscription_id and vp.packet_rank = sp.packet_rank
join profile.profile_submission_traffic_source pcs on pcs.profile_id = vp.profile_id
left join employer.v_jcoin_model_jcoin_price jp on sp.database_source_id = jp.database_source_id and sp.payment_id = jp.payment_id
where e.sources = 1 and vp.action_datediff between _datediff - 6 and _datediff and e.country_code = 'ua' and vp.feature_type_id = 3;
