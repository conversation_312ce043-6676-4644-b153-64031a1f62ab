with view_itc as (
        select distinct account_id,
               profile_id,
               is_view,
               view_date,
               is_intention_to_contact
               min(interact_date) as min_dt_action
        from profile_base.profile_base_itc_detailed
        group by account_id, profile_id, is_view, view_date, is_intention_to_contact)
select case when is_view = 1 and is_intention_to_contact = 0 then view_date else min_dt_action end as  min_dt_action,
       is_view,
       is_intention_to_contact,
       count(profile_id) as profile_cnt
from view_itc
group by case when is_view = 1 and is_intention_to_contact = 0 then view_date else min_dt_action end, is_view, is_intention_to_contact
order by min_dt_action desc;
