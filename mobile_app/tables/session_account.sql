do
$$

    declare _dd_start int = (select fn_get_date_diff('2022-06-01'));

    begin
            insert into mobile_app.session_account
            select s.country as country_id,
                   s.cookie_label,
                   s.date_diff as session_datediff,
                   s.id as session_id,
                   min(sa.id_account) as account_id,
                   count(distinct si.id) as impression_cnt,
                   count(distinct sion.id_impression) as impression_on_screen_cnt,
                   count(distinct sc.id) as serp_click_cnt,
                   count(distinct saw.id) as serp_away_cnt,
                   count(distinct sal.sub_id_alert) as subscription_cnt
            from imp.session s
            join imp.session_account sa
              on sa.country = s.country and
                 sa.date_diff = s.date_diff and
                 sa.id_session = s.id
            join imp.account_contact ac
                   on ac.country = sa.country and
                      ac.id_account = sa.id_account and
                      ac.type = 2 /*only mobile app accounts*/
            left join imp.session_alertview sal
                 on sal.country = s.country and
                    sal.date_diff = s.date_diff and
                    sal.id_session = s.id
            left join imp.session_impression si
                   on si.country = sal.country and
                      si.date = sal.date_diff and
                      si.id_session = sal.id_session and
                      si.id_alertview = sal.id
            left join imp.session_impression_on_screen sion
                  on sion.country = si.country and
                     sion.date_diff = si.date and
                     sion.id_impression = si.id
            left join imp.session_click sc
                   on sc.country = sion.country and
                      sc.date_diff = sion.date_diff and
                      sc.id_impression = sion.id_impression
            left join imp.session_away saw
                   on saw.country = sc.country and
                      saw.date_diff = sc.date_diff and
                      saw.id_click = sc.id
            where s.flags &64 = 64 /*mobile app*/ and
                  s.flags &1 = 0 /*not bots*/ and
                  s.date_diff >= _dd_start  -- 44711
            group by s.country,
                   s.cookie_label,
                   s.date_diff,
                   s.id
            ;
    end

$$
;
