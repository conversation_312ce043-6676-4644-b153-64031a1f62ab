create temp table temp_first_launch_attribute as
select lower(installation_id) as installation_id,
       first_launch_datetime,
       traffic_source,
       traffic_medium,
       traffic_campaign,
       platform,
       country_code
from mobile_app.first_launch_attribute;

create index temp_first_launch_attribute_idx on temp_first_launch_attribute (installation_id);


select ac.country                                     as country_id,
       ac.id_account                                  as account_id,
       ac.verify_date                                 as account_verification_datetime,
       ac.contact                                     as installation_id,
       s.cookie_label,
       s.id                                           as session_id,
       s.date_diff                                    as session_datediff,
       s.ip_cc,
       --case when tle.account_id > 0 then 1 else 0 end as is_in_funnel,
       maifs.traffic_source,
       maifs.platform as mobile_platform_name
from imp.account_contact ac
         join imp.email_account_interactions eai
              on eai.country = ac.country and
                 eai.id_account = ac.id_account and
                 eai.interaction_type = 0
         join imp.session s
              on s.country = eai.country and
                 s.id = eai.id_session
         left join temp_first_launch_attribute maifs
                   on  lower(ac.contact) = maifs.installation_id
where ac.type = 2
  and ac.verify_date = '2022-06-01'
  and s.flags & 64 = 64
  and ((s.flags & 1 = 0) or
       (s.flags & 1 = 1
           and s.flags & 128 = 128)
    )
;
