CREATE OR REPLACE FUNCTION an.select_itc_jdp(
_date_start integer, _date_end integer)
RETURNS TABLE(dt date, device int, placement int, is_ea int,jdp_response_type_id int,is_from_swipe int,
    views bigint,apply_clicks bigint,call_clicks bigint, away_clicks bigint, applies bigint, intention_to_contact bigint)
AS $$
BEGIN
  DROP TABLE IF EXISTS temp_session;
  DROP TABLE IF EXISTS temp_searches;
  DROP TABLE IF EXISTS temp_clicks_jdp;

CREATE TEMP TABLE temp_session as
select
    s.cookie_label,
    s.date_diff        as session_datediff,
    s.start_date,
    s.id               as session_id
from
    public.session s
where
      s.flags & 64 = 64 /*mobile app*/
  and s.flags & 1 = 0 /*not bots*/
  and s.date_diff BETWEEN _date_start and _date_end
group by
    s.cookie_label, s.date_diff, s.start_date, s.id;

CREATE TEMP TABLE temp_searches as
select
    session_datediff,
    session_id,
    case
        when (ss.search_source = 128 and ss.date_diff < 44887) or ss.search_source = 1004
            then 1
        when ss.search_source = 1001 then 2
        else 0 end as placement,
    ss.id          as id_search,
    null::bigint   as id_alertview
from
    temp_session s
    join public.session_search ss
         on ss.date_diff = s.session_datediff and
            ss.id_session = s.session_id;

insert into
    temp_searches
select
    session_datediff,
    session_id,
    case
        when sal.service_flags &
             536870912 = 536870912 then 2
        else 0 end as placement,
    null::bigint   as id_search,
    sal.id         as id_alertview
from
    temp_session s
    join public.session_alertview sal
         on sal.date_diff = s.session_datediff and
            sal.id_session = s.session_id;

CREATE TEMP TABLE temp_clicks_jdp as
select distinct s.session_datediff,
                s.start_date,
                s.session_id,
                sj.id                                                as id_jdp,
                case when sj.job_id_project = -1 then 1 else 0 end   as is_ea,
                case
                    when sj.flags & 4 = 4 /* easy apply form */ then 1 /* easy apply form */
                    when sj.flags & 8 = 8 /* mapped apply form */ then 2 /* mapped apply form */
                    else 0 end                                       as jdp_response_type_id,
                case when sj.id_ref_action is null then 0 else 1 end as is_from_swipe,
                coalesce(ts.placement, 0)                            as placement
from
    temp_session s
    join public.session_jdp sj
    on s.session_datediff = sj.date_diff
       and s.session_id = sj.id_session
    left join public.session_click sc
         on sc.date_diff = s.session_datediff and
            sj.id_click = sc.id
    left join public.session_impression si
              on si.date = sc.date_diff and
                 si.id = sc.id_impression
    left join temp_searches ts
              on s.session_datediff = ts.session_datediff and
                 (coalesce(sc.id_search, si.id_search) = ts.id_search
                      or coalesce(sc.id_alertview, si.id_alertview) = ts.id_alertview);
RETURN QUERY
    select date(tcj.start_date) as dt,
       2::int as device,
       tcj.placement,
       tcj.is_ea,
       tcj.jdp_response_type_id,
       tcj.is_from_swipe,
       count(distinct tcj.id_jdp)                                                                              as views,
       count(distinct case
                          when (sja.type in (1/*respond*/) and sja.flags & 2 = 2 /*easy_apply*/)
                              then tcj.id_jdp end)                                                             as apply_clicks,
       count(distinct case
                          when sja.type in (13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/,
                                            34/*view_multiply_employer_phone*/ )
                              then tcj.id_jdp end)                                                             as call_clicks,
       count(distinct case
                          when sja.type in (1/*respond*/) and sja.flags & 2 = 0 /*away*/
                              then tcj.id_jdp end)                                                             as away_clicks,
       count(distinct case
                          when sja.type in
                               ( 2/*easyapply_form_submit*/) and
                               sja.flags & 1 = 1 /*successful submit*/ then tcj.id_jdp end)                    as applies,
       count(distinct case
                          when sja.type in
                               (1/*respond*/, 13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/,
                                34/*view_multiply_employer_phone*/ )
                              then tcj.id_jdp end)                                                             as intention_to_contact
from temp_clicks_jdp tcj
left join public.session_jdp_action sja
     on tcj.session_datediff = sja.date_diff
         and tcj.id_jdp = sja.id_jdp and sja.type in (1/*respond*/, 30/*apply_no_cv_submit*/, 2/*easyapply_form_submit*/,
                                                  13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/,
                                                  34/*view_multiply_employer_phone*/ )
group by date(tcj.start_date),
     tcj.placement,
     tcj.is_ea,
     tcj.jdp_response_type_id,
     tcj.is_from_swipe;

END;
$$ LANGUAGE plpgsql;
