select
    country_id,
    session_datediff,
    session_id,
    sum(revenue_usd) as revenue_usd,
    revenue_type_id
from
    (
        select
            s.country                             as country_id,
            s.date_diff                           as session_datediff,
            s.id                                  as session_id,
            sum(sa.click_price * ic.value_to_usd) as revenue_usd,
            1 /*internal statistics*/             as revenue_type_id
        from
            imp.session_away sa
            join imp.session s
                 on sa.country = s.country and
                    sa.date_diff = s.date_diff and
                    sa.id_session = s.id
            join dimension.info_currency ic
                 on ic.country = sa.country and
                    ic.id = sa.id_currency
            left join imp.auction_campaign ac
                      on ac.country_id = sa.country and
                         ac.id = sa.id_campaign
            left join imp.site ast
                      on ac.country_id = ast.country and
                         ac.id_site = ast.id
            left join imp.auction_user au
                      on au.country = ast.country and
                         au.id = ast.id_user
                -- serp -> away
            left join imp.session_click sc
                      on sc.country = sa.country and
                         sc.date_diff = sa.date_diff and
                         sc.id = sa.id_click
        where
              sa.date_diff = 44711
          and s.flags & 64 = 64 /*mobile app*/
          and s.flags & 1 = 0
          and (sa.id_campaign = 0 or au.flags & 2 = 0)
          and sa.flags & 2 = 0
          and sa.flags & 512 = 0
        group by
            s.id,
            s.country,
            s.date_diff

        union all

        select
            s.country                             as country_id,
            s.date_diff                           as session_datediff,
            s.id                                  as session_id,
            sum(sc.click_price * ic.value_to_usd) as revenue_usd,
            2 /*external statistics*/             as revenue_type_id
        from
            imp.session_click sc
            join imp.session s
                 on sc.country = s.country and
                    sc.date_diff = s.date_diff and
                    sc.id_session = s.id
            join dimension.info_currency ic
                 on ic.country = sc.country and
                    ic.id = sc.id_currency
            left join imp.auction_campaign ac
                 on ac.country_id = sc.country and
                    ac.id = sc.id_campaign
            left join imp.site ast
                 on ac.country_id = ast.country and
                    ac.id_site = ast.id
            left join imp.auction_user au
                 on au.country = ast.country and
                    au.id = ast.id_user
            left join imp.session_away sa
                      on sa.country = sc.country and
                         sc.date_diff = sa.date_diff and
                         sc.id = sa.id_click
        where
              sc.date_diff = 44711
          and s.flags & 64 = 64 /*mobile app*/
          and s.flags & 1 = 0
          and au.flags & 2 = 2
          and sc.flags & 16 = 0
          and sc.flags & 4096 = 0
        group by
            s.id,
            s.country,
            s.date_diff

        union all

        select
            s.country                               as country_id,
            s.date_diff                             as session_datediff,
            s.id                                    as session_id,
            sum(scns.click_price * ic.value_to_usd) as revenue_usd,
            2 /*external statistics*/               as revenue_type_id
        from
            imp.session_click_no_serp scns
            join imp.session s
                 on scns.country = s.country and
                    scns.date_diff = s.date_diff and
                    scns.id_session = s.id
            join dimension.info_currency ic
                 on ic.country = scns.country and
                    ic.id = scns.id_currency
            join imp.auction_campaign ac
                 on ac.country_id = scns.country and
                    ac.id = scns.id_campaign
            join imp.site ast
                 on ac.country_id = ast.country and
                    ac.id_site = ast.id
            join imp.auction_user au
                 on au.country = ast.country and
                    au.id = ast.id_user
            left join imp.session_away sa
                      on sa.country = scns.country and
                         scns.date_diff = sa.date_diff and
                         scns.id = sa.id_click_no_serp
        where
              scns.date_diff = 44711
          and s.flags & 64 = 64 /*mobile app*/
          and s.flags & 1 = 0
          and au.flags & 2 = 2
          and scns.flags & 16 = 0
          and scns.flags & 4096 = 0
        group by
            s.id,
            s.country,
            s.date_diff
    ) t
group by
    country_id, session_datediff, session_id, revenue_type_id
;
