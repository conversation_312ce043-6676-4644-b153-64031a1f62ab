select c.alpha_2 as country_code,
       count(distinct ac.id_account) as account_cnt,
       case when ac.type = 2 then 'mobile app'
            when ac.type = 0 then 'email'
        end as account_type_name,
       maas.traffic_source,
       maas.platform,
       fn_get_date_diff(ac.verify_date) as account_verification_datediff,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+2 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_3_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_7_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.away_clicks_free+ar.away_clicks_premium else 0 end) as cumulative_away_14_days_cnt,
       sum(coalesce(ar.away_clicks_free+ar.away_clicks_premium,0)) as cumulative_away_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+2 then ar.total_revenue else 0 end) as cumulative_revenue_3_days_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.total_revenue else 0 end) as cumulative_revenue_7_days_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.total_revenue else 0 end) as cumulative_revenue_14_days_usd,
       sum(coalesce(ar.total_revenue,0)) as cumulative_revenue_usd,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+2 then ar.away_clicks_premium else 0 end) as cumulative_paid_away_3_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+6 then ar.away_clicks_premium else 0 end) as cumulative_paid_away_7_days_cnt,
       sum(case when ar.date_diff between fn_get_date_diff(ac.verify_date)  and fn_get_date_diff(ac.verify_date)+13 then ar.away_clicks_premium else 0 end) as cumulative_paid_away_14_days_cnt
from imp.account_contact ac
join dimension.countries c
  on c.id = ac.country
left join imp.account_revenue ar
  on ar.country = ac.country and
     ar.id_account = ac.id_account and
     ar.date_diff >= fn_get_date_diff( ac.verify_date)
left join mobile_app.account_source maas
      on maas.country_id = ac.country and
         maas.account_id = ac.id_account
where ac.verify_date >= '2022-06-01'
 and ac.type in (0,2)
group by ac.type, fn_get_date_diff(ac.verify_date), ac.country, maas.traffic_source,
       maas.platform,
         c.alpha_2
;
