select c.alpha_2 as country_code,
       marvw.user_type_id,
       verify_date_diff,
       user_cnt,
       week_retention_user_cnt,
       second_week_retention_user_cnt,
       third_week_retention_user_cnt,
       fourth_week_retention_user_cnt,
        first_week_session_cnt,
        second_week_session_cnt,
        third_week_session_cnt,
       fourth_week_session_cnt,
       first_week_serp_click_cnt,
       second_week_serp_click_cnt,

       third_week_serp_click_cnt,
       fourth_week_serp_click_cnt
from mobile_app.retention_vs_web marvw
join dimension.countries c
  on c.id = marvw.country_id;
