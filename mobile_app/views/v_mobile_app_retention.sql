create or replace view mobile_app.v_mobile_app_retention
            (period_type, country, country_id, signup_date, session_date, platform, traffic_source, traffic_medium,
             traffic_campaign, was_activated, user_type, user_cnt)
as
WITH account_activity AS (
    SELECT sabp.country_id,
           sabp.session_datediff,
           sabp.account_id,
           sum(sabp.serp_click_cnt)           AS serp_click_cnt,
           sum(sabp.impression_on_screen_cnt) AS impression_on_screen_cnt
    FROM mobile_app.session_account_by_placement sabp
    WHERE sabp.session_datediff >= fn_get_date_diff((CURRENT_DATE - 120)::timestamp without time zone)
    GROUP BY sabp.country_id, sabp.session_datediff, sabp.account_id
),
     weeks_table AS (
         SELECT d.dt                                                     AS day,
                date_trunc('week'::text, d.dt::timestamp with time zone) AS week
         FROM dimension.info_calendar d
        WHERE d.dt >= (CURRENT_DATE - 120)::timestamp with time zone
           AND d.dt < date_trunc('week'::text, CURRENT_DATE::timestamp with time zone)
     ),
     account_attribute AS (
         SELECT DISTINCT fla.installation_id,
                         last_value(fla.traffic_source)
                         OVER (PARTITION BY fla.installation_id ORDER BY fla.first_launch_datetime RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS traffic_source,
                         last_value(fla.traffic_medium)
                         OVER (PARTITION BY fla.installation_id ORDER BY fla.first_launch_datetime RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS traffic_medium,
                         last_value(fla.traffic_campaign)
                         OVER (PARTITION BY fla.installation_id ORDER BY fla.first_launch_datetime RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS traffic_campaign,
                         last_value(fla.platform)
                         OVER (PARTITION BY fla.installation_id ORDER BY fla.first_launch_datetime RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS platform
         FROM mobile_app.first_launch_attribute fla
     ),
     accounts_weekly AS (
         SELECT ac.country,
                ac.id_account,
                aa.platform,
                aa.traffic_source,
                aa.traffic_campaign,
                aa.traffic_medium,
                date_trunc('week'::text, ac.verify_date) AS signup_week
         FROM imp.account_contact ac
                  LEFT JOIN account_attribute aa ON lower(aa.installation_id::text) = lower(ac.contact::text)
         WHERE ac.type = 2
           AND date_trunc('week'::text, ac.verify_date) >= (CURRENT_DATE - 120)::timestamp with time zone
           AND date_trunc('week'::text, ac.verify_date) <=
               date_trunc('week'::text, (CURRENT_DATE - 7)::timestamp with time zone)
     ),
     account_click AS (
         SELECT a.country,
                a.id_account,
                a.platform,
                a.traffic_source,
                a.traffic_medium,
                a.traffic_campaign,
                a.signup_week,
                wt.week                AS session_week,
                sum(sa.serp_click_cnt) AS click_cnt
         FROM accounts_weekly a
                  JOIN weeks_table wt ON wt.week >= a.signup_week
                  LEFT JOIN account_activity sa ON sa.country_id = a.country AND sa.account_id = a.id_account AND
                                                   date_trunc('week'::text,
                                                              fn_get_date_from_date_diff(sa.session_datediff)::timestamp with time zone) =
                                                   wt.week
         GROUP BY a.country, a.id_account, a.signup_week, wt.week, a.platform, a.traffic_source, a.traffic_medium,
                  a.traffic_campaign
     ),
     weekly_active AS (
         SELECT acl.country,
                acl.id_account,
                acl.signup_week,
                acl.session_week,
                acl.platform,
                acl.traffic_source,
                acl.traffic_medium,
                acl.traffic_campaign,
                CASE
                    WHEN COALESCE(acl.click_cnt, 0::numeric) >= 1::numeric THEN 1
                    ELSE 0
                    END AS is_active,
                CASE
                    WHEN acl.session_week IS NULL OR acl.session_week = acl.signup_week THEN 1
                    ELSE 0
                    END AS is_new
         FROM account_click acl
     ),
     weekly_active_2 AS (
         SELECT wa.country,
                wa.id_account,
                wa.platform,
                wa.traffic_source,
                wa.traffic_medium,
                wa.traffic_campaign,
                wa.signup_week,
                wa.session_week,
                wa.is_active,
                wa.is_new,
                COALESCE(lag(wa.is_active) OVER (PARTITION BY wa.country, wa.id_account ORDER BY wa.session_week),
                         0)                                                                                                                                  AS prev_is_active,
                max(wa.is_active)
                OVER (PARTITION BY wa.country, wa.id_account ORDER BY wa.session_week RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW EXCLUDE CURRENT ROW) AS is_activated_yet,
                max(wa.is_active) OVER (PARTITION BY wa.country, wa.id_account)                                                                              AS was_activated,
                date_part('day'::text, wa.session_week - max(
                                                         CASE
                                                             WHEN wa.is_active = 1 THEN wa.session_week
                                                             ELSE NULL::timestamp with time zone
                                                             END)
                                                         OVER (PARTITION BY wa.country, wa.id_account ORDER BY wa.session_week RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW EXCLUDE CURRENT ROW)) /
                7::double precision                                                                                                                          AS weeks_from_prev_active
         FROM weekly_active wa
     ),
     weekly_result AS (
         SELECT wa2.country                    AS country_id,
                wa2.signup_week,
                wa2.session_week,
                wa2.platform,
                wa2.traffic_source,
                wa2.traffic_medium,
                wa2.traffic_campaign,
                wa2.was_activated,
                CASE
                    WHEN wa2.is_new = 1 AND wa2.is_active = 1 THEN 'new'::text
                    WHEN wa2.is_new = 1 AND wa2.is_active = 0 THEN 'new not activated'::text
                    WHEN wa2.is_new = 0 AND wa2.is_active = 0 THEN
                        CASE
                            WHEN wa2.is_activated_yet = 1 AND wa2.weeks_from_prev_active >= 7::double precision
                                THEN 'dead'::text
                            WHEN wa2.is_activated_yet = 1 THEN 'dormant'::text
                            WHEN wa2.is_activated_yet = 0 THEN 'not activated'::text
                            ELSE NULL::text
                            END
                    WHEN wa2.is_new = 0 AND wa2.is_active = 1 THEN
                        CASE
                            WHEN wa2.prev_is_active = 1 THEN 'retained'::text
                            WHEN wa2.prev_is_active = 0 AND wa2.is_activated_yet = 0 THEN 'late activated'::text
                            WHEN wa2.prev_is_active = 0 AND wa2.weeks_from_prev_active > 7::double precision
                                THEN 'late reactivated'::text
                            WHEN wa2.prev_is_active = 0 THEN 'resurrected'::text
                            ELSE NULL::text
                            END
                    ELSE NULL::text
                    END                        AS user_type,
                count(DISTINCT wa2.id_account) AS user_cnt
         FROM weekly_active_2 wa2
         GROUP BY wa2.country, wa2.signup_week, wa2.session_week, wa2.platform, wa2.traffic_source, wa2.traffic_medium,
                  wa2.traffic_campaign, wa2.was_activated,
                  (
                      CASE
                          WHEN wa2.is_new = 1 AND wa2.is_active = 1 THEN 'new'::text
                          WHEN wa2.is_new = 1 AND wa2.is_active = 0 THEN 'new not activated'::text
                          WHEN wa2.is_new = 0 AND wa2.is_active = 0 THEN
                              CASE
                                  WHEN wa2.is_activated_yet = 1 AND wa2.weeks_from_prev_active >= 7::double precision
                                      THEN 'dead'::text
                                  WHEN wa2.is_activated_yet = 1 THEN 'dormant'::text
                                  WHEN wa2.is_activated_yet = 0 THEN 'not activated'::text
                                  ELSE NULL::text
                                  END
                          WHEN wa2.is_new = 0 AND wa2.is_active = 1 THEN
                              CASE
                                  WHEN wa2.prev_is_active = 1 THEN 'retained'::text
                                  WHEN wa2.prev_is_active = 0 AND wa2.is_activated_yet = 0 THEN 'late activated'::text
                                  WHEN wa2.prev_is_active = 0 AND wa2.weeks_from_prev_active > 7::double precision
                                      THEN 'late reactivated'::text
                                  WHEN wa2.prev_is_active = 0 THEN 'resurrected'::text
                                  ELSE NULL::text
                                  END
                          ELSE NULL::text
                          END)
     )
SELECT 'weekly'::text        AS period_type,
       c.alpha_2             AS country,
       wr.country_id,
       wr.signup_week::date  AS signup_date,
       wr.session_week::date AS session_date,
       wr.platform,
       wr.traffic_source,
       wr.traffic_medium,
       wr.traffic_campaign,
       wr.was_activated,
       wr.user_type,
       wr.user_cnt
FROM weekly_result wr
         JOIN dimension.countries c ON c.id = wr.country_id;

alter view mobile_app.v_mobile_app_retention
    owner to ypr;

grant select on mobile_app.v_mobile_app_retention to user_agg_team;
