

select version();

-- PostgreSQL 16.2 on x86_64-pc-linux-gnu, compiled by gcc (GCC) 11.4.1 20230605 (Red Hat 11.4.1-2), 64-bit
------------------------------------------------------------------------------------------------
-- Replication slots


SELECT * FROM pg_publication;


-- DROP PUBLICATION dbz_publication_at_job;
-- DROP PUBLICATION dbz_publication_de_job;
-- DROP PUBLICATION dbz_publication_fr_job;
-- DROP PUBLICATION dbz_publication_pl_job;
-- DROP PUBLICATION dbz_publication_uk_job;
-- DROP PUBLICATION dbz_publication_pl_job;



-- ALTER PUBLICATION publication_name DROP TABLE table_name;
-- ALTER PUBLICATION dbz_publication_at_job SET public.job;
-- SELECT * FROM pg_create_logical_replication_slot('debezium_at_job', 'pgoutput');


SELECT * FROM pg_publication;
SELECT * FROM pg_publication_tables;


drop publication dbz_publication_at_job;
drop publication dbz_publication_de_job;
drop publication dbz_publication_uk_job;
drop publication dbz_publication_pl_job;
drop publication dbz_publication_fr_job;


CREATE PUBLICATION dbz_publication_at_job FOR TABLE public.job;
CREATE PUBLICATION dbz_publication_de_job FOR TABLE public.job;
CREATE PUBLICATION dbz_publication_uk_job FOR TABLE public.job;
CREATE PUBLICATION dbz_publication_pl_job FOR TABLE public.job;
CREATE PUBLICATION dbz_publication_fr_job FOR TABLE public.job;



---------------
-- select * from pg_replication_slots;


alter table public.abtest_email replica identity full ;
alter table public.email_visit replica identity nothing ;
alter table public.email_open replica identity nothing ;

alter table public.email_sent replica identity nothing ;
alter table public.email_sent_detail replica identity nothing ;

alter table public.banner_click replica identity nothing ;
alter table public.job_cluster replica identity nothing ;
alter table public.job_json_migration replica identity nothing ;
alter table public.job_sentinel replica identity nothing ;
alter table public.job_text replica identity nothing ;
-- alter table public.job_text_json_migration replica identity nothing ;
alter table public.last_imported_json replica identity nothing ;
alter table public.next_batch_selection_options replica identity nothing ;
-- alter table public.test_heartbeat_table replica identity full ;
-- alter table public.yiv_test_repl_table replica identity nothing ;



------------------------------------------------
--- Replication slots
------------------------------------------------
SELECT 'select pg_drop_replication_slot(' || quote_literal(slot_name) || ');' AS drop_replication_slot,
       database,
       slot_name,
       active,
       pg_size_pretty(pg_wal_lsn_diff(pg_current_wal_lsn(), restart_lsn))         AS replication_lag,
       pg_size_pretty(pg_wal_lsn_diff(pg_current_wal_lsn(), confirmed_flush_lsn)) AS confirmed_flush_lsn
FROM pg_replication_slots
;




------------------------------------------------




-- SELECT slot_name, active, pg_wal_lsn_diff(pg_current_wal_lsn(), restart_lsn) AS replication_lag_bytes  FROM pg_replication_slots;


select *
from public.test_heartbeat_table
order by heartbeat_time desc
;


CREATE TABLE public.test_heartbeat_table (
    id SERIAL PRIMARY KEY,
    heartbeat_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    value int
);


---------------


-- alter table public.job REPLICA IDENTITY FULL;
------------------------------------------------------------------------------------------------

SELECT pg_size_pretty(pg_current_wal_lsn() - '0/0'::pg_lsn) AS current_wal_size;
-- 6980 GB
SELECT COUNT(*) FROM pg_ls_dir('pg_wal');
-- 162



SELECT * FROM pg_stat_bgwriter;
show checkpoint_timeout;
show wal_level;
show min_wal_size;
show max_wal_size;
show wal_keep_size;
-- show wal_retention_policy;
show wal_segment_size;
-- show wal_keep_segments;
show max_slot_wal_keep_size;
show wal_sender_timeout;
show wal_receiver_timeout;      -- 1min
SHOW wal_keep_size;
SHOW max_replication_slots;


-- select pg_reload_conf();

select * from public.test_heartbeat_table order by heartbeat_time desc;

SELECT min(backend_start) AS server_start_time FROM pg_stat_activity;










------------------------------------------------------------------------------------------------
-- Test 1
------------------------------------------------------------------------------------------------

select date_created::date as date,
       count(*) as cnt
from public.job
where date_created::date >= '2024-03-10'
group by date_created::date
order by date desc
;


------------------------------------------------------------------------------------------------
select to_char(date_created, 'YYYY-MM'),
       count(*) as rows_cnt
from public.job
where date_created::date >= '2023-01-01'
group by to_char(date_created, 'YYYY-MM')
;


select count(*) from public.job;
-- 649 450 868

------------------------------------------------------------------------------------------------












SELECT COALESCE(blockingl.relation::regclass::text, blockingl.locktype) AS locked_item,
       blockeda.datname,
       now() - blockeda.query_start                                     AS waiting_duration,
       blockeda.pid                                                     AS blocked_pid,
       blockeda.query                                                   AS blocked_query,
       blockeda.usename                                                 AS blocked_user,
       blockedl.mode                                                    AS blocked_mode,
       blockinga.pid                                                    AS blocking_pid,
       blockinga.query                                                  AS blocking_query,
       blockingl.mode                                                   AS blocking_mode,
       blockinga.usename                                                AS blocked_by
FROM pg_locks blockedl
         JOIN pg_stat_activity blockeda ON blockedl.pid = blockeda.pid
         JOIN pg_locks blockingl ON (blockingl.transactionid = blockedl.transactionid OR
                                     blockingl.relation = blockedl.relation AND
                                     blockingl.locktype = blockedl.locktype) AND blockedl.pid <> blockingl.pid
         JOIN pg_stat_activity blockinga ON blockingl.pid = blockinga.pid AND blockinga.datid = blockeda.datid
WHERE NOT blockedl.granted
;






SELECT pg_stat_activity.pid,
       datname,
       pg_stat_activity.usename,
       pg_stat_activity.application_name,
       pg_stat_activity.query_start,
       (now() - pg_stat_activity.query_start)::time without time zone AS exec_time,
       pg_stat_activity.state,
       pg_stat_activity.query
FROM pg_stat_activity
where 1=1
--   and usename = 'ruslan.luchytskyi'
    and state = 'active'
ORDER BY pg_stat_activity.query
;




---- terminate
SELECT pg_terminate_backend(pid)
from pg_catalog.pg_stat_activity
where pid in (
931623
);


------------------------------------------------------------------------------------------------




