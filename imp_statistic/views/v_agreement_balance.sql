create view v_dte_sales
            (id, employer_id, company, points_available, active_job, competitor_jobs, competitor_cost, last_contact,
             blue_score, blue_score_ratio, priority, complete, manager, department, name, moderation_status_id,
             registred_date)
as
SELECT DISTINCT a.id,
                a.employer_id,
                a.name                                  AS company,
                ps.points_available,
                COALESCE(j.active_jobs, 0::bigint)      AS active_job,
                COALESCE(jc.competitor_jobs, 0)         AS competitor_jobs,
                COALESCE(jc.competitor_cost, 0::bigint) AS competitor_cost,
                act.last_contact,
                ah.blue_score,
                aah.blue_score_ratio,
                al.priority,
                ac.complete,
                u.name                                  AS manager,
                ut.name                                 AS department,
                aat.name,
                a.moderation_status_id,
                date(a.registred_date)                  AS registred_date
FROM imp_statistic.crm_lancer_account a
         LEFT JOIN (SELECT packet_subscription.id_employer,
                           packet_subscription.points_available
                    FROM imp_employer.packet_subscription
                    WHERE packet_subscription.status = 2
                    GROUP BY packet_subscription.id_employer, packet_subscription.points_available) ps
                   ON a.employer_id = ps.id_employer
         LEFT JOIN (SELECT job.id_employer,
                           count(DISTINCT job.id) AS active_jobs
                    FROM imp_employer.job
                    WHERE (job.flags & 1) = 1
                    GROUP BY job.id_employer) j ON a.employer_id = j.id_employer
         LEFT JOIN (SELECT DISTINCT trigger_competitors.account_id,
                                    max(trigger_competitors.all_vac) AS competitor_jobs,
                                    sum(trigger_competitors.cost)    AS competitor_cost
                    FROM imp_statistic.trigger_competitors
                    GROUP BY trigger_competitors.account_id) jc ON jc.account_id = a.id
         LEFT JOIN (SELECT DISTINCT activity.account_id,
                                    max(activity.created_on) AS last_contact
                    FROM imp_statistic.activity
                    WHERE activity.type_id = 3
                      AND activity.is_deleted = 0
                    GROUP BY activity.account_id) act ON act.account_id = a.id
         LEFT JOIN (SELECT ah_1.account_id,
                           ah_1.date_update,
                           ah_1.blue_score
                    FROM imp_statistic.account_hash ah_1
                             LEFT JOIN (SELECT account_hash.account_id,
                                               max(account_hash.date_update) AS date_update
                                        FROM imp_statistic.account_hash
                                        GROUP BY account_hash.account_id) hh ON hh.account_id = ah_1.account_id
                    WHERE ah_1.date_update = hh.date_update) ah ON a.id = ah.account_id
         LEFT JOIN (SELECT DISTINCT account_hash.account_id,
                                    round(avg(account_hash.blue_score), 1) AS blue_score_ratio
                    FROM imp_statistic.account_hash
                    GROUP BY account_hash.account_id) aah ON a.id = aah.account_id
         LEFT JOIN (SELECT DISTINCT account_list.account_id,
                                    account_list.priority
                    FROM imp_statistic.account_list
                    GROUP BY account_list.account_id, account_list.priority) al ON a.id = al.account_id
         LEFT JOIN (SELECT activity.account_id,
                           count(activity.id) AS complete
                    FROM imp_statistic.activity
                    WHERE activity.type_id = 3
                      AND activity.start < activity."end"
                    GROUP BY activity.account_id) ac ON a.id = ac.account_id
         JOIN imp_statistic."user" u ON a.user_id = u.id
         JOIN imp_statistic.account_type aat ON a.type_id = aat.id
         LEFT JOIN imp_statistic.user_type ut ON u.type_id = ut.id
WHERE a.department_id = 1
  AND a.is_deleted = 0
  AND u.type_id <> 3;

alter table v_dte_sales
    owner to npo;

grant select on v_dte_sales to readonly;

