declare @start_date date = ${dt_begin},
        @dt_end date = ${dt_end},
		@country_id int = ${country_id};

select
    @country_id as country_id, 
    country,
    cast(date as datetime) as date,
    id_project,
    site,
    contractor,
    id_campaign,
    campaign,
    click_count,
    click_price,
    currency,
    rel_bonus,
    job_count,
    session_count,
    ip_count,
    to_jdp_count,
    from_jdp_count,
    total_value,
    id_user,
    test_count,
    organic_count,
    paid_overflow_count,
    flags
from auction.click_statistic with(nolock)
where [date] between @start_date and @dt_end;
