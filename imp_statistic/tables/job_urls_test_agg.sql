declare @target_date date = dateadd(day, -3, cast(getdate() as date));

select
    id_project,
    country,
    date_pushed,
    uid
into #pushed
from dbo.jobs_pushed jp with (nolock)
where date_pushed = @target_date;

select
    country,
    id_project,
    uid,
    inactive,
    date_checked
into #active_d1
from dbo.jobs_inactive with (nolock)
where date_checked = dateadd(day, 1, @target_date)
  and inactive = 0;

select
    country,
    id_project,
    uid,
    inactive,
    date_checked
into #active_d2
from dbo.jobs_inactive with (nolock)
where date_checked = dateadd(day, 2, @target_date)
  and inactive = 0;

select
    country,
    id_project,
    uid,
    inactive,
    date_checked
into #active_d3
from dbo.jobs_inactive with (nolock)
where date_checked = dateadd(day, 3, @target_date)
  and inactive = 0;

select
    jp.id_project,
    jp.country,
    jp.date_pushed,
    count(distinct jp.uid) as job_pushed_cnt
into #pushed_agg
from #pushed jp with (nolock)
group by
    jp.id_project,
    jp.country,
    jp.date_pushed;

select
    jp.id_project,
    jp.country,
    jp.date_pushed,
    count(distinct jp.uid) as job_active_cnt_d1
into #active_d1_agg
from #pushed jp with (nolock)
     join #active_d1 ji1 with (nolock)
          on jp.uid = ji1.uid and
             jp.id_project = ji1.id_project and
             jp.country = ji1.country
group by
    jp.id_project,
    jp.country,
    jp.date_pushed;

select
    jp.id_project,
    jp.country,
    jp.date_pushed,
    count(distinct jp.uid) as job_active_cnt_d2
into #active_d2_agg
from #pushed jp with (nolock)
     join #active_d2 ji2 with (nolock)
          on jp.uid = ji2.uid and
             jp.id_project = ji2.id_project and
             jp.country = ji2.country
group by
    jp.id_project,
    jp.country,
    jp.date_pushed;

select
    jp.id_project,
    jp.country,
    jp.date_pushed,
    count(distinct jp.uid) as job_active_cnt_d3
into #active_d3_agg
from #pushed jp with (nolock)
     join #active_d3 ji3 with (nolock)
          on jp.uid = ji3.uid and
             jp.id_project = ji3.id_project and
             jp.country = ji3.country
group by
    jp.id_project,
    jp.country,
    jp.date_pushed;

select
    pa.country,
    pa.id_project,
    pa.date_pushed,
    pa.job_pushed_cnt,
    pa.job_pushed_cnt-coalesce(job_active_cnt_d1, 0) as job_inactive_cnt_d1,
    pa.job_pushed_cnt-coalesce(job_active_cnt_d2, 0) as job_inactive_cnt_d2,
    pa.job_pushed_cnt-coalesce(job_active_cnt_d3, 0) as job_inactive_cnt_d3
from #pushed_agg pa
     left join #active_d1_agg i1
               on i1.id_project = pa.id_project and i1.country = pa.country and i1.date_pushed = pa.date_pushed
     left join #active_d2_agg i2
               on i1.id_project = i2.id_project and i1.country = i2.country and i1.date_pushed = i2.date_pushed
     left join #active_d3_agg i3
               on i1.id_project = i3.id_project and i1.country = i3.country and i1.date_pushed = i3.date_pushed;

drop table #active_d1;
drop table #active_d2;
drop table #active_d3;
drop table #active_d1_agg;
drop table #active_d2_agg;
drop table #active_d3_agg;
drop table #pushed;
drop table #pushed_agg;