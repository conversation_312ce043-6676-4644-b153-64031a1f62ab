with 
    -- Step 1: Calculate Monthly Revenue History
     monthly_revenue_history             as (select country,
                                                    extract(month from revenue.date) as month_number,
                                                    sum(revenue_usd)                 as revenue
                                             from ono.dv_revenue_by_placement_and_src revenue
                                             where date_trunc('month', revenue.date)::date between
                                                 date_trunc('month', current_date - 1)::date - interval '3 months' and
                                                 date_trunc('month', current_date - 1)::date - interval '1 month'
                                               and placement in
                                                   ('alertview', 'external', 'letter type 8', 'recommendations',
                                                    'search')
                                                 /* PLACEMENTS EXCLUDED: 'adsense', 'ad exchange', 'mobile app', 'xs', 'other',
                                                 'category page', 'salary page', 'company page', 'job description page', 'skill page',
                                                 'letter type 1', 'letter type 13', 'letter type 14', 'letter type 15',
                                                 'letter type 18', 'letter type 19', 'letter type 3', 'letter type 39', 'letter type 4',
                                                 'letter type 5', 'letter type 54', 'letter type 56', 'letter type 57', 'letter type 6',
                                                 'letter type 65', 'letter type 71', 'letter type 74', , 'letter type 80', 'letter type 9',
                                                 'other letter types' */
                                             group by country, extract(month from revenue.date)),
     -- Step 2: Get AB Test Information
     ab_tests                            as (select distinct
                                                    test_id,
                                                    iteration,
                                                    c.name_country_eng as country,
                                                    device_type_id
                                             from dredd.v_main_metrics_abtest_daily_report r
                                                  left join dimension.countries c
                                                  on r.country_code = lower(c.alpha_2)
                                             where country_code is not null
                                               and device_type_id in (0, 1)),
     -- Step 3: Calculate Revenue Seasonality Coefficients
     revenue_seasonality_coefficients    as (select country,
                                                    device_type_id,
                                                    season_number                                                as month_number,
                                                    coefficient /
                                                    avg(coefficient) over (partition by country, device_type_id) as coefficient
                                             from dimension.revenue_season_coefficient
                                             where season_type = 'month'),
     -- Step 4: Calculate Average Monthly Revenue by Test, Country, and Device
     revenue_by_test_country_device_temp as (select t.test_id,
                                                    t.iteration,
                                                    t.country,
                                                    t.device_type_id,
                                                    avg(r.revenue * (1 / season.coefficient) *
                                                        device_prop.device_revenue_proportion) as average_monthly_revenue
                                             from ab_tests t
                                                  left join monthly_revenue_history r
                                                  on t.country = r.country
                                                  left join aggregation.v_abtest_device_revenue_proportion device_prop
                                                  on t.country = device_prop.country
                                                      and t.device_type_id = device_prop.device_type_id
                                                      and t.test_id = device_prop.test_id
                                                      and t.iteration = device_prop.iteration
                                                  left join revenue_seasonality_coefficients season
                                                  on t.country = season.country
                                                      and t.device_type_id = season.device_type_id
                                                      and r.month_number = season.month_number
                                             group by t.test_id,
                                                      t.iteration,
                                                      t.country,
                                                      t.device_type_id),
     -- Step 5: Add Totals (by country, by device, overall) to the previous table `revenue_by_test_country_device_temp`
     revenue_by_test_country_device      as (select test_id,
                                                    iteration,
                                                    country,
                                                    device_type_id,
                                                    average_monthly_revenue
                                             from revenue_by_test_country_device_temp

                                             union all

                                             select r.test_id,                                        -- add revenue with breakdown by country, but without breakdown by device
                                                    r.iteration,
                                                    r.country,
                                                    -1                             as device_type_id, -- here -1 means 'all devices'
                                                    sum(r.average_monthly_revenue) as average_monthly_revenue
                                             from revenue_by_test_country_device_temp r
                                             group by test_id, iteration, country

                                             union all

                                             select r.test_id, -- add revenue with breakdown by device, but without breakdown by country
                                                    r.iteration,
                                                    'Total'                        as country,
                                                    r.device_type_id,
                                                    sum(r.average_monthly_revenue) as average_monthly_revenue
                                             from revenue_by_test_country_device_temp r
                                             group by test_id, iteration, device_type_id

                                             union all

                                             select r.test_id, -- add revenue in total (without breakdown by country and device)
                                                    r.iteration,
                                                    'Total'                        as country,
                                                    -1                             as device_type_id,
                                                    sum(r.average_monthly_revenue) as average_monthly_revenue
                                             from revenue_by_test_country_device_temp r
                                             group by test_id, iteration),
     -- Step 6: Calculate Significance Statistics for A/B Tests
     significance_statistics             as (select r.test_id,
                                                    r.iteration,
                                                    coalesce(r.name_country_eng, 'Total')      as country,
                                                    coalesce(r.device_type_id, -1)             as device_type_id,
                                                    r.base_group_id,
                                                    r.competing_group_id,
                                                    r.delta,

                                                     /* WARNING! The following is different from how we determine significance in Main Metrics dashboard*/
                                                    (delta_ci_lower > 0 or delta_ci_upper < 0) as is_significant,  
                                                    first_value(end_date)
                                                    over (partition by r.test_id, r.iteration, r.name_country_eng, r.device_type_id
                                                        order by end_date desc)                as latest_available_report_date,
                                                    end_date
                                             from dredd.v_main_metrics_abtest_daily_report r
                                             where metric_name = 'revenue per 1k session'
                                               and competing_group_id >= 2),
     -- Step 7: Calculate Revenue Estimation Monthly
     revenue_estimation_monthly          as (select r.test_id,
                                                    r.iteration,
                                                    r.country,
                                                    r.device_type_id,
                                                    sign_stat.base_group_id,
                                                    sign_stat.competing_group_id,
                                                    sign_stat.latest_available_report_date,

                                                    sign_stat.delta,
                                                    sign_stat.is_significant,
                                                    r.average_monthly_revenue,
                                                    sign_stat.delta * r.average_monthly_revenue as average_monthly_revenue_gain
                                             from revenue_by_test_country_device r
                                                  left join significance_statistics sign_stat
                                                  on r.test_id = sign_stat.test_id
                                                      and r.iteration = sign_stat.iteration
                                                      and r.country = sign_stat.country
                                                      and r.device_type_id = sign_stat.device_type_id
                                                      and sign_stat.end_date = sign_stat.latest_available_report_date),
     -- Step 8: Calculate Revenue Estimation Yearly
     revenue_estimation_yearly           as (select r.test_id,
                                                    r.iteration,
                                                    r.country,
                                                    r.device_type_id,
                                                    -- Details:
                                                    r.base_group_id,
                                                    r.competing_group_id,
                                                    -- Main number
                                                    sum(r.average_monthly_revenue * season.coefficient)      as yearly_revenue,
                                                    sum(r.average_monthly_revenue_gain * season.coefficient) as yearly_revenue_gain
                                             from revenue_estimation_monthly r
                                                  left join revenue_seasonality_coefficients season
                                                  on r.country = season.country
                                                      and r.device_type_id = season.device_type_id
                                             group by r.test_id, r.iteration, r.country, r.device_type_id,
                                                      r.base_group_id, r.competing_group_id)
-- Step 9: Select the Final Output
select rm.test_id,
       rm.iteration,
       rm.country,
       rm.device_type_id,
       -- Details:
       rm.base_group_id,
       rm.competing_group_id,
       rm.delta,
       rm.is_significant,
       rm.latest_available_report_date,
       -- Main numbers
       rm.average_monthly_revenue,
       rm.average_monthly_revenue_gain,
       ry.yearly_revenue,
       ry.yearly_revenue_gain
from revenue_estimation_monthly rm
     left join revenue_estimation_yearly ry
     using (test_id,
            iteration,
            country,
            device_type_id,
            base_group_id,
            competing_group_id);
