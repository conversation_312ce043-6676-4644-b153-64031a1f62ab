create or replace view aggregation.v_additional_pages_click_daily_agg
            (country_name, page_name, search_source_name, search_source_id, session_datediff, traffic_name,
             traffic_channel,
             session_create_page_type, device_type_id, serp_click_cnt, away_cnt, revenue_usd, revenue_type)
as
SELECT c.name_country_eng AS country_name,
       spcda.page_name,
       ss.search_source_name,
       spcda.search_source_id,
       spcda.session_datediff,
       uts.name           AS traffic_name,
       uts.channel        AS traffic_channel,
       spcda.session_create_page_type,
       spcda.device_type_id,
       spcda.serp_click_cnt,
       spcda.away_cnt,
       spcda.revenue_usd,
       revenue_type
FROM aggregation.additional_pages_click_daily_agg spcda
         LEFT JOIN dimension.countries c ON spcda.country_id = c.id
         LEFT JOIN dimension.search_source ss ON ss.search_source_id = spcda.search_source_id
         LEFT JOIN dimension.u_traffic_source uts ON uts.country = spcda.country_id AND uts.id = spcda.traffic_source_id
WHERE spcda.session_datediff >= 44898
  AND (spcda.country_id = ANY
       (ARRAY [1, 2, 3, 7, 11, 4, 9, 18, 19, 20, 21, 26, 33, 35, 36, 39, 10, 51, 17, 55, 57, 59, 60, 61, 64, 66, 5, 6, 12, 13, 14, 15, 16]))
UNION ALL
/* The subscription form is available only on the salary page */
SELECT c.name_country_eng                                                   AS country_name,
       'salary'                                                             as page_name,
       ''::character varying(100)                                           AS search_source_name,
       NULL::integer                                                        AS search_source_id,
       fn_get_date_diff(ar.revenue_date::date::timestamp without time zone) AS session_datediff,
       ''::character varying(255)                                           AS traffic_name,
       ''::character varying(255)                                           AS traffic_channel,
       NULL::smallint                                                       AS session_create_page_type,
       NULL::smallint                                                       AS device_type_id,
       NULL::bigint                                                         AS serp_click_cnt,
       ar.away_cnt::bigint                                                  AS away_cnt,
       ar.account_revenue::money                                            AS revenue_usd,
       'account revenue'                                                    AS revenue_type
FROM aggregation.account_revenue ar
         LEFT JOIN dimension.countries c ON ar.country_id = c.id
WHERE (ar.source = ANY (ARRAY [57, 58]))
  AND fn_get_date_diff(ar.revenue_date::date::timestamp without time zone) >= 44898
  AND (ar.country_id = ANY
       (ARRAY [1, 2, 3, 7, 11, 4, 9, 18, 19, 20, 21, 26, 33, 35, 36, 39, 10, 51, 17, 55, 57, 59, 60, 61, 64, 66, 5, 6, 12, 13, 14, 15, 16]));

alter table aggregation.v_additional_pages_click_daily_agg
    owner to postgres;

grant select on aggregation.v_additional_pages_click_daily_agg to readonly;

grant select on aggregation.v_additional_pages_click_daily_agg to writeonly_pyscripts;
