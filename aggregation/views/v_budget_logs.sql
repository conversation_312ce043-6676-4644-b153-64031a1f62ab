create or replace view aggregation.v_budget_logs
            (country, user_id, project_id, project_name, campaign_id, campaign, client_buying_model,
             is_job_ad_exchange_project, in_affiliate_ban, is_daily_cmp_budget, is_price_per_job, date, metric_name,
             metric_value, prev_metric_value, next_metric_value, mtd_revenue, sale_manager, currency, contractor,
             user_cnt, campaign_cnt, site_cnt)
as
WITH unions AS (SELECT user_budget_log.country_id                                  AS id_country,
                       user_budget_log.id_user,
                       NULL::integer                                               AS id_project,
                       NULL::integer                                               AS id_campaign,
                       user_budget_log.date::date                                  AS date,
                       NULL::numeric                                               AS campaign_click_price,
                       NULL::numeric                                               AS campaign_daily_budget,
                       NULL::numeric                                               AS campaign_monthly_budget,
                       0                                                           AS paid_job_count,
                       user_budget_log.daily_budget * info_currency_1.value_to_usd AS user_daily_budget,
                       user_budget_log.budget * info_currency_1.value_to_usd       AS user_monthly_budget,
                       NULL::integer                                               AS campaign_status,
                       NULL::numeric                                               AS cpc_ppj_campaign
                FROM auction.user_budget_log
                         JOIN imp.auction_user ON user_budget_log.country_id = auction_user.country AND
                                                  user_budget_log.id_user = auction_user.id
                         JOIN dimension.info_currency info_currency_1
                              ON user_budget_log.country_id = info_currency_1.country AND
                                 auction_user.currency = info_currency_1.id
                WHERE user_budget_log.date::date >= '2022-01-01'::date
                UNION ALL
                SELECT jobs_stat_daily.id_country,
                       site.id_user,
                       jobs_stat_daily.id_project,
                       jobs_stat_daily.id_campaign,
                       jobs_stat_daily.date::date                              AS date,
                       NULL::numeric                                           AS campaign_click_price,
                       NULL::numeric                                           AS campaign_daily_budget,
                       NULL::numeric                                           AS campaign_monthly_budget,
                       NULL::integer                                           AS paid_job_count,
                       NULL::numeric                                           AS user_daily_budget,
                       NULL::numeric                                           AS user_monthly_budget,
                       NULL::integer                                           AS campaign_status,
                       sum(jobs_stat_daily.avg_cpc_for_paid_job_count_usd * jobs_stat_daily.paid_job_count::numeric) /
                       NULLIF(sum(jobs_stat_daily.paid_job_count), 0)::numeric AS cpc_ppj_campaign
                FROM aggregation.jobs_stat_daily
                         JOIN imp.site ON jobs_stat_daily.id_country = site.country AND
                                          jobs_stat_daily.id_project = site.id_project
                WHERE jobs_stat_daily.is_price_per_job::text = 'true'::text
                  AND jobs_stat_daily.date::date >= '2022-01-01'::date
                GROUP BY jobs_stat_daily.id_country, site.id_user, jobs_stat_daily.id_project,
                         jobs_stat_daily.id_campaign, (jobs_stat_daily.date::date)
                UNION ALL
                SELECT campaign_log.country_id                                  AS id_country,
                       site.id_user,
                       site.id_project,
                       campaign_log.id_campaign,
                       campaign_log.date,
                       campaign_log.click_price * info_currency_1.value_to_usd  AS campaign_click_price,
                       campaign_log.daily_budget * info_currency_1.value_to_usd AS campaign_daily_budget,
                       campaign_log.budget * info_currency_1.value_to_usd       AS campaign_monthly_budget,
                       NULL::integer                                            AS paid_job_count,
                       NULL::numeric                                            AS user_daily_budget,
                       NULL::numeric                                            AS user_monthly_budget,
                       CASE
                           WHEN (campaign_log.change_source = ANY (ARRAY [0, 1])) AND
                                (campaign_log.campaign_status = ANY (ARRAY [0, 1])) THEN campaign_log.campaign_status
                           ELSE NULL::smallint
                           END                                                  AS campaign_status,
                       NULL::numeric                                            AS cpc_ppj_campaign
                FROM auction.campaign_log
                         JOIN imp.campaign campaign_1 ON campaign_log.country_id = campaign_1.country AND
                                                         campaign_log.id_campaign = campaign_1.id
                         JOIN imp.site ON campaign_1.country = site.country AND campaign_1.id_project = site.id_project
                         JOIN dimension.info_currency info_currency_1
                              ON campaign_1.country = info_currency_1.country AND
                                 campaign_1.currency = info_currency_1.id
                WHERE campaign_log.date::date >= '2022-01-01'::date
                UNION ALL
                SELECT jobs_stat_daily.id_country,
                       jobs_stat_daily.id_user,
                       jobs_stat_daily.id_project,
                       NULL::integer                                                                                 AS id_campaign,
                       max(jobs_stat_daily.date::date)                                                               AS date,
                       NULL::numeric                                                                                 AS campaign_click_price,
                       NULL::numeric                                                                                 AS campaign_daily_budget,
                       NULL::numeric                                                                                 AS campaign_monthly_budget,
                       round(sum(jobs_stat_daily.paid_job_count) / count(DISTINCT jobs_stat_daily.date)::numeric,
                             0)                                                                                      AS paid_job_count,
                       NULL::numeric                                                                                 AS user_daily_budget,
                       NULL::numeric                                                                                 AS user_monthly_budget,
                       NULL::integer                                                                                 AS campaign_status,
                       NULL::numeric                                                                                 AS cpc_ppj_campaign
                FROM (SELECT jobs_stat_daily_1.id_country,
                             site.id_user,
                             jobs_stat_daily_1.id_project,
                             jobs_stat_daily_1.date,
                             sum(jobs_stat_daily_1.paid_job_count) AS paid_job_count
                      FROM aggregation.jobs_stat_daily jobs_stat_daily_1
                               JOIN imp.site ON jobs_stat_daily_1.id_country = site.country AND
                                                jobs_stat_daily_1.id_project = site.id_project
                      WHERE jobs_stat_daily_1.date::date >= '2022-01-01'::date
                        AND jobs_stat_daily_1.id_campaign IS NOT NULL
                      GROUP BY jobs_stat_daily_1.id_country, site.id_user, jobs_stat_daily_1.id_project,
                               jobs_stat_daily_1.date) jobs_stat_daily
                GROUP BY jobs_stat_daily.id_country, jobs_stat_daily.id_user, jobs_stat_daily.id_project,
                         (date_part('week'::text, jobs_stat_daily.date::date))),
     metric AS (SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'campaign_click_price'::text AS metric_name,
                       unions.campaign_click_price  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'campaign_daily_budget'::text AS metric_name,
                       unions.campaign_daily_budget  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'campaign_monthly_budget'::text AS metric_name,
                       unions.campaign_monthly_budget  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'campaign_budget'::text AS metric_name,
                       CASE
                           WHEN COALESCE(unions.campaign_monthly_budget, 0::numeric) <
                                COALESCE(unions.campaign_daily_budget * 30::numeric, 0::numeric) AND
                                COALESCE(unions.campaign_daily_budget * 30::numeric, 0::numeric) > 0::numeric
                               THEN unions.campaign_monthly_budget
                           WHEN COALESCE(unions.campaign_daily_budget * 30::numeric, 0::numeric) = 0::numeric
                               THEN unions.campaign_monthly_budget
                           ELSE unions.campaign_daily_budget * 30::numeric
                           END                 AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'paid_job_count'::text AS metric_name,
                       unions.paid_job_count  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'user_daily_budget'::text AS metric_name,
                       unions.user_daily_budget  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'user_monthly_budget'::text AS metric_name,
                       unions.user_monthly_budget  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'user_budget'::text AS metric_name,
                       CASE
                           WHEN COALESCE(unions.user_monthly_budget, 0::numeric) <
                                COALESCE(unions.user_daily_budget * 30::numeric, 0::numeric) AND
                                COALESCE(unions.user_daily_budget * 30::numeric, 0::numeric) > 0::numeric
                               THEN unions.user_monthly_budget
                           WHEN COALESCE(unions.user_daily_budget * 30::numeric, 0::numeric) = 0::numeric
                               THEN unions.user_monthly_budget
                           ELSE unions.user_daily_budget * 30::numeric
                           END             AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'campaign_status'::text AS metric_name,
                       unions.campaign_status  AS metric_value
                FROM unions
                UNION ALL
                SELECT unions.id_country,
                       unions.id_user,
                       unions.id_project,
                       unions.id_campaign,
                       unions.date,
                       'cpc_ppj_campaign'::text AS metric_name,
                       unions.cpc_ppj_campaign  AS metric_value
                FROM unions
                UNION ALL
                SELECT budget_revenue_daily_agg.country_id,
                       budget_revenue_daily_agg.user_id,
                       budget_revenue_daily_agg.project_id,
                       budget_revenue_daily_agg.id_campaign::integer AS id_campaign,
                       budget_revenue_daily_agg.action_date,
                       'active_user'::text                           AS metric_name,
                       NULL::numeric                                 AS metric_value
                FROM aggregation.budget_revenue_daily_agg
                WHERE budget_revenue_daily_agg.action_date = (CURRENT_DATE - 1)
                  AND budget_revenue_daily_agg.click_cnt > 0),
     budget_revenue_daily_agg AS (SELECT auction_user.company                      AS contractor,
                                         sum(budget_revenue_daily_agg.revenue_usd) AS revenue_usd
                                  FROM aggregation.budget_revenue_daily_agg
                                           LEFT JOIN imp.auction_user
                                                     ON budget_revenue_daily_agg.country_id = auction_user.country AND
                                                        budget_revenue_daily_agg.user_id = auction_user.id
                                  WHERE budget_revenue_daily_agg.action_date = (CURRENT_DATE - 1)
                                  GROUP BY auction_user.company),
     logs AS (SELECT countries.alpha_2                                                                                                                     AS country,
                     auction_user.id                                                                                                                       AS user_id,
                     info_project.id                                                                                                                       AS project_id,
                     COALESCE(info_project.name, site.url, REPLACE(auction_user.email, '@jooble.com', ''))                                                                                                 AS project_name,
                     metric.id_campaign                                                                                                                    AS campaign_id,
                     auction_user.company                                                                                                                  AS contractor,
                     CASE
                         WHEN (auction_user.flags & 65536) = 65536 THEN 'external_CPA'::text
                         WHEN (auction_user.flags & 2) = 2 THEN 'external_CPC'::text
                         ELSE 'internal_CPC'::text
                         END                                                                                                                               AS client_buying_model,
                     max(
                     CASE
                         WHEN info_project.hide_in_search THEN 1
                         ELSE 0
                         END)
                     OVER (PARTITION BY metric.id_country, metric.id_user)                                                                                 AS is_job_ad_exchange_project,
                     max(
                     CASE
                         WHEN general_feed_ignored_projects.id_project IS NOT NULL THEN 1
                         ELSE 0
                         END)
                     OVER (PARTITION BY metric.id_country, metric.id_user)                                                                                 AS in_affiliate_ban,
                     max(
                     CASE
                         WHEN campaign_1.daily_budget > 0::numeric THEN 1
                         ELSE 0
                         END)
                     OVER (PARTITION BY metric.id_country, metric.id_user)                                                                                 AS is_daily_cmp_budget,
                     max(
                     CASE
                         WHEN campaign_1.is_price_per_job = true THEN 1
                         ELSE 0
                         END)
                     OVER (PARTITION BY metric.id_country, metric.id_user)                                                                                 AS is_price_per_job,
                     metric.id_country,
                     metric.date,
                     metric.metric_name,
                     metric.metric_value,
                     lag(metric.metric_value)
                     OVER (PARTITION BY metric.id_country, metric.id_user, metric.id_project, metric.id_campaign, metric.metric_name ORDER BY metric.date) AS prev_metric_value,
                     budget_revenue_daily_agg.revenue_usd                                                                                                  AS mtd_revenue,
                     lead(metric.metric_value, 2)
                     OVER (PARTITION BY metric.id_country, metric.id_user, metric.id_project, metric.id_campaign, metric.metric_name ORDER BY metric.date) AS next_metric_value,
                     auction_user.currency
              FROM metric
                       JOIN dimension.countries ON metric.id_country = countries.id
                       LEFT JOIN dimension.info_project
                                 ON metric.id_country = info_project.country AND metric.id_project = info_project.id
                       LEFT JOIN imp.campaign campaign_1
                                 ON metric.id_country = campaign_1.country AND metric.id_campaign = campaign_1.id
                       LEFT JOIN imp.site ON metric.id_country = site.country AND campaign_1.id_site = site.id
                       LEFT JOIN imp.auction_user ON metric.id_country = auction_user.country AND
                                                     COALESCE(site.id_user, metric.id_user) = auction_user.id
                       LEFT JOIN budget_revenue_daily_agg
                                 ON auction_user.company::text = budget_revenue_daily_agg.contractor::text
                       LEFT JOIN imp.general_feed_ignored_projects
                                 ON general_feed_ignored_projects.country_id = metric.id_country AND
                                    general_feed_ignored_projects.id_project = site.id_project
              WHERE metric.metric_value IS NOT NULL
                 OR metric.metric_name = 'active_user'::text)
SELECT logs.country,
       logs.user_id,
       logs.project_id,
       logs.project_name,
       logs.campaign_id,
       campaign.name      AS campaign,
       logs.client_buying_model,
       logs.is_job_ad_exchange_project,
       logs.in_affiliate_ban,
       logs.is_daily_cmp_budget,
       logs.is_price_per_job,
       logs.date,
       logs.metric_name,
       logs.metric_value,
       logs.prev_metric_value,
       logs.next_metric_value,
       logs.mtd_revenue,
       v_sale_manager.sale_manager,
       info_currency.name AS currency,
       logs.contractor,
       NULL::bigint       AS user_cnt,
       NULL::bigint       AS campaign_cnt,
       NULL::bigint       AS site_cnt
FROM logs
         LEFT JOIN (SELECT distinct sale_manager, country, id_user
                        FROM aggregation.v_sale_manager) v_sale_manager
                   ON logs.id_country = v_sale_manager.country AND logs.user_id = v_sale_manager.id_user
         LEFT JOIN imp.campaign ON logs.id_country = campaign.country AND logs.campaign_id = campaign.id
         LEFT JOIN dimension.info_currency
                   ON logs.id_country = info_currency.country AND logs.currency = info_currency.id
WHERE (COALESCE(logs.metric_value, 0::numeric) + COALESCE(logs.prev_metric_value, 0::numeric)) > 0::numeric
   OR logs.metric_name = 'active_user'::text;

alter table aggregation.v_budget_logs
    owner to ono;

grant select on aggregation.v_budget_logs to ypr;

grant select on aggregation.v_budget_logs to vnov;

grant select on aggregation.v_budget_logs to oleksandra_yankovska;

