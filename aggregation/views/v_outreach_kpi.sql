create or replace view aggregation.v_outreach_kpi
            (period_id, period_name, period_closed, period_year, report_update_time, team_id, team_name, lead_id,
             lead_name, lead_country_cc, pay_type, bonus, bonus_currency, bonus_score, bonus_link, team_kpi, person_kpi)
as
SELECT kpi.id_period           AS period_id,
       lp.name                 AS period_name,
       CASE
           WHEN lp.closed = 1 THEN 'yes'::text
           ELSE 'no'::text
           END                 AS period_closed,
       lp.year                 AS period_year,
       kpi.report_update_time,
       kpi.id_team             AS team_id,
       team.team_name,
       kpi.id_lead             AS lead_id,
       kpi.team_lead_name      AS lead_name,
       NULL::character varying AS lead_country_cc,
       kpi.team_pay_type_name  AS pay_type,
       NULL::numeric           AS bonus,
       NULL::character varying AS bonus_currency,
       NULL::numeric           AS bonus_score,
       NULL::numeric           AS bonus_link,
       kpi.team_kpi,
       kpi.person_kpi
FROM aggregation.outreach_kpi_current kpi
         LEFT JOIN aggregation.outreach_period lp ON lp.id = kpi.id_period
         LEFT JOIN (SELECT DISTINCT cl.account_team_id   AS team_id,
                                    cl.account_team_name AS team_name
                    FROM aggregation.outreach_link_current cl) team ON team.team_id = kpi.id_team::numeric
WHERE kpi.team_pay_type_name::text = 'Office'::text
GROUP BY kpi.id_period, lp.name,
         (
             CASE
                 WHEN lp.closed = 1 THEN 'yes'::text
                 ELSE 'no'::text
                 END), lp.year, kpi.report_update_time, kpi.id_lead, kpi.team_lead_name, kpi.id_team, team.team_name,
         kpi.team_pay_type_name, kpi.team_kpi, kpi.person_kpi
UNION ALL
SELECT cl.period_id,
       cl.period_name,
       cl.period_closed,
       cl.period_year,
       cl.report_update_time,
       cl.team_id,
       cl.team_name,
       cl.lead_id,
       cl.lead_name,
       cl.lead_country_cc,
       cl.pay_type,
       cl.bonus,
       cl.bonus_currency,
       cl.bonus_score,
       cl.bonus_link,
       cl.team_kpi,
       cl.person_kpi
FROM aggregation.outreach_kpi_closed cl;

alter table aggregation.v_outreach_kpi
    owner to ono;

grant select on aggregation.v_outreach_kpi to readonly;

grant select on aggregation.v_outreach_kpi to pbi;

grant select on aggregation.v_outreach_kpi to readonly_aggregation;

grant select on aggregation.v_outreach_kpi to "pavlo.kvasnii";

grant select on aggregation.v_outreach_kpi to bdu;

grant select on aggregation.v_outreach_kpi to vnov;

