create view aggregation.v_chat_bot_jdp_away_clicks_agg
            (country, date, traffic_source_name, placement, away_type, project_id, project_name, campaign_name, ip_cc,
             is_paid, is_mobile, revenue_usd, jdp_away_count, id_account_chatbot, sale_manager, channel)
as
SELECT c.name_country_eng                                         AS country,
       date(fn_get_timestamp_from_date_diff(jac.action_datediff)) AS date,
       jac.traffic_source_name,
       jac.placement,
       jac.away_type,
       jac.project_id,
       jac.project_name,
       jac.campaign_name,
       jac.ip_cc,
       jac.is_paid,
       jac.is_mobile,
       jac.revenue_usd,
       jac.jdp_away_count,
       jac.id_account_chatbot,
       v_sale_manager.sale_manager,
       traffic_source.channel
FROM aggregation.jdp_away_clicks_agg jac
         LEFT JOIN dimension.countries c ON c.id = jac.country_id
         LEFT JOIN aggregation.v_sale_manager
                   ON jac.country_id = v_sale_manager.country AND jac.project_id::text = v_sale_manager.id_project::text
         LEFT JOIN (SELECT u_traffic_source.country,
                           u_traffic_source.name,
                           u_traffic_source.channel,
                           u_traffic_source.num
                    FROM (SELECT u_traffic_source_1.country,
                                 u_traffic_source_1.name,
                                 u_traffic_source_1.channel,
                                 row_number()
                                 OVER (PARTITION BY u_traffic_source_1.name, u_traffic_source_1.country ORDER BY u_traffic_source_1.id DESC) AS num
                          FROM dimension.u_traffic_source u_traffic_source_1) u_traffic_source
                    WHERE u_traffic_source.num = 1) traffic_source
                   ON jac.country_id = traffic_source.country AND jac.traffic_source_name::text = traffic_source.name::text
WHERE traffic_source.channel::text = 'ChatBotAgg'::text;

alter table aggregation.v_chat_bot_jdp_away_clicks_agg
    owner to ono;

grant select on aggregation.v_chat_bot_jdp_away_clicks_agg to readonly;

grant select on aggregation.v_chat_bot_jdp_away_clicks_agg to ypi;

grant select on aggregation.v_chat_bot_jdp_away_clicks_agg to ypr;

grant select on aggregation.v_chat_bot_jdp_away_clicks_agg to user_agg_team;

grant select on aggregation.v_chat_bot_jdp_away_clicks_agg to vnov;

