create view aggregation.v_paid_traffic_metrics_agg
AS
WITH jobs_stat AS (SELECT jsd.id_country                              AS country_id,
                          jsd.date::date                              AS date,
                          sum(jsd.paid_job_count)                     AS paid_job_count,
                          sum(jsd.avg_cpc_for_paid_job_count_usd * jsd.paid_job_count::numeric) /
                          NULLIF(sum(jsd.paid_job_count), 0)::numeric AS avg_cpc_for_paid_job_count,
                          sum(jsd.flag_32_jobs_count)                 AS flag_32_jobs_count
                   FROM aggregation.jobs_stat_daily jsd
                   GROUP BY jsd.id_country, jsd.date),
     auction_click AS (SELECT acs.country_id,
                              upper(countries.alpha_2::text)                      AS country,
                              acs.date::date                                      AS date,
                              sum(acs.paid_overflow_count)                        AS clicks_flag_32,
                              sum(acs.total_value / acs.click_count::numeric *
                                  (acs.click_count - acs.organic_count)::numeric) AS revenue_on_serp,
                              NULLIF(sum(acs.click_count - acs.organic_count), 0) AS click_on_serp
                       FROM aggregation.auction_click_statistic_analytics acs
                                JOIN dimension.countries ON acs.country_id = countries.id
                       WHERE acs.click_count > 0
                       GROUP BY acs.country_id, (upper(countries.alpha_2::text)), acs.date),
     job_auction_union AS (SELECT jobs_stat.date,
                                  jobs_stat.country_id,
                                  lower(auction_click.country) AS country,
                                  jobs_stat.paid_job_count,
                                  auction_click.clicks_flag_32,
                                  jobs_stat.avg_cpc_for_paid_job_count,
                                  jobs_stat.flag_32_jobs_count,
                                  auction_click.revenue_on_serp,
                                  auction_click.click_on_serp,
                                  sum(auction_click.revenue_on_serp)/sum(auction_click.click_on_serp)
                                    *sum(auction_click.clicks_flag_32)   AS revenue_flag_32
                           FROM jobs_stat
                                    LEFT JOIN auction_click ON jobs_stat.date = auction_click.date AND
                                                               jobs_stat.country_id = auction_click.country_id
                           GROUP BY jobs_stat.date, jobs_stat.country_id, auction_click.country,
                                    jobs_stat.paid_job_count, auction_click.clicks_flag_32,
                                    jobs_stat.avg_cpc_for_paid_job_count, jobs_stat.flag_32_jobs_count,
                                    auction_click.revenue_on_serp, auction_click.click_on_serp),
     final_union AS (SELECT paid_traffic_metrics_agg.date,
                            paid_traffic_metrics_agg.country,
                            paid_traffic_metrics_agg.is_active,
                            paid_traffic_metrics_agg.source,
                            paid_traffic_metrics_agg.label,
                            paid_traffic_metrics_agg.imp,
                            paid_traffic_metrics_agg.clicks,
                            paid_traffic_metrics_agg.cost_usd,
                            paid_traffic_metrics_agg.rev,
                            paid_traffic_metrics_agg.csa,
                            paid_traffic_metrics_agg.ea,
                            paid_traffic_metrics_agg.rev_1,
                            paid_traffic_metrics_agg.csa_1,
                            paid_traffic_metrics_agg.ea_1,
                            0 AS paid_job_count,
                            0 AS clicks_flag_32,
                            0 AS avg_cpc_for_paid_job_count,
                            0 AS flag_32_jobs_count,
                            0 AS revenue_on_serp,
                            0 AS click_on_serp,
                            0 AS revenue_flag_32
                     FROM aggregation.paid_traffic_metrics_agg
                     UNION ALL
                     SELECT job_auction_union.date,
                            job_auction_union.country,
                            CASE
                                WHEN lower(job_auction_union.country) = ANY
                                     (ARRAY ['ca'::text, 'ru'::text, 'at'::text, 'be'::text, 'br'::text, 'ch'::text, 'cl'::text, 'cz'::text, 'de'::text, 'dk'::text, 'es'::text, 'fr'::text, 'hu'::text, 'it'::text, 'kz'::text, 'nl'::text, 'pl'::text, 'pt'::text, 'ro'::text, 'se'::text, 'sg'::text, 'tr'::text, 'uk'::text, 'us'::text, 'za'::text])
                                    THEN 1
                                ELSE 0
                                END    AS is_active,
                            NULL::text AS source,
                            NULL::text AS label,
                            0          AS imp,
                            0          AS clicks,
                            0          AS cost_usd,
                            0          AS rev,
                            0          AS csa,
                            0          AS ea,
                            0          AS rev_1,
                            0          AS csa_1,
                            0          AS ea_1,
                            job_auction_union.paid_job_count,
                            job_auction_union.clicks_flag_32,
                            job_auction_union.avg_cpc_for_paid_job_count,
                            job_auction_union.flag_32_jobs_count,
                            job_auction_union.revenue_on_serp,
                            job_auction_union.click_on_serp,
                            job_auction_union.revenue_flag_32
                     FROM job_auction_union
                     )
SELECT final_union.date,
       final_union.country,
       final_union.is_active,
       final_union.source,
       final_union.label,
       final_union.imp,
       final_union.clicks,
       final_union.cost_usd,
       final_union.rev,
       final_union.csa,
       final_union.ea,
       final_union.rev_1,
       final_union.csa_1,
       final_union.ea_1,
       final_union.paid_job_count,
       final_union.clicks_flag_32,
       final_union.avg_cpc_for_paid_job_count,
       final_union.flag_32_jobs_count,
       final_union.revenue_on_serp,
       final_union.click_on_serp,
       final_union.revenue_flag_32
FROM final_union
WHERE final_union.country::text <> ALL
      (ARRAY ['ua'::character varying::text, 'ro'::character varying::text, 'hu'::character varying::text]);


alter table aggregation.v_paid_traffic_metrics_agg
    owner to ono;

grant select on aggregation.v_paid_traffic_metrics_agg to readonly;

grant select on aggregation.v_paid_traffic_metrics_agg to yb;

grant select on aggregation.v_paid_traffic_metrics_agg to ypr;

grant select on aggregation.v_paid_traffic_metrics_agg to writeonly_pyscripts;

grant select on aggregation.v_paid_traffic_metrics_agg to user_agg_team;

grant select on aggregation.v_paid_traffic_metrics_agg to vnov;

grant select on aggregation.v_paid_traffic_metrics_agg to readonly_aggregation;

grant select on aggregation.v_paid_traffic_metrics_agg to "pavlo.kvasnii";

