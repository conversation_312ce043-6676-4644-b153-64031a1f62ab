create view v_finance_report
            (date, year_date, quarter_date, month_date, week_date, country, country_code, revenue_type, revenue_usd,
             test_revenue_usd, adv_impression, adv_view, adv_click, project_site, manager_name, cost_type, cost_usd,
             ga_users, ga_session, project_site_budget_usd)
as
WITH ea_sales_usd AS (
    SELECT employer_balance_history.date_created::date             AS date,
           countries.name_country_eng                              AS country,
           countries.alpha_2                                       AS country_code,
           employer_balance_history.value / currency_source.to_usd /
           (employer_balance.vat / 100::numeric + 1::numeric)      AS value_usd,
           employer_balance_history.change_type,
           employer_balance_history.value                          AS in_local_currency,
           info_currency.iso_code                                  AS currency,
           employer_balance.vat,
           employer_balance_history.value / currency_source.to_usd AS in_usd_with_vat,
           employer_balance_history.sources                        AS server,
           employer_balance_history.id_employer
    FROM imp_employer.employer_balance_history
             LEFT JOIN imp_employer.employer_balance ON employer_balance.sources = employer_balance_history.sources AND
                                                        employer_balance.id_employer =
                                                        employer_balance_history.id_employer
             LEFT JOIN imp_employer.info_currency ON employer_balance.sources = info_currency.sources AND
                                                     employer_balance.id_currency = info_currency.id
             LEFT JOIN imp_statistic.currency_source
                       ON currency_source.date = employer_balance_history.date_created::date AND
                          currency_source.currency = info_currency.iso_code::bpchar
             LEFT JOIN imp_employer.employer ON employer.id = employer_balance_history.id_employer AND
                                                employer.sources = employer_balance_history.sources
             LEFT JOIN dimension.countries ON lower(employer.country_code::text) = lower(countries.alpha_2::text)
    WHERE employer_balance_history.change_type = 0
),
     revenue AS (
         SELECT adsense_afs.create_date                                          AS date,
                COALESCE(countries.name_country_eng, 'Other'::character varying) AS country,
                COALESCE(countries.alpha_2, 'Other'::character varying)          AS country_code,
                'AdSense for Search'::text                                       AS revenue_type,
                adsense_afs.estimated_income_usd                                 AS revenue,
                0                                                                AS total_test_value,
                adsense_afs.show                                                 AS impressions,
                adsense_afs.views_page                                           AS views,
                adsense_afs.click                                                AS clicks,
                NULL::text                                                       AS manager_name,
                NULL::text                                                       AS project_site,
                0                                                                AS user_budget_usd
         FROM imp_statistic.adsense_afs
                  LEFT JOIN dimension.info_country_adsense
                            ON adsense_afs.country::text = info_country_adsense.country_report_name
                  LEFT JOIN dimension.countries
                            ON lower(info_country_adsense.country_code) = lower(countries.alpha_2::text)
         UNION ALL
         SELECT adsense_afc.create_date,
                CASE
                    WHEN adsense_afc.site_address::text = 'jooble.org'::text THEN 'United States'::character varying
                    ELSE COALESCE(countries.name_country_eng, 'Other'::character varying)
                    END                     AS "coalesce",
                CASE
                    WHEN adsense_afc.site_address::text = 'jooble.org'::text THEN 'US'::character varying
                    ELSE COALESCE(countries.alpha_2, 'Other'::character varying)
                    END                     AS "coalesce",
                'AdSense for Content'::text AS text,
                adsense_afc.estimated_income_usd,
                0                           AS total_test_value,
                adsense_afc.show            AS impressions,
                adsense_afc.view_page       AS views,
                adsense_afc.click           AS clicks,
                NULL::text                  AS manager_name,
                NULL::text                  AS project_site,
                0                           AS user_budget_usd
         FROM imp_statistic.adsense_afc
                  LEFT JOIN dimension.countries ON "left"(adsense_afc.site_address::text, 2) = countries.alpha_2::text
         UNION ALL
         SELECT auction_click_statistic.date,
                countries.name_country_eng,
                countries.alpha_2,
                'Auction'::text                                                                              AS text,
                auction_click_statistic.total_value,
                COALESCE(auction_click_statistic.total_value -
                         (auction_click_statistic.click_count - auction_click_statistic.test_count -
                          auction_click_statistic.to_jdp_count)::numeric * (auction_click_statistic.total_value /
                                                                            NULLIF(auction_click_statistic.click_count -
                                                                                   auction_click_statistic.to_jdp_count,
                                                                                   0)::numeric),
                         0::numeric)                                                                         AS total_test_value,
                0                                                                                            AS impressions,
                0                                                                                            AS views,
                auction_click_statistic.click_count                                                          AS clicks,
                crm_manageranddomain.manager_name,
                auction_click_statistic.site                                                                 AS project_site,
                budget_and_revenue.user_budget_usd
         FROM imp_statistic.auction_click_statistic
                  LEFT JOIN dimension.countries ON auction_click_statistic.country::text = countries.alpha_2::text
                  LEFT JOIN imp_statistic.crm_manageranddomain
                            ON auction_click_statistic.site::text = btrim(crm_manageranddomain.domain_name::text)
                  LEFT JOIN (SELECT budget_and_revenue_archive.date,
                                    budget_and_revenue_archive.country,
                                    budget_and_revenue_archive.id_user,
                                    CASE
                                        WHEN max(budget_and_revenue_archive.is_unlim_cmp) = 1 THEN 0::numeric
                                        WHEN max(budget_and_revenue_archive.user_budget_month_usd) = 0::numeric AND
                                             sum(budget_and_revenue_archive.campaign_budget_month_usd) = 0::numeric
                                            THEN 0::numeric
                                        WHEN max(budget_and_revenue_archive.user_budget_month_usd) >
                                             sum(budget_and_revenue_archive.campaign_budget_month_usd)
                                            THEN max(budget_and_revenue_archive.user_budget_month_usd)
                                        ELSE sum(budget_and_revenue_archive.campaign_budget_month_usd)
                                        END AS user_budget_usd
                             FROM ono.budget_and_revenue_archive
                             GROUP BY budget_and_revenue_archive.date, budget_and_revenue_archive.country,
                                      budget_and_revenue_archive.id_user) budget_and_revenue
                            ON auction_click_statistic.country::text = budget_and_revenue.country AND
                               auction_click_statistic.date = budget_and_revenue.date AND
                               auction_click_statistic.id_user = budget_and_revenue.id_user
         UNION ALL
         SELECT ea_sales_usd.date,
                ea_sales_usd.country,
                ea_sales_usd.country_code,
                'Employer Account'::text    AS revenue_type,
                sum(ea_sales_usd.value_usd) AS revenue,
                0                           AS total_test_value,
                0                           AS impressions,
                0                           AS views,
                0                           AS clicks,
                NULL::text                  AS manager_name,
                NULL::text                  AS project_site,
                0                           AS user_budget_usd
         FROM ea_sales_usd
         GROUP BY ea_sales_usd.date, ea_sales_usd.country, ea_sales_usd.country_code
     ),
     cost AS (
         SELECT adwords.day                                                                 AS date,
                countries.name_country_eng                                                  AS country,
                countries.alpha_2                                                           AS country_code,
                NULL::text                                                                  AS revenue_type,
                'AdWords'::text                                                             AS cost_type,
                0                                                                           AS revenue,
                adwords.impressions,
                0                                                                           AS views,
                adwords.clicks,
                adwords.cost::numeric / COALESCE(currency_source.to_usd, avg_to_usd.to_usd) AS cost
         FROM imp_statistic.adwords
                  LEFT JOIN dimension.countries ON COALESCE(adwords.country,
                                                            lower("left"(adwords.campaign::text, 2))::character varying)::text =
                                                   lower(countries.alpha_2::text)
                  LEFT JOIN imp_statistic.currency_source
                            ON adwords.day = currency_source.date AND currency_source.currency = 'COP'::bpchar
                  LEFT JOIN (SELECT date_part('year'::text, currency_source_1.date)  AS year,
                                    date_part('month'::text, currency_source_1.date) AS month,
                                    avg(currency_source_1.to_usd)                    AS to_usd
                             FROM imp_statistic.currency_source currency_source_1
                             WHERE currency_source_1.currency = 'COP'::bpchar
                             GROUP BY (date_part('year'::text, currency_source_1.date)),
                                      (date_part('month'::text, currency_source_1.date))) avg_to_usd
                            ON date_part('year'::text, adwords.day) = avg_to_usd.year AND
                               date_part('month'::text, adwords.day) = avg_to_usd.month
         UNION ALL
         SELECT facebook_2018.date_start                                                           AS date,
                countries.name_country_eng,
                countries.alpha_2                                                                  AS country_code,
                NULL::text                                                                         AS revenue_type,
                'FaceBook'::text                                                                   AS cost_type,
                0                                                                                  AS revenue,
                facebook_2018.impressions,
                0                                                                                  AS views,
                facebook_2018.clicks,
                facebook_2018.spend::numeric / COALESCE(currency_source.to_usd, avg_to_usd.to_usd) AS cost
         FROM imp_statistic.facebook_2018
                  LEFT JOIN dimension.countries
                            ON lower("left"(facebook_2018.campaign_name::text, 2)) = lower(countries.alpha_2::text)
                  LEFT JOIN imp_statistic.currency_source
                            ON facebook_2018.date_start = currency_source.date AND currency_source.currency = 'COP'::bpchar
                  LEFT JOIN (SELECT date_part('year'::text, currency_source_1.date)  AS year,
                                    date_part('month'::text, currency_source_1.date) AS month,
                                    avg(currency_source_1.to_usd)                    AS to_usd
                             FROM imp_statistic.currency_source currency_source_1
                             WHERE currency_source_1.currency = 'COP'::bpchar
                             GROUP BY (date_part('year'::text, currency_source_1.date)),
                                      (date_part('month'::text, currency_source_1.date))) avg_to_usd
                            ON date_part('year'::text, facebook_2018.date_start) = avg_to_usd.year AND
                               date_part('month'::text, facebook_2018.date_start) = avg_to_usd.month
         UNION ALL
         SELECT yandex_2018.date,
                countries.name_country_eng                                                               AS country,
                countries.alpha_2                                                                        AS country_code,
                NULL::text                                                                               AS revenue_type,
                'Yandex'::text                                                                           AS cost_type,
                0                                                                                        AS revenue,
                yandex_2018.impressions,
                0                                                                                        AS views,
                yandex_2018.clicks,
                yandex_2018.cost / COALESCE(currency_source.to_usd, avg_to_usd.to_usd)::double precision AS cost
         FROM imp_statistic.yandex_2018
                  LEFT JOIN dimension.countries
                            ON lower("left"(replace(yandex_2018.login::text, 'yd'::text, ''::text), 2)) =
                               lower(countries.alpha_2::text)
                  LEFT JOIN imp_statistic.currency_source
                            ON yandex_2018.date = currency_source.date AND currency_source.currency = 'RUB'::bpchar
                  LEFT JOIN (SELECT date_part('year'::text, currency_source_1.date)  AS year,
                                    date_part('month'::text, currency_source_1.date) AS month,
                                    avg(currency_source_1.to_usd)                    AS to_usd
                             FROM imp_statistic.currency_source currency_source_1
                             WHERE currency_source_1.currency = 'RUB'::bpchar
                             GROUP BY (date_part('year'::text, currency_source_1.date)),
                                      (date_part('month'::text, currency_source_1.date))) avg_to_usd
                            ON date_part('year'::text, yandex_2018.date) = avg_to_usd.year AND
                               date_part('month'::text, yandex_2018.date) = avg_to_usd.month
     ),
     google_analytics AS (
         SELECT ga.date,
                countries.name_country_eng AS country,
                countries.alpha_2          AS country_code,
                ga.users,
                ga.sessions
         FROM (SELECT google_analytics_general.date,
                      CASE
                          WHEN google_analytics_general.name::text = 'https://jooble.org'::text THEN 'US'::text
                          ELSE upper(replace(replace(replace(replace(replace(google_analytics_general.name::text,
                                                                             'https://'::text, ''::text),
                                                                     '.jooble.org'::text, ''::text),
                                                             '.jooble.com'::text, ''::text), 'http://'::text, ''::text),
                                             'www.'::text, ''::text))
                          END                                AS country,
                      sum(google_analytics_general.users)    AS users,
                      sum(google_analytics_general.sessions) AS sessions
               FROM imp_statistic.google_analytics_general
               WHERE google_analytics_general.table_num = 6
               GROUP BY google_analytics_general.date,
                        (
                            CASE
                                WHEN google_analytics_general.name::text = 'https://jooble.org'::text THEN 'US'::text
                                ELSE upper(replace(replace(replace(replace(replace(google_analytics_general.name::text,
                                                                                   'https://'::text, ''::text),
                                                                           '.jooble.org'::text, ''::text),
                                                                   '.jooble.com'::text, ''::text), 'http://'::text,
                                                           ''::text), 'www.'::text, ''::text))
                                END)) ga
                  LEFT JOIN dimension.countries ON ga.country = countries.alpha_2::text
     ),
     all_unions AS (
         SELECT revenue.date,
                revenue.country,
                revenue.country_code,
                revenue.revenue_type,
                abs(revenue.revenue) AS revenue_usd,
                revenue.total_test_value,
                revenue.impressions  AS adv_impressions,
                revenue.views        AS adv_views,
                revenue.clicks       AS adv_clicks,
                revenue.project_site,
                revenue.manager_name,
                NULL::text           AS cost_type,
                0                    AS cost_usd,
                0                    AS ga_users,
                0                    AS ga_sessions,
                revenue.user_budget_usd
         FROM revenue
         UNION ALL
         SELECT cost.date,
                cost.country,
                cost.country_code,
                NULL::text AS text,
                0,
                0,
                cost.impressions,
                cost.views,
                cost.clicks,
                NULL::text AS text,
                NULL::text AS text,
                cost.cost_type,
                cost.cost  AS cost_usd,
                0          AS ga_users,
                0          AS ga_sessions,
                0          AS user_budget_usd
         FROM cost
         UNION ALL
         SELECT google_analytics.date,
                google_analytics.country,
                google_analytics.country_code,
                NULL::text                AS text,
                0,
                0,
                0,
                0,
                0,
                NULL::text                AS text,
                NULL::text                AS text,
                NULL::text                AS text,
                0,
                google_analytics.users    AS ga_users,
                google_analytics.sessions AS ga_sessions,
                0                         AS user_budget_usd
         FROM google_analytics
     )
SELECT all_unions.date,
       date_part('year'::text, all_unions.date)    AS year_date,
       date_part('quarter'::text, all_unions.date) AS quarter_date,
       date_part('month'::text, all_unions.date)   AS month_date,
       date_part('week'::text, all_unions.date)    AS week_date,
       all_unions.country,
       all_unions.country_code,
       all_unions.revenue_type,
       sum(all_unions.revenue_usd)                 AS revenue_usd,
       sum(all_unions.total_test_value)            AS test_revenue_usd,
       sum(all_unions.adv_impressions)             AS adv_impression,
       sum(all_unions.adv_views)                   AS adv_view,
       sum(all_unions.adv_clicks)                  AS adv_click,
       all_unions.project_site,
       all_unions.manager_name,
       all_unions.cost_type,
       sum(all_unions.cost_usd)                    AS cost_usd,
       sum(all_unions.ga_users)                    AS ga_users,
       sum(all_unions.ga_sessions)                 AS ga_session,
       max(all_unions.user_budget_usd)             AS project_site_budget_usd
FROM all_unions
GROUP BY all_unions.date, (date_part('year'::text, all_unions.date)), (date_part('quarter'::text, all_unions.date)),
         (date_part('month'::text, all_unions.date)), (date_part('week'::text, all_unions.date)), all_unions.country,
         all_unions.country_code, all_unions.revenue_type, all_unions.project_site, all_unions.manager_name,
         all_unions.cost_type;

alter table v_finance_report
    owner to ono;

grant select on v_finance_report to readonly;

