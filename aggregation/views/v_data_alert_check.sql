create view aggregation.v_data_alert_check
            (date_diff, date, table_name, scheme_name, metric_name, country_id, country_name_eng, yesterday_metric) as
SELECT source.date_diff,
       fn_get_timestamp_from_date_diff(source.date_diff)                AS date,
       source.table_name,
       source.scheme_name,
       source.metric_name,
       source.country_id,
       COALESCE(countries.name_country_eng, 'Other'::character varying) AS country_name_eng,
       source.yesterday_metric
FROM (WITH last7date_diff AS (SELECT fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 8 AS date_d,
                                     fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 2 AS date_d1),
           cluster_affiliate AS (SELECT c.id                                   AS id_country,
                                        lower(c.alpha_2::text)                 AS country_code,
                                        "substring"(s.server_code::text, 1, 2) AS cluster_code
                                 FROM dimension.countries c
                                          LEFT JOIN dimension.product_database d ON c.product_database_id = d.id
                                          LEFT JOIN dimension.product_database_server s ON s.id = d.server_id)
      SELECT 'email_agg_daily'::text                                        AS table_name,
             'email'::text                                                  AS scheme_name,
             'jdp_away_revenue'::text                                       AS metric_name,
             ead.country_id,
             fn_get_date_diff(ead.action_date::timestamp without time zone) AS date_diff,
             sum(ead.jdp_away_revenue)                                      AS yesterday_metric
      FROM email.email_agg_daily ead
      WHERE fn_get_date_diff(ead.action_date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                FROM last7date_diff))
        AND fn_get_date_diff(ead.action_date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                FROM last7date_diff))
      GROUP BY ead.country_id, (fn_get_date_diff(ead.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'adv_revenue_by_placement_and_src'::text AS table_name,
             'imp'::text                              AS scheme_name,
             'revenue_usd'::text                      AS metric_name,
             eaa.country                              AS country_id,
             eaa.date_diff,
             sum(eaa.revenue_usd)                     AS yesterday_metric
      FROM imp.adv_revenue_by_placement_and_src eaa
      WHERE eaa.date_diff >= ((SELECT last7date_diff.date_d
                               FROM last7date_diff))
        AND eaa.date_diff <= ((SELECT last7date_diff.date_d1
                               FROM last7date_diff))
      GROUP BY eaa.country, eaa.date_diff
      UNION ALL
      SELECT 'auction_click_statistic'::text                           AS table_name,
             'imp_statistic'::text                                     AS scheme_name,
             'revenue_usd'::text                                       AS metric_name,
             acs_1.country_id,
             fn_get_date_diff(acs_1.date::timestamp without time zone) AS date_diff,
             sum(acs_1.total_value + COALESCE(- (acs_1.total_value -
                                                 (acs_1.click_count - acs_1.test_count - acs_1.to_jdp_count)::numeric *
                                                 (acs_1.total_value /
                                                  NULLIF(acs_1.click_count - acs_1.to_jdp_count, 0)::numeric)),
                                              0::numeric))             AS yesterday_metric
      FROM imp_statistic.auction_click_statistic acs_1
      WHERE fn_get_date_diff(acs_1.date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                           FROM last7date_diff))
        AND fn_get_date_diff(acs_1.date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                           FROM last7date_diff))
      GROUP BY acs_1.country_id, (fn_get_date_diff(acs_1.date::timestamp without time zone))
      UNION ALL
      SELECT 'adsense_afc'::text                                                    AS table_name,
             'imp_statistic'::text                                                  AS scheme_name,
             'estimated_income_usd'::text                                           AS metric_name,
             countries_1.id                                                         AS country_id,
             fn_get_date_diff(adsense_afc.create_date::timestamp without time zone) AS date_diff,
             sum(adsense_afc.estimated_income_usd)                                  AS yesterday_metric
      FROM imp_statistic.adsense_afc
               LEFT JOIN ono.dic_countries_adsense ON "left"(
                                                              CASE
                                                                  WHEN adsense_afc.site_address::text = 'jooble.org'::text
                                                                      THEN 'us.jooble.org'::character varying
                                                                  WHEN adsense_afc.site_address::text = 'ja.jooble.org'::text
                                                                      THEN 'jp.jooble.org'::character varying
                                                                  ELSE adsense_afc.site_address
                                                                  END::text, 2) =
                                                      dic_countries_adsense.country_as_in_reports
               LEFT JOIN dimension.countries countries_1
                         ON lower(dic_countries_adsense.two_digits) = lower(countries_1.alpha_2::text)
      WHERE fn_get_date_diff(adsense_afc.create_date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                        FROM last7date_diff))
        AND fn_get_date_diff(adsense_afc.create_date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                        FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(adsense_afc.create_date::timestamp without time zone))
      UNION ALL
      SELECT 'adsense_afs'::text                                                    AS table_name,
             'imp_statistic'::text                                                  AS scheme_name,
             'estimated_income_usd'::text                                           AS metric_name,
             COALESCE(countries_1.id, 0)                                            AS country_id,
             fn_get_date_diff(adsense_afs.create_date::timestamp without time zone) AS date_diff,
             sum(adsense_afs.estimated_income_usd)                                  AS yesterday_metric
      FROM imp_statistic.adsense_afs
               LEFT JOIN ono.dic_countries_adsense
                         ON adsense_afs.country::text = dic_countries_adsense.country_as_in_reports
               LEFT JOIN dimension.countries countries_1
                         ON lower(dic_countries_adsense.two_digits) = lower(countries_1.alpha_2::text)
      WHERE fn_get_date_diff(adsense_afs.create_date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                        FROM last7date_diff))
        AND fn_get_date_diff(adsense_afs.create_date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                        FROM last7date_diff))
      GROUP BY (COALESCE(countries_1.id, 0)), (fn_get_date_diff(adsense_afs.create_date::timestamp without time zone))
      UNION ALL
      SELECT 'search_client_conversion'::text                                                    AS table_name,
             'aggregation'::text                                                                 AS scheme_name,
             'away_revenue'::text                                                                AS metric_name,
             search_client_conversion.country                                                    AS country_id,
             fn_get_date_diff(search_client_conversion.search_date::timestamp without time zone) AS date_diff,
             sum(search_client_conversion.away_revenue)                                          AS yesterday_metric
      FROM aggregation.search_client_conversion
      WHERE fn_get_date_diff(search_client_conversion.search_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(search_client_conversion.search_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY search_client_conversion.country,
               (fn_get_date_diff(search_client_conversion.search_date::timestamp without time zone))
      UNION ALL
      SELECT 'funnel_agg'::text                       AS table_name,
             'aggregation'::text                      AS scheme_name,
             'away_revenue_usd'::text                 AS metric_name,
             funnel_agg.country_id,
             fn_get_date_diff(funnel_agg.action_date) AS date_diff,
             sum(funnel_agg.away_revenue_usd)         AS yesterday_metric
      FROM aggregation.funnel_agg
      WHERE fn_get_date_diff(funnel_agg.action_date) >= ((SELECT last7date_diff.date_d
                                                          FROM last7date_diff))
        AND fn_get_date_diff(funnel_agg.action_date) <= ((SELECT last7date_diff.date_d1
                                                          FROM last7date_diff))
      GROUP BY funnel_agg.country_id, (fn_get_date_diff(funnel_agg.action_date))
      UNION ALL
      SELECT 'pyramid_agg'::text                                                    AS table_name,
             'aggregation'::text                                                    AS scheme_name,
             'aways'::text                                                          AS metric_name,
             pyramid_agg.country_id,
             fn_get_date_diff(pyramid_agg.action_date::timestamp without time zone) AS date_diff,
             sum(pyramid_agg.metric_cnt)                                            AS yesterday_metric
      FROM aggregation.pyramid_agg
      WHERE fn_get_date_diff(pyramid_agg.action_date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                        FROM last7date_diff))
        AND fn_get_date_diff(pyramid_agg.action_date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                        FROM last7date_diff))
        AND pyramid_agg.metric_name::text = 'Aways'::text
      GROUP BY pyramid_agg.country_id, (fn_get_date_diff(pyramid_agg.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'email_abtest_agg'::text                                                    AS table_name,
             'aggregation'::text                                                         AS scheme_name,
             'metric_cnt'::text                                                          AS metric_name,
             email_abtest_agg.country_id,
             fn_get_date_diff(email_abtest_agg.action_date::timestamp without time zone) AS date_diff,
             sum(email_abtest_agg.metric_cnt)                                            AS yesterday_metric
      FROM aggregation.email_abtest_agg
      WHERE fn_get_date_diff(email_abtest_agg.action_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(email_abtest_agg.action_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY email_abtest_agg.country_id,
               (fn_get_date_diff(email_abtest_agg.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'jdp_away_clicks_agg'::text          AS table_name,
             'aggregation'::text                  AS scheme_name,
             'revenue_usd'::text                  AS metric_name,
             jdp_away_clicks_agg.country_id,
             jdp_away_clicks_agg.action_datediff,
             sum(jdp_away_clicks_agg.revenue_usd) AS yesterday_metric
      FROM aggregation.jdp_away_clicks_agg
      WHERE jdp_away_clicks_agg.action_datediff >= ((SELECT last7date_diff.date_d
                                                     FROM last7date_diff))
        AND jdp_away_clicks_agg.action_datediff <= ((SELECT last7date_diff.date_d1
                                                     FROM last7date_diff))
      GROUP BY jdp_away_clicks_agg.country_id, jdp_away_clicks_agg.action_datediff
      UNION ALL
      SELECT 'budget_revenue_daily_agg'::text                                                    AS table_name,
             'aggregation'::text                                                                 AS scheme_name,
             'revenue_usd'::text                                                                 AS metric_name,
             budget_revenue_daily_agg.country_id,
             fn_get_date_diff(budget_revenue_daily_agg.action_date::timestamp without time zone) AS date_diff,
             sum(budget_revenue_daily_agg.revenue_usd)                                           AS yesterday_metric
      FROM aggregation.budget_revenue_daily_agg
      WHERE fn_get_date_diff(budget_revenue_daily_agg.action_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(budget_revenue_daily_agg.action_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY budget_revenue_daily_agg.country_id,
               (fn_get_date_diff(budget_revenue_daily_agg.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'adsense_version_daily'::text          AS table_name,
             'aggregation'::text                    AS scheme_name,
             'revenue_eur'::text                    AS metric_name,
             adsense_version_daily.country_id,
             adsense_version_daily.action_datediff,
             sum(adsense_version_daily.revenue_eur) AS yesterday_metric
      FROM aggregation.adsense_version_daily
      WHERE adsense_version_daily.action_datediff >= ((SELECT last7date_diff.date_d
                                                       FROM last7date_diff))
        AND adsense_version_daily.action_datediff <= ((SELECT last7date_diff.date_d1
                                                       FROM last7date_diff))
      GROUP BY adsense_version_daily.country_id, adsense_version_daily.action_datediff
      UNION ALL
      SELECT 'project_conversions_daily'::text                                                     AS table_name,
             'aggregation'::text                                                                   AS scheme_name,
             'away_revenue'::text                                                                  AS metric_name,
             project_conversions_daily.country_id,
             fn_get_date_diff(project_conversions_daily.session_date::timestamp without time zone) AS date_diff,
             sum(project_conversions_daily.away_revenue)                                           AS yesterday_metric
      FROM aggregation.project_conversions_daily
      WHERE fn_get_date_diff(project_conversions_daily.session_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(project_conversions_daily.session_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY project_conversions_daily.country_id,
               (fn_get_date_diff(project_conversions_daily.session_date::timestamp without time zone))
      UNION ALL
      SELECT 'yandex_2018'::text                                             AS table_name,
             'imp_statistic'::text                                           AS scheme_name,
             'impressions'::text                                             AS metric_name,
             0                                                               AS country_id,
             fn_get_date_diff(yandex_2018.date::timestamp without time zone) AS date_diff,
             sum(yandex_2018.impressions)                                    AS yesterday_metric
      FROM imp_statistic.yandex_2018
      WHERE fn_get_date_diff(yandex_2018.date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                 FROM last7date_diff))
        AND fn_get_date_diff(yandex_2018.date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                 FROM last7date_diff))
      GROUP BY 0::integer, (fn_get_date_diff(yandex_2018.date::timestamp without time zone))
      UNION ALL
      SELECT 'facebook_2018'::text                                                   AS table_name,
             'imp_statistic'::text                                                   AS scheme_name,
             'impressions'::text                                                     AS metric_name,
             0                                                                       AS country_id,
             fn_get_date_diff(facebook_2018.date_start::timestamp without time zone) AS date_diff,
             sum(facebook_2018.impressions)                                          AS yesterday_metric
      FROM imp_statistic.facebook_2018
      WHERE fn_get_date_diff(facebook_2018.date_start::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                         FROM last7date_diff))
        AND fn_get_date_diff(facebook_2018.date_start::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                         FROM last7date_diff))
      GROUP BY 0::integer, (fn_get_date_diff(facebook_2018.date_start::timestamp without time zone))
      UNION ALL
      SELECT 'adwords'::text                                            AS table_name,
             'imp_statistic'::text                                      AS scheme_name,
             'impressions'::text                                        AS metric_name,
             0                                                          AS country_id,
             fn_get_date_diff(adwords.day::timestamp without time zone) AS date_diff,
             sum(adwords.impressions)                                   AS yesterday_metric
      FROM imp_statistic.adwords
      WHERE fn_get_date_diff(adwords.day::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                            FROM last7date_diff))
        AND fn_get_date_diff(adwords.day::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                            FROM last7date_diff))
      GROUP BY 0::integer, (fn_get_date_diff(adwords.day::timestamp without time zone))
      UNION ALL
      SELECT 'session_abtest_agg'::text           AS table_name,
             'aggregation'::text                  AS scheme_name,
             'revenue_usd'::text                  AS metric_name,
             session_abtest_agg.country_id,
             session_abtest_agg.action_datediff,
             sum(session_abtest_agg.metric_value) AS yesterday_metric
      FROM aggregation.session_abtest_agg
      WHERE session_abtest_agg.action_datediff >= ((SELECT last7date_diff.date_d
                                                    FROM last7date_diff))
        AND session_abtest_agg.action_datediff <= ((SELECT last7date_diff.date_d1
                                                    FROM last7date_diff))
        AND session_abtest_agg.metric_name::text = 'revenue'::text
        AND session_abtest_agg.attribute_value = 7
      GROUP BY session_abtest_agg.country_id, session_abtest_agg.action_datediff
      UNION ALL
      SELECT 'statistics_daily_agg'::text                 AS table_name,
             'affiliate'::text                              AS scheme_name,
             'external_click_cnt'::text                       AS metric_name,
             CASE
                 WHEN cluster_affiliate.cluster_code = 'us'::text THEN 6
                 ELSE 20
                 END                                          AS country_id,
             statistics_daily_agg.date_diff,
             sum(statistics_daily_agg.external_click_cnt) AS yesterday_metric
      FROM affiliate.statistics_daily_agg
               LEFT JOIN cluster_affiliate ON cluster_affiliate.id_country = statistics_daily_agg.id_country
      WHERE statistics_daily_agg.date_diff >= ((SELECT last7date_diff.date_d
                                                    FROM last7date_diff))
        AND statistics_daily_agg.date_diff <= ((SELECT last7date_diff.date_d1
                                                    FROM last7date_diff))
      GROUP BY (
                   CASE
                       WHEN cluster_affiliate.cluster_code = 'us'::text THEN 6
                       ELSE 20
                       END), statistics_daily_agg.date_diff
      UNION ALL
      SELECT 'jobs_stat_daily'::text                                             AS table_name,
             'aggregation'::text                                                 AS scheme_name,
             'organic_job_count'::text                                           AS metric_name,
             jobs_stat_daily.id_country                                          AS country_id,
             fn_get_date_diff(jobs_stat_daily.date::timestamp without time zone) AS date_diff,
             sum(jobs_stat_daily.organic_job_count)                              AS yesterday_metric
      FROM aggregation.jobs_stat_daily
      WHERE fn_get_date_diff(jobs_stat_daily.date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                     FROM last7date_diff))
        AND fn_get_date_diff(jobs_stat_daily.date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                     FROM last7date_diff))
      GROUP BY jobs_stat_daily.id_country, (fn_get_date_diff(jobs_stat_daily.date::timestamp without time zone))
      UNION ALL
      SELECT 'impression_statistic'::text                AS table_name,
             'imp_statistic'::text                       AS scheme_name,
             'impressions_count'::text                   AS metric_name,
             impression_statistic.country                AS country_id,
             impression_statistic.date_diff,
             sum(impression_statistic.impressions_count) AS yesterday_metric
      FROM imp_statistic.impression_statistic
      WHERE impression_statistic.date_diff >= ((SELECT last7date_diff.date_d
                                                FROM last7date_diff))
        AND impression_statistic.date_diff <= ((SELECT last7date_diff.date_d1
                                                FROM last7date_diff))
      GROUP BY impression_statistic.country, impression_statistic.date_diff
      UNION ALL
      SELECT 'action_agg'::text          AS table_name,
             'aggregation'::text         AS scheme_name,
             'session_cnt'::text         AS metric_name,
             action_agg.country_id,
             action_agg.date_diff,
             sum(action_agg.session_cnt) AS yesterday_metric
      FROM aggregation.action_agg
      WHERE action_agg.date_diff >= ((SELECT last7date_diff.date_d
                                      FROM last7date_diff))
        AND action_agg.date_diff <= ((SELECT last7date_diff.date_d1
                                      FROM last7date_diff))
      GROUP BY action_agg.country_id, action_agg.date_diff
      UNION ALL
      SELECT 'statistics_daily_raw'::text               AS table_name,
             'affiliate'::text                       AS scheme_name,
             'partner_cost_usd'::text                  AS metric_name,
             CASE
                 WHEN cluster_affiliate.cluster_code = 'us'::text THEN 6
                 ELSE 20
                 END                                   AS country_id,
             statistics_daily_raw.date_diff,
             sum(statistics_daily_raw.partner_cost_usd) AS yesterday_metric
      FROM affiliate.statistics_daily_raw
               LEFT JOIN cluster_affiliate ON cluster_affiliate.id_country = statistics_daily_raw.id_country
      WHERE statistics_daily_raw.date_diff >= ((SELECT last7date_diff.date_d
                                               FROM last7date_diff))
        AND statistics_daily_raw.date_diff <= ((SELECT last7date_diff.date_d1
                                               FROM last7date_diff))
      GROUP BY (
                   CASE
                       WHEN cluster_affiliate.cluster_code = 'us'::text THEN 6
                       ELSE 20
                       END), statistics_daily_raw.date_diff
      UNION ALL
      SELECT 'partner_daily_snapshot'::text               AS table_name,
             'affiliate'::text                                    AS scheme_name,
             'feed_min_cpc_usd'::text                               AS metric_name,
             CASE
                 WHEN cluster_affiliate.cluster_code = 'us'::text THEN 6
                 ELSE 20
                 END                                                AS country_id,
             partner_daily_snapshot.date_diff,
             sum(partner_daily_snapshot.feed_min_cpc_usd) AS yesterday_metric
      FROM affiliate.partner_daily_snapshot
               LEFT JOIN cluster_affiliate ON cluster_affiliate.id_country = partner_daily_snapshot.id_country
      WHERE partner_daily_snapshot.date_diff >= ((SELECT last7date_diff.date_d
                                                            FROM last7date_diff))
        AND partner_daily_snapshot.date_diff <= ((SELECT last7date_diff.date_d1
                                                            FROM last7date_diff))
      GROUP BY (
                   CASE
                       WHEN cluster_affiliate.cluster_code = 'us'::text THEN 6
                       ELSE 20
                       END), partner_daily_snapshot.date_diff
      UNION ALL
      SELECT 'email_metric_daily'::text       AS table_name,
             'aggregation'::text              AS scheme_name,
             'sent_msg'::text                 AS metric_name,
             email_metric_daily.country_id,
             email_metric_daily.date_diff,
             sum(email_metric_daily.sent_msg) AS yesterday_metric
      FROM archive.email_metric_daily
      WHERE email_metric_daily.date_diff >= ((SELECT last7date_diff.date_d
                                              FROM last7date_diff))
        AND email_metric_daily.date_diff <= ((SELECT last7date_diff.date_d1
                                              FROM last7date_diff))
      GROUP BY email_metric_daily.country_id, email_metric_daily.date_diff
      UNION ALL
      SELECT 'finance_report'::text                                             AS table_name,
             'aggregation'::text                                                AS scheme_name,
             'revenue_usd'::text                                                AS metric_name,
             countries_1.id                                                     AS country_id,
             fn_get_date_diff(finance_report.date::timestamp without time zone) AS date_diff,
             sum(finance_report.revenue_usd)                                    AS yesterday_metric
      FROM aggregation.finance_report
               LEFT JOIN dimension.countries countries_1 ON finance_report.country::text = countries_1.name_country_eng::text
      WHERE fn_get_date_diff(finance_report.date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                    FROM last7date_diff))
        AND fn_get_date_diff(finance_report.date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                    FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(finance_report.date::timestamp without time zone))
      UNION ALL
      SELECT 'session_daily_agg'::text                AS table_name,
             'aggregation'::text                      AS scheme_name,
             'total_session_cnt'::text                AS metric_name,
             session_daily_agg.country_id,
             session_daily_agg.action_datediff,
             sum(session_daily_agg.total_session_cnt) AS yesterday_metric
      FROM aggregation.session_daily_agg
      WHERE session_daily_agg.action_datediff >= ((SELECT last7date_diff.date_d
                                                   FROM last7date_diff))
        AND session_daily_agg.action_datediff <= ((SELECT last7date_diff.date_d1
                                                   FROM last7date_diff))
      GROUP BY session_daily_agg.country_id, session_daily_agg.action_datediff
      UNION ALL
      SELECT 'adsense_afc'::text                                                    AS table_name,
             'imp_api'::text                                                        AS scheme_name,
             'estimated_earnings_usd'::text                                         AS metric_name,
             countries_1.id                                                         AS country_id,
             fn_get_date_diff(adsense_afc.action_date::timestamp without time zone) AS date_diff,
             sum(adsense_afc.estimated_earnings_usd)                                AS yesterday_metric
      FROM imp_api.adsense_afc
               LEFT JOIN ono.dic_countries_adsense ON "left"(
                                                              CASE
                                                                  WHEN adsense_afc.domain_code::text = 'jooble.org'::text
                                                                      THEN 'us.jooble.org'::character varying
                                                                  WHEN adsense_afc.domain_code::text = 'ja.jooble.org'::text
                                                                      THEN 'jp.jooble.org'::character varying
                                                                  ELSE adsense_afc.domain_code
                                                                  END::text, 2) =
                                                      dic_countries_adsense.country_as_in_reports
               LEFT JOIN dimension.countries countries_1
                         ON lower(dic_countries_adsense.two_digits) = lower(countries_1.alpha_2::text)
      WHERE fn_get_date_diff(adsense_afc.action_date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                        FROM last7date_diff))
        AND fn_get_date_diff(adsense_afc.action_date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                        FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(adsense_afc.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'adsense_afs'::text                                                    AS table_name,
             'imp_api'::text                                                        AS scheme_name,
             'estimated_earnings_usd'::text                                         AS metric_name,
             countries_1.id                                                         AS country_id,
             fn_get_date_diff(adsense_afs.action_date::timestamp without time zone) AS date_diff,
             sum(adsense_afs.estimated_earnings_usd)                                AS yesterday_metric
      FROM imp_api.adsense_afs
               LEFT JOIN dimension.countries countries_1
                         ON adsense_afs.country_name::text = countries_1.name_country_eng::text
      WHERE fn_get_date_diff(adsense_afs.action_date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                        FROM last7date_diff))
        AND fn_get_date_diff(adsense_afs.action_date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                        FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(adsense_afs.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'adsense_custom_channels_revenue'::text                                                    AS table_name,
             'imp_api'::text                                                                            AS scheme_name,
             'estimated_earnings_usd'::text                                                             AS metric_name,
             countries_1.id                                                                             AS country_id,
             fn_get_date_diff(adsense_custom_channels_revenue.action_date::timestamp without time zone) AS date_diff,
             sum(adsense_custom_channels_revenue.estimated_earnings_usd)                                AS yesterday_metric
      FROM imp_api.adsense_custom_channels_revenue
               LEFT JOIN dimension.countries countries_1
                         ON adsense_custom_channels_revenue.country_name::text = countries_1.name_country_eng::text
      WHERE fn_get_date_diff(adsense_custom_channels_revenue.action_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(adsense_custom_channels_revenue.action_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY countries_1.id,
               (fn_get_date_diff(adsense_custom_channels_revenue.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'adsense_product_revenue'::text                                                    AS table_name,
             'imp_api'::text                                                                    AS scheme_name,
             'estimated_earnings_usd'::text                                                     AS metric_name,
             countries_1.id                                                                     AS country_id,
             fn_get_date_diff(adsense_product_revenue.action_date::timestamp without time zone) AS date_diff,
             sum(adsense_product_revenue.estimated_earnings_usd)                                AS yesterday_metric
      FROM imp_api.adsense_product_revenue
               LEFT JOIN dimension.countries countries_1
                         ON adsense_product_revenue.country_name::text = countries_1.name_country_eng::text
      WHERE fn_get_date_diff(adsense_product_revenue.action_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(adsense_product_revenue.action_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(adsense_product_revenue.action_date::timestamp without time zone))
      UNION ALL
      SELECT 'paid_traffic_metrics_agg'::text                                             AS table_name,
             'aggregation'::text                                                          AS scheme_name,
             'clicks'::text                                                               AS metric_name,
             countries_1.id                                                               AS country_id,
             fn_get_date_diff(paid_traffic_metrics_agg.date::timestamp without time zone) AS date_diff,
             sum(paid_traffic_metrics_agg.clicks)                                         AS yesterday_metric
      FROM aggregation.paid_traffic_metrics_agg
               LEFT JOIN dimension.countries countries_1
                         ON paid_traffic_metrics_agg.country::text = lower(countries_1.alpha_2::text)
      WHERE fn_get_date_diff(paid_traffic_metrics_agg.date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(paid_traffic_metrics_agg.date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(paid_traffic_metrics_agg.date::timestamp without time zone))
      UNION ALL
      SELECT 'account_revenue'::text                                                     AS table_name,
             'aggregation'::text                                                         AS scheme_name,
             'account_revenue'::text                                                     AS metric_name,
             account_revenue.country_id,
             fn_get_date_diff(account_revenue.revenue_date::timestamp without time zone) AS date_diff,
             sum(account_revenue.account_revenue)                                        AS yesterday_metric
      FROM aggregation.account_revenue
      WHERE fn_get_date_diff(account_revenue.revenue_date::timestamp without time zone) >=
            ((SELECT last7date_diff.date_d
              FROM last7date_diff))
        AND fn_get_date_diff(account_revenue.revenue_date::timestamp without time zone) <=
            ((SELECT last7date_diff.date_d1
              FROM last7date_diff))
      GROUP BY account_revenue.country_id, (fn_get_date_diff(account_revenue.revenue_date::timestamp without time zone))
      UNION ALL
      SELECT 'conversions'::text                                             AS table_name,
             'imp_statistic'::text                                           AS scheme_name,
             'conversions'::text                                             AS metric_name,
             countries_1.id                                                  AS country_id,
             fn_get_date_diff(conversions.date::timestamp without time zone) AS date_diff,
             sum(conversions.conversions)                                    AS yesterday_metric
      FROM imp_statistic.conversions
               LEFT JOIN dimension.countries countries_1 ON conversions.country::text = countries_1.alpha_2::text
      WHERE fn_get_date_diff(conversions.date::timestamp without time zone) >= ((SELECT last7date_diff.date_d
                                                                                 FROM last7date_diff))
        AND fn_get_date_diff(conversions.date::timestamp without time zone) <= ((SELECT last7date_diff.date_d1
                                                                                 FROM last7date_diff))
      GROUP BY countries_1.id, (fn_get_date_diff(conversions.date::timestamp without time zone))) source
         LEFT JOIN dimension.countries ON source.country_id = countries.id
WHERE COALESCE(countries.name_country_eng, 'Other'::character varying)::text <> 'Russia'::text
  AND COALESCE(countries.name_country_eng, 'Other'::character varying)::text <> 'Belarus'::text;

alter table aggregation.v_data_alert_check
    owner to ono;

grant select on aggregation.v_data_alert_check to readonly;

grant select on aggregation.v_data_alert_check to tma;

grant select on aggregation.v_data_alert_check to ypr;

grant select on aggregation.v_data_alert_check to vnov;

grant select on aggregation.v_data_alert_check to readonly_aggregation;

grant select on aggregation.v_data_alert_check to "pavlo.kvasnii";

