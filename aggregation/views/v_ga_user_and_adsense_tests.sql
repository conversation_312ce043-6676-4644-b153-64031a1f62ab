create view aggregation.v_ga_user_and_adsense_tests
            (action_date, test_groups_session, advert_version, sessions, avg_session_duration, bounce_rate,
             country_domain, country_id, country, union_group)
as
SELECT ga_user_and_adsense_tests.action_date,
       ga_user_and_adsense_tests.test_groups_session,
       ga_user_and_adsense_tests.advert_version,
       ga_user_and_adsense_tests.sessions,
       ga_user_and_adsense_tests.avg_session_duration,
       ga_user_and_adsense_tests.bounce_rate,
       ga_user_and_adsense_tests.country_domain,
       ga_user_and_adsense_tests.country_id,
       countries.name_country_eng AS country,
       'union 1'::text            AS union_group
FROM imp_api.ga_user_and_adsense_tests
         JOIN dimension.countries ON ga_user_and_adsense_tests.country_id = countries.id
WHERE ga_user_and_adsense_tests.action_date < '2023-07-01'
UNION ALL
SELECT ga_user_and_adsense_tests.action_date,
       ga_user_and_adsense_tests.test_groups_session,
       ga_user_and_adsense_tests.advert_version,
       ga_user_and_adsense_tests.sessions,
       ga_user_and_adsense_tests.avg_session_duration,
       ga_user_and_adsense_tests.bounce_rate,
       ga_user_and_adsense_tests.country_domain,
       ga_user_and_adsense_tests.country_id,
       countries.name_country_eng AS country,
       'union 2'::text            AS union_group
FROM imp_api.ga_user_and_adsense_tests
         JOIN dimension.countries ON ga_user_and_adsense_tests.country_id = countries.id
WHERE ga_user_and_adsense_tests.action_date < '2023-07-01'
UNION ALL
SELECT ga4_user_and_adsense_tests.action_date,
       ga4_user_and_adsense_tests.test_groups_session,
       ga4_user_and_adsense_tests.advert_version,
       ga4_user_and_adsense_tests.sessions,
       ga4_user_and_adsense_tests.average_session_duration as avg_session_duration,
       ga4_user_and_adsense_tests.bounce_rate,
       ga4_user_and_adsense_tests.country_domain,
       ga4_user_and_adsense_tests.country_id,
       countries.name_country_eng AS country,
       'union 1'::text            AS union_group
FROM imp_api.ga4_user_and_adsense_tests
         JOIN dimension.countries ON ga4_user_and_adsense_tests.country_id = countries.id
WHERE ga4_user_and_adsense_tests.action_date >= '2023-07-01'
UNION ALL
SELECT ga4_user_and_adsense_tests.action_date,
       ga4_user_and_adsense_tests.test_groups_session,
       ga4_user_and_adsense_tests.advert_version,
       ga4_user_and_adsense_tests.sessions,
       ga4_user_and_adsense_tests.average_session_duration as avg_session_duration,
       ga4_user_and_adsense_tests.bounce_rate,
       ga4_user_and_adsense_tests.country_domain,
       ga4_user_and_adsense_tests.country_id,
       countries.name_country_eng AS country,
       'union 2'::text            AS union_group
FROM imp_api.ga4_user_and_adsense_tests
         JOIN dimension.countries ON ga4_user_and_adsense_tests.country_id = countries.id
WHERE ga4_user_and_adsense_tests.action_date >= '2023-07-01'
;
alter table aggregation.v_ga_user_and_adsense_tests
    owner to ono;

grant select on aggregation.v_ga_user_and_adsense_tests to readonly;

grant delete, insert, select, update on aggregation.v_ga_user_and_adsense_tests to write_ono;

grant select on aggregation.v_ga_user_and_adsense_tests to readonly_aggregation;

grant select on aggregation.v_ga_user_and_adsense_tests to "pavlo.kvasnii";

