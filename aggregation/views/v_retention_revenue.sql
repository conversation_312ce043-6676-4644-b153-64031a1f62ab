create or replace view aggregation.v_retention_revenue as
with account_revenue_cte as (select distinct country_id,
                                             revenue_date::date              as revenue_date,
                                             account_date::date              as account_date,
                                             case
                                                 when revenue_date::date - account_date::date between 0 and 3 then 1
                                                 when revenue_date::date - account_date::date between 4 and 7 then 2
                                                 when revenue_date::date - account_date::date between 8 and 14 then 3
                                                 when revenue_date::date - account_date::date between 15 and 21 then 4
                                                 when revenue_date::date - account_date::date between 22 and 28 then 5
                                                 else 0 /*old accounts*/ end as job_seeker_lifecycle_stage,
                                             source                          as registration_source_id,
                                             id_traf_src                     as traffic_source_id,
                                             device_type_id,
                                             email_revenue,
                                             account_revenue,
                                             away_cnt,
                                             revenue_account_cnt,
                                             revenue_verified_account_cnt
                             from aggregation.account_revenue
                             where revenue_date is not null
                               and revenue_date >= '2023-01-01'),
     account_revenue_agg as (select country_id,
                                    revenue_date,
                                    job_seeker_lifecycle_stage,
                                    registration_source_id,
                                    traffic_source_id,
                                    device_type_id,
                                    sum(email_revenue)                as email_revenue,
                                    sum(account_revenue)              as account_revenue,
                                    sum(away_cnt)                     as away_cnt,
                                    sum(revenue_account_cnt)          as revenue_account_cnt,
                                    sum(revenue_verified_account_cnt) as revenue_verified_account_cnt
                             from account_revenue_cte
                             group by country_id,
                                      revenue_date,
                                      job_seeker_lifecycle_stage,
                                      registration_source_id,
                                      traffic_source_id,
                                      device_type_id)

select account_revenue_agg.country_id,
       countries.name_country_eng                                                       AS country_name,
       revenue_date,
       job_seeker_lifecycle_stage,
       registration_source_id,
       btrim(COALESCE(auth_source.source_name, 'other'::text::character varying)::text) AS registration_source_name,
       traffic_source_id,
       COALESCE(u_traffic_source.name, 'Undefined'::character varying)                  AS account_traffic_source_name,
       COALESCE(u_traffic_source.channel, 'Undefined'::character varying)               AS account_traffic_channel_name,
       device_type_id,
       email_revenue,
       account_revenue,
       away_cnt,
       revenue_account_cnt,
       revenue_verified_account_cnt
from account_revenue_agg
LEFT JOIN dimension.countries ON account_revenue_agg.country_id = countries.id
LEFT JOIN dimension.auth_source ON account_revenue_agg.registration_source_id = auth_source.id
LEFT JOIN dimension.u_traffic_source
          ON account_revenue_agg.country_id = u_traffic_source.country AND
             u_traffic_source.id = account_revenue_agg.traffic_source_id
;
