--code owner: @<PERSON><PERSON><PERSON><PERSON> in Slack

create or replace view aggregation.v_abtest_new_account_email_revenue_estimation
            (test_id, is_significant, country, metric_name, iteration, compared_group_test, compared_total,
             group_0_5_total, diff_value, cohort, running_total_percent, avg_revenue)


as
WITH email_revenue_by_country_3m                 --countr sum email revenue for countries
         AS (SELECT date_trunc('month'::text, fn_get_timestamp_from_date_diff(revenue_data.date_diff)) AS month_revenue,
                    countries.name_country_eng                                                         AS country,
                    sum(revenue_data.revenue_usd)                                                      AS revenue
             FROM aggregation.adv_revenue_by_placement_and_src_analytics revenue_data
                      JOIN dimension.countries ON revenue_data.country_id = countries.id
             WHERE 1 = 1
               AND ((revenue_data.placement::text = ANY
                     (ARRAY ['alertview'::character varying, 'other letter types'::character varying]::text[])) OR
                    "position"(revenue_data.placement::text, 'letter type '::text) > 0)
               AND date_trunc('month'::text,
                              date_trunc('month'::text, fn_get_timestamp_from_date_diff(revenue_data.date_diff))) <=
                   date_trunc('month'::text, CURRENT_DATE - '1 mon'::interval)
               AND date_trunc('month'::text,
                              date_trunc('month'::text, fn_get_timestamp_from_date_diff(revenue_data.date_diff))) >=
                   date_trunc('month'::text, CURRENT_DATE - '3 mons'::interval)
             GROUP BY (date_trunc('month'::text, fn_get_timestamp_from_date_diff(revenue_data.date_diff))),
                      countries.name_country_eng),
     
     avg_email_revenue_by_country_3m                --3m avg                                                                       
              AS (SELECT email_revenue_by_country_3m.country,                                                                                                    
                                                avg(email_revenue_by_country_3m.revenue) AS avg_revenue
                                         FROM email_revenue_by_country_3m
                                         GROUP BY email_revenue_by_country_3m.country),
     
     
     cohorts_distr 
              AS (SELECT v_account_revenue.country,                          --counting prec of revenue generated by specific account creation date cohort
                              date_trunc('month'::text,
                                         v_account_revenue.revenue_date::timestamp with time zone)::date AS revenue_date_month,
                              CASE
                                  WHEN date_trunc('month'::text,
                                                  v_account_revenue.revenue_date::timestamp with time zone) =
                                       date_trunc('month'::text,
                                                  v_account_revenue.account_date::timestamp with time zone)
                                      THEN 0::double precision
                                  WHEN ((date_part('year'::text, v_account_revenue.revenue_date) -
                                         date_part('year'::text, v_account_revenue.account_date)) *
                                        12::double precision +
                                        (date_part('month'::text, v_account_revenue.revenue_date) -
                                         date_part('month'::text, v_account_revenue.account_date))) >=
                                       1::double precision AND
                                       ((date_part('year'::text, v_account_revenue.revenue_date) -
                                         date_part('year'::text, v_account_revenue.account_date)) *
                                        12::double precision +
                                        (date_part('month'::text, v_account_revenue.revenue_date) -
                                         date_part('month'::text, v_account_revenue.account_date))) <=
                                       11::double precision THEN
                                              (date_part('year'::text, v_account_revenue.revenue_date) -
                                               date_part('year'::text, v_account_revenue.account_date)) *
                                              12::double precision +
                                              (date_part('month'::text, v_account_revenue.revenue_date) -
                                               date_part('month'::text, v_account_revenue.account_date))
                                  WHEN ((date_part('year'::text, v_account_revenue.revenue_date) -
                                         date_part('year'::text, v_account_revenue.account_date)) *
                                        12::double precision +
                                        (date_part('month'::text, v_account_revenue.revenue_date) -
                                         date_part('month'::text, v_account_revenue.account_date))) >
                                       11::double precision THEN 13::double precision
                                  ELSE NULL::double precision
                                  END                                                                    AS cohort,
                              sum(v_account_revenue.email_revenue)                                       AS email_revenue
                       FROM aggregation.v_account_revenue
                       WHERE date_trunc('month'::text, v_account_revenue.revenue_date::timestamp with time zone) =
                             date_trunc('month'::text, CURRENT_DATE - '1 mon'::interval)
                       GROUP BY v_account_revenue.country, (date_trunc('month'::text,
                                                                       v_account_revenue.revenue_date::timestamp with time zone)::date),
                                (
                                    CASE
                                        WHEN date_trunc('month'::text,
                                                        v_account_revenue.revenue_date::timestamp with time zone) =
                                             date_trunc('month'::text,
                                                        v_account_revenue.account_date::timestamp with time zone)
                                            THEN 0::double precision
                                        WHEN ((date_part('year'::text, v_account_revenue.revenue_date) -
                                               date_part('year'::text, v_account_revenue.account_date)) *
                                              12::double precision +
                                              (date_part('month'::text, v_account_revenue.revenue_date) -
                                               date_part('month'::text, v_account_revenue.account_date))) >=
                                             1::double precision AND
                                             ((date_part('year'::text, v_account_revenue.revenue_date) -
                                               date_part('year'::text, v_account_revenue.account_date)) *
                                              12::double precision +
                                              (date_part('month'::text, v_account_revenue.revenue_date) -
                                               date_part('month'::text, v_account_revenue.account_date))) <=
                                             11::double precision THEN
                                                    (date_part('year'::text, v_account_revenue.revenue_date) -
                                                     date_part('year'::text, v_account_revenue.account_date)) *
                                                    12::double precision +
                                                    (date_part('month'::text, v_account_revenue.revenue_date) -
                                                     date_part('month'::text, v_account_revenue.account_date))
                                        WHEN ((date_part('year'::text, v_account_revenue.revenue_date) -
                                               date_part('year'::text, v_account_revenue.account_date)) *
                                              12::double precision +
                                              (date_part('month'::text, v_account_revenue.revenue_date) -
                                               date_part('month'::text, v_account_revenue.account_date))) >
                                             11::double precision THEN 13::double precision
                                        ELSE NULL::double precision
                                        END)),
      prec_by_cohort                      --prec of specific cohort from all revenue
                     AS (SELECT cohorts_distr.country,
                               cohorts_distr.cohort,
                               cohorts_distr.email_revenue / sum(NULLIF(cohorts_distr.email_revenue, 0::numeric))
                                                             OVER (PARTITION BY cohorts_distr.country) AS prec_from_total
                        FROM cohorts_distr),
     commulitive_prec 
                     AS (SELECT prec_by_cohort.country,
                                 prec_by_cohort.cohort,
                                 prec_by_cohort.prec_from_total,
                                 round(sum(prec_by_cohort.prec_from_total)
                                       OVER (PARTITION BY prec_by_cohort.country ORDER BY prec_by_cohort.cohort ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW),
                                       4) AS running_total_percent
                          FROM prec_by_cohort),
     account_revenue AS (SELECT account_revenue_abtest_agg.country_id,
                                fn_get_timestamp_from_date_diff(account_revenue_abtest_agg.date_diff) AS                                  date,
                                countries.name_country_eng AS                                                                             country,
                                account_revenue_abtest_agg.groups,
                                concat(account_revenue_abtest_agg.account_source, '-', COALESCE(auth_source.source_name,
                                                                                                'undefined'::text::character varying)) AS account_source,
                                sum(account_revenue_abtest_agg.new_account_revenue) AS                                                    new_account_revenue,
                                sum(account_revenue_abtest_agg.new_serp_account_revenue) AS                                               new_serp_account_revenue,
                                sum(account_revenue_abtest_agg.new_email_account_revenue) AS                                              new_email_account_revenue
                         FROM aggregation.account_revenue_abtest_agg
                                  JOIN dimension.countries ON account_revenue_abtest_agg.country_id = countries.id
                                  LEFT JOIN dimension.auth_source
                                            ON account_revenue_abtest_agg.account_source = auth_source.id
                         WHERE account_revenue_abtest_agg.date_diff >=
                               (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 45)
                         GROUP BY (fn_get_timestamp_from_date_diff(account_revenue_abtest_agg.date_diff)),
                                  countries.name_country_eng, account_revenue_abtest_agg.groups,
                                  account_revenue_abtest_agg.country_id,
                                  (concat(account_revenue_abtest_agg.account_source, '-',
                                          COALESCE(auth_source.source_name, 'undefined'::text::character varying)))),
     account_revenue_union AS (SELECT account_revenue.country_id,
                                      account_revenue.date,
                                      account_revenue.country,
                                      account_revenue.groups,
                                      NULL::text                                AS attribute_name,
                                      NULL::text                                AS attribute_value,
                                      'new_email_account_revenue'::text         AS metric_name,
                                      account_revenue.new_email_account_revenue AS metric_value
                               FROM account_revenue),
     final                  --countring target metric (only new_email_account_revenue as for now)
              AS (SELECT account_revenue_union.country_id,
                      account_revenue_union.groups AS group_num,
                      account_revenue_union.attribute_name,
                      account_revenue_union.attribute_value,
                      account_revenue_union.metric_name,
                      account_revenue_union.metric_value,
                      account_revenue_union.country,
                      account_revenue_union.date   AS action_date,
                      'union 1'::text              AS union_group
               FROM account_revenue_union
               UNION ALL
               SELECT account_revenue_union.country_id,
                      account_revenue_union.groups AS group_num,
                      account_revenue_union.attribute_name,
                      account_revenue_union.attribute_value,
                      account_revenue_union.metric_name,
                      account_revenue_union.metric_value,
                      account_revenue_union.country,
                      account_revenue_union.date   AS action_date,
                      'union 2'::text              AS union_group
               FROM account_revenue_union),
     final_account_revenue AS (SELECT final.country_id,
                                      final.group_num,
                                      final.attribute_name,
                                      final.attribute_value,
                                      final.metric_name,
                                      final.metric_value,
                                      final.country,
                                      final.action_date,
                                      final.union_group
                               FROM final
                               WHERE final.group_num IS NOT NULL),
     test_info AS (SELECT art_1.test_id,
                          art_1.iteration,
                          min(art_1.start_date) AS test_iteration_start_date
                   FROM dredd.abtest_report_task art_1
                   WHERE (CURRENT_DATE - art_1.end_date) >= 0
                     AND (CURRENT_DATE - art_1.end_date) <= 60
                     AND art_1.author::text = 'dredd'::text
                     AND art_1.test_type = 1
                   GROUP BY art_1.test_id, art_1.iteration),
     test_tasks AS (SELECT art_1.test_id,
                           art_1.iteration,
                           ti.test_iteration_start_date                                                                                               AS start_date,
                           art_1.end_date,
                           art_1.id                                                                                                                   AS task_id,
                           1 = rank()
                               OVER (PARTITION BY ti.test_id, ti.iteration, art_1.start_date, art_1.end_date ORDER BY art_1.record_add_datetime DESC) AS is_last_task
                    FROM dredd.abtest_report_task art_1
                             JOIN test_info ti ON art_1.test_id = ti.test_id AND art_1.iteration = ti.iteration AND
                                                  art_1.start_date = ti.test_iteration_start_date
                    WHERE art_1.author::text = 'dredd'::text
                      AND art_1.test_type = 1),
     test_reports_by_day AS (SELECT test_tasks.task_id,
                                    test_tasks.test_id,
                                    test_tasks.iteration,
                                    test_tasks.start_date,
                                    test_tasks.end_date
                             FROM test_tasks
                             WHERE test_tasks.is_last_task
                             ORDER BY test_tasks.test_id, test_tasks.iteration, test_tasks.start_date,
                                      test_tasks.end_date),
     test_start_end AS (SELECT test_reports_by_day.test_id,
                               test_reports_by_day.iteration,
                               min(test_reports_by_day.start_date) AS min_start_date,
                               max(test_reports_by_day.end_date)   AS max_end_end_date
                        FROM test_reports_by_day
                        GROUP BY test_reports_by_day.test_id, test_reports_by_day.iteration),
     all_metrics AS (SELECT art_1.test_id,
                            art_1.iteration,
                            art_1.start_date,
                            art_1.end_date,
                            art_1.id                                                                               AS task_id,
                            compare_groups.metric_name,
                            compare_groups.country_code,
                            c.name_country_eng,
                            CASE
                                WHEN compare_groups.device_type_id IS NULL THEN 5
                                ELSE compare_groups.device_type_id::integer
                                END                                                                                AS device_type_id,
                            compare_groups.base_group_id,
                            compare_groups.competing_group_id,
                            compare_groups.metric_value,
                            compare_groups.delta,
                            compare_groups.delta_ci_lower,
                            compare_groups.delta_ci_upper,
                            compare_groups.p_value,
                            ((compare_groups.stat_parameters -> 'confidence_level'::text)::text)::double precision AS confidence_level,
                            compare_groups.stat_parameters,
                            compare_groups.delta_ci_lower > 0::double precision OR compare_groups.delta_ci_upper <
                                                                                   0::double precision             AS is_significant
                     FROM dredd.abtest_report_task art_1
                              JOIN test_reports_by_day t ON art_1.id = t.task_id
                              JOIN dredd.abtest_report_compare_groups compare_groups
                                   ON art_1.id = compare_groups.task_id AND
                                      (compare_groups.device_type_id <> 2 OR compare_groups.device_type_id IS NULL) AND
                                      compare_groups.metric_name::text = 'New Account Email Revenue'::text
                              LEFT JOIN dimension.countries c
                                        ON compare_groups.country_code::text = lower(c.alpha_2::text)
                     WHERE COALESCE(compare_groups.dimensions_additional ->> 'is_dte_country'::text, 'Total'::text) =
                           'Total'::text),
     significent_output AS (SELECT a.test_id,
                                   a.iteration,
                                   a.start_date,
                                   a.end_date,
                                   a.task_id,
                                   a.metric_name,
                                   a.country_code,
                                   a.name_country_eng,
                                   a.device_type_id,
                                   a.base_group_id,
                                   a.competing_group_id,
                                   a.metric_value,
                                   a.delta,
                                   a.delta_ci_lower,
                                   a.delta_ci_upper,
                                   a.p_value,
                                   a.confidence_level,
                                   a.stat_parameters,
                                   a.is_significant
                            FROM all_metrics a
                                     JOIN test_start_end e
                                          ON a.end_date = e.max_end_end_date AND a.test_id = e.test_id AND
                                             a.iteration = e.iteration),
     split_rows AS (SELECT final_account_revenue.group_num,
                           final_account_revenue.metric_name,
                           final_account_revenue.metric_value,
                           final_account_revenue.country,
                           final_account_revenue.union_group,
                           final_account_revenue.action_date,
                           unnest(string_to_array(final_account_revenue.group_num::text, ' '::text)) AS combined_group,
                           split_part(unnest(string_to_array(final_account_revenue.group_num::text, ' '::text)),
                                      '-'::text, 1)                                                  AS test_id,
                           split_part(
                                   split_part(unnest(string_to_array(final_account_revenue.group_num::text, ' '::text)),
                                              '-'::text, 2), '_'::text, 1)                           AS group_test,
                           split_part(
                                   split_part(unnest(string_to_array(final_account_revenue.group_num::text, ' '::text)),
                                              '-'::text, 2), '_'::text, 2)                           AS iteration
                    FROM final_account_revenue),
     abtest_data AS (SELECT s.test_id,
                            s.iteration,
                            s.country,
                            s.metric_name,
                            CASE
                                WHEN s.union_group = 'union 1'::text THEN s.group_test
                                WHEN s.union_group = 'union 2'::text AND
                                     (s.group_test = ANY (ARRAY ['1'::text, '0'::text])) THEN '0.5'::text
                                WHEN s.union_group = 'union 2'::text AND
                                     (s.group_test <> ALL (ARRAY ['1'::text, '0'::text])) THEN 'not relevant'::text
                                ELSE NULL::text
                                END          AS group_test,
                            sum(
                                    CASE
                                        WHEN s.union_group = 'union 1'::text THEN s.metric_value
                                        WHEN s.union_group = 'union 2'::text THEN s.metric_value
                                        ELSE NULL::numeric
                                        END) 
                            /
                            count(distinct case when s.union_group = 'union 1' then '1'
                              when (s.union_group = 'union 2' and (s.group_test = ANY (ARRAY ['1', '0'])))
                                                           then  group_test   else '0' end)
                                        
                                        AS metric_value
                     FROM split_rows s
                              JOIN significent_output sign
                                   ON s.test_id = sign.test_id::text AND sign.iteration::text = s.iteration AND
                                      s.action_date >= sign.start_date AND s.action_date <= sign.end_date AND s.country::text = sign.name_country_eng::text
                     WHERE 1 = 1
                     GROUP BY s.test_id, s.iteration, s.country, s.metric_name,
                              (
                                  CASE
                                      WHEN s.union_group = 'union 1'::text THEN s.group_test
                                      WHEN s.union_group = 'union 2'::text AND
                                           (s.group_test = ANY (ARRAY ['1'::text, '0'::text])) THEN '0.5'::text
                                      WHEN s.union_group = 'union 2'::text AND
                                           (s.group_test <> ALL (ARRAY ['1'::text, '0'::text])) THEN 'not relevant'::text
                                      ELSE NULL::text
                                      END)),
     groupcomparisons AS (SELECT base.test_id,
                                 sign.is_significant,
                                 base.country,
                                 base.metric_name,
                                 base.iteration,
                                 base.group_test                                                          AS compared_group_test,
                                 base.metric_value                                                        AS compared_total,
                                 target.metric_value                                                      AS group_0_5_total,
                                 base.metric_value / NULLIF(target.metric_value, 0::numeric) - 1::numeric AS diff_value
                          FROM abtest_data base
                                   JOIN abtest_data target ON base.country::text = target.country::text AND
                                                              base.metric_name = target.metric_name AND
                                                              base.iteration = target.iteration AND
                                                              base.test_id = target.test_id AND
                                                              target.group_test in ('0.5')
                                   JOIN significent_output sign ON base.test_id = sign.test_id::text AND
                                                                   sign.iteration::text = base.iteration AND
                                                                   base.country::text = sign.name_country_eng::text
                          WHERE base.group_test <> ALL
                                (ARRAY ['0.5'::text, '1'::text, '0'::text, 'not relevant'::text])),
     final_agg AS (SELECT gc.test_id,
                          gc.is_significant,
                          gc.country,
                          gc.metric_name,
                          gc.iteration,
                          gc.compared_group_test,
                          gc.compared_total,
                          gc.group_0_5_total,
                          gc.diff_value,
                          cp.cohort,
                          cp.running_total_percent,
                          avg.avg_revenue
                   FROM groupcomparisons gc
                            JOIN commulitive_prec cp ON gc.country::text = cp.country::text
                            JOIN avg_email_revenue_by_country_3m avg ON gc.country::text = avg.country::text)
SELECT final_agg.test_id,
       final_agg.is_significant,
       final_agg.country,
       final_agg.metric_name,
       final_agg.iteration,
       final_agg.compared_group_test,
       final_agg.compared_total,
       final_agg.group_0_5_total,
       final_agg.diff_value,
       final_agg.cohort,
       final_agg.running_total_percent,
       final_agg.avg_revenue
FROM final_agg;

alter table aggregation.v_abtest_new_account_email_revenue_estimation
    owner to kostiantyn_kucheriavyi;
