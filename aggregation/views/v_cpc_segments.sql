create view aggregation.v_cpc_segments as
with current_cpc as (
    select
        country_id,
        id_campaign,
        sum(total_value)/sum(click_count) as current_avg_cpc_usd
    from aggregation.auction_click_statistic_analytics
    where click_price > 0
        and cast(date as date) between current_date-7 and current_date-1
    group by country_id, id_campaign
)
select
    c.alpha_2                                                                    as country,
    cs.record_date,
    cs.id_project,
    cs.id_campaign,
    ac.name                                                                      as campaign_name,
    ip.name                                                                      as site,
    sm.sale_manager,
    cs.click_price_usd,
    cs.click_count,
    cs.revenue_usd,
    cs.paid_click_count,
    cs.paid_job_count,
    cs.project_revenue_usd,
    cpc.marketing_cpc,
    cpc.country_median_cpc,
    cpc.country_revenue_rank,
    (cs.click_price_usd - cpc.marketing_cpc)                                     as diff_marketing_cpc,
    (cs.click_price_usd - cpc.country_median_cpc)                                as diff_median_cpc,
    100 * (cs.click_price_usd - cpc.marketing_cpc) / cpc.marketing_cpc           as percent_diff_marketing_cpc,
    100 * (cs.click_price_usd - cpc.country_median_cpc) / cpc.country_median_cpc as percent_diff_median_cpc,
    case
        when cs.marketing_cluster = 0 then 'Very high'
        when cs.marketing_cluster = 1 then 'High'
        when cs.marketing_cluster = 2 then 'Medium'
        when cs.marketing_cluster = 3 then 'Low'
        when cs.marketing_cluster = 4 then 'Extremely low'
        end                                                                      as marketing_cluster,
    case
        when cs.median_cluster = 0 then 'Very high'
        when cs.median_cluster = 1 then 'High'
        when cs.median_cluster = 2 then 'Medium'
        when cs.median_cluster = 3 then 'Low'
        when cs.median_cluster = 4 then 'Extremely low'
        end                                                                      as median_cluster,
    current_cpc.current_avg_cpc_usd
from
    aggregation.segment_campaign_data cs
    join dimension.countries c
         on c.id = cs.id_country
    left join dimension.info_project ip
              on cs.id_country = ip.country
                  and cs.id_project = ip.id
    left join aggregation.segment_country_data cpc
              on cpc.record_date = cs.record_date
                  and cpc.id_country = cs.id_country
    left join aggregation.v_sale_manager sm
              on sm.country = cs.id_country 
                  and sm.id_project = cs.id_project
    left join imp.auction_campaign ac 
              on ac.country_id = cs.id_country 
                  and ac.id = cs.id_campaign
    left join current_cpc
              on current_cpc.country_id = cs.id_country 
                  and current_cpc.id_campaign = cs.id_campaign
;

alter table aggregation.v_cpc_segments
    owner to ono;

grant select on aggregation.v_cpc_segments to ypr;

grant select on aggregation.v_cpc_segments to user_agg_team;
