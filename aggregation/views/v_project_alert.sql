create view v_project_alert
            (country, date, id_user, id_project, site, manager, budget_usd, is_unlim_cmp, job_count, revenue_usd,
             session_count, click_count)
as
SELECT acs.country,
       acs.date,
       acs.id_user,
       acs.id_project,
       acs.site,
       acs.manager,
       max(
               CASE
                   WHEN br.user_budget_month_usd > br.campaign_budget_month_usd THEN br.user_budget_month_usd
                   ELSE br.campaign_budget_month_usd
                   END)       AS budget_usd,
       max(br.is_unlim_cmp)   AS is_unlim_cmp,
       max(jobs.job_count)    AS job_count,
       sum(acs.total_value)   AS revenue_usd,
       sum(acs.session_count) AS session_count,
       sum(acs.click_count)   AS click_count
FROM ono.dv_auction_click_statistic acs
         LEFT JOIN aggregation.budget_revenue_daily_agg br
                   ON acs.country_id = br.country_id AND acs.id_user = br.user_id AND acs.id_project = br.project_id AND
                      acs.date::date = br.action_date
         LEFT JOIN (SELECT jobs_stat_daily.id_country,
                           jobs_stat_daily.id_project,
                           jobs_stat_daily.date::date                          AS date,
                           sum(COALESCE(jobs_stat_daily.paid_job_count, 0) +
                               COALESCE(jobs_stat_daily.organic_job_count, 0)) AS job_count
                    FROM aggregation.jobs_stat_daily
                    GROUP BY jobs_stat_daily.id_country, jobs_stat_daily.id_project, jobs_stat_daily.date) jobs
                   ON acs.country_id = jobs.id_country AND acs.id_project = jobs.id_project AND
                      acs.date::date = jobs.date
WHERE acs.date::date >= (CURRENT_DATE - 30)
GROUP BY acs.country, acs.date, acs.id_user, acs.id_project, acs.site, acs.manager;

alter table v_project_alert
    owner to ono;

