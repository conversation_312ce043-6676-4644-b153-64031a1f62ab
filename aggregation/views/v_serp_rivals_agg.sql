create view aggregation.v_serp_rivals_agg
            (date, country, target_project_id, target_project_name, rival_project_id, rival_project_name,
             click_price_usd, serp_click_value, search_cnt, impression_cnt, impression_on_screen_cnt, click_cnt, position_mean,
             position_min, position_max)
as
select
    d.dt                                                    as date,
    c.alpha_2                                               as country,
    sr.target_project_id,
    tp.name                                                 as target_project_name,
    sr.rival_project_id,
    coalesce(rp.name, 'free or unknown'::character varying) as rival_project_name,
    sr.click_price_usd,
    sr.serp_click_value,
    sr.search_cnt,
    sr.impression_cnt,
    sr.impression_on_screen_cnt,
    sr.click_cnt,
    sr.position_mean,
    sr.position_min,
    sr.position_max
from
    aggregation.serp_rivals_agg sr
    join dimension.countries c
         on c.id = sr.country_id
    join dimension.info_calendar d
         on d.date_diff = sr.date_diff
    left join dimension.info_project tp
              on tp.country = sr.country_id and tp.id = sr.target_project_id
    left join dimension.info_project rp
              on rp.country = sr.country_id and rp.id = sr.rival_project_id;
