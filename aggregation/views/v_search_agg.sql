create or replace view aggregation.v_search_agg
as
WITH keywords AS (SELECT search_agg_1.country_id,
                         search_agg_1.date_diff,
                         search_agg_1.q_id_region,
                         search_agg_1.q_kw,
                         CASE
                             WHEN search_agg_1.country_id <> 2 AND row_number()
                                                                   OVER (PARTITION BY search_agg_1.country_id ORDER BY (sum(search_agg_1.search_cnt)) DESC) <=
                                                                   10000 THEN 1
                             WHEN search_agg_1.country_id = 2 THEN 2
                             ELSE 0
                             END AS modified_q_kw
                  FROM aggregation.search_agg search_agg_1
                  WHERE fn_get_timestamp_from_date_diff(search_agg_1.date_diff) >= (CURRENT_DATE - 30)
                  GROUP BY search_agg_1.country_id, search_agg_1.date_diff, search_agg_1.q_id_region, search_agg_1.q_kw)
SELECT
       countries.name_country_eng                       AS country,
       fn_get_date_from_date_diff(search_agg.date_diff) AS date,
       uts1.name                                        AS name_current_traf_source,
       uts1.channel                                     AS channel_traf_source_current,
       CASE
           WHEN search_agg.user_device = 1 THEN 'mobile'::text
           WHEN search_agg.user_device = 2 THEN 'mobile app'::text
           ELSE 'desktop'::text
           END                                          AS device,
       search_agg.is_local,
       pt.name                                          AS session_create_page_type_name,
       CASE
           WHEN search_agg.serp_results_total::integer = 0 THEN '0'::text
           WHEN search_agg.serp_results_total::integer = 1 THEN '10-20'::text
           WHEN search_agg.serp_results_total::integer = 2 THEN '10-20'::text
           WHEN search_agg.serp_results_total::integer = 3 THEN '20-50'::text
           WHEN search_agg.serp_results_total::integer = 4 THEN '50-100'::text
           WHEN search_agg.serp_results_total::integer = 5 THEN '100-250'::text
           WHEN search_agg.serp_results_total::integer = 6 THEN '250-500'::text
           WHEN search_agg.serp_results_total::integer = 7 THEN '500-1000'::text
           WHEN search_agg.serp_results_total::integer = 8 THEN '1000-5000'::text
           WHEN search_agg.serp_results_total::integer = 9 THEN '5000+'::text
           ELSE NULL::text
           END                                          AS serp_results_for_search,
       dic_search_source.name as  search_source,
       CASE
           WHEN keywords.modified_q_kw = 2 THEN keywords.q_kw
           WHEN keywords.modified_q_kw = 1 THEN keywords.q_kw
           WHEN keywords.modified_q_kw = 0 THEN 'Other'::character varying
           ELSE NULL::character varying
           END                                          AS modified_q_kw,
        CASE
           WHEN keywords.modified_q_kw = 2 THEN ir.name
           WHEN keywords.modified_q_kw = 1 THEN ir.name
           WHEN keywords.modified_q_kw = 0 THEN 'Other'::character varying
           ELSE NULL::character varying
           END                                          AS q_region_name,
       CASE
           WHEN search_agg.empty_searches = 0 THEN 'empty keyword'::text
           WHEN search_agg.empty_searches = 1 THEN 'empty region'::text
           WHEN search_agg.empty_searches = 2 THEN 'empty keyword and region'::text
           WHEN search_agg.empty_searches = 3 THEN 'not empty keyword and region'::text
           ELSE NULL::text
           END                                          AS empty_searches,
       sum(search_agg.search_cnt) as search_cnt ,
       sum(search_agg.click_cnt)  as click_cnt,
       sum(search_agg.revenue_usd)as revenue_usd,
       sum(search_agg.destination_away_click) as destination_away_click,
       sum(search_agg.destination_jdp_away_click) as destination_jdp_away_click,
       sum(search_agg.destination_jdp_apply_click) as destination_jdp_apply_click,
       sum(search_agg.click_paid_cnt) as click_paid_cnt,
       sum(search_agg.click_premium_cnt) as click_premium_cnt,
       sum(search_agg.click_free_cnt) as click_free_cnt,
       sum(search_agg.with_only_free_jobs) as with_only_free_jobs,
       sum(search_agg.with_paid_jobs) as with_paid_jobs,
       sum(search_agg.with_paid_clicks) as with_paid_clicks,
       sum(search_agg.without_paid_clicks) as without_paid_clicks,
       sum(search_agg.without_any_clicks) as without_any_clicks
FROM aggregation.search_agg
         LEFT JOIN dimension.u_traffic_source uts
                   ON uts.id = search_agg.id_traf_source AND uts.country = search_agg.country_id
         LEFT JOIN dimension.u_traffic_source uts1
                   ON uts1.id = search_agg.id_current_traf_source AND uts1.country = search_agg.country_id
         LEFT JOIN dimension.info_region_other ir
                   ON ir.id = search_agg.q_id_region AND ir.country = search_agg.country_id
         LEFT JOIN aggregation.dic_session_create_page_type pt ON pt.id = search_agg.session_create_page_type
         LEFT JOIN dimension.countries ON search_agg.country_id = countries.id
         LEFT JOIN keywords
                   ON search_agg.country_id = keywords.country_id AND search_agg.date_diff = keywords.date_diff AND
                      search_agg.q_kw::text = keywords.q_kw::text AND search_agg.q_id_region = keywords.q_id_region
        left join dimension.dic_search_source
                    on dic_search_source.id = search_agg.search_source
WHERE fn_get_timestamp_from_date_diff(search_agg.date_diff) >= (CURRENT_DATE - 30)
group by
      countries.name_country_eng,
       fn_get_date_from_date_diff(search_agg.date_diff) ,
       uts1.name  ,
       uts1.channel  ,
       CASE
           WHEN search_agg.user_device = 1 THEN 'mobile'::text
           WHEN search_agg.user_device = 2 THEN 'mobile app'::text
           ELSE 'desktop'::text
           END ,
       search_agg.is_local,
       pt.name ,
       CASE
           WHEN search_agg.serp_results_total::integer = 0 THEN '0'::text
           WHEN search_agg.serp_results_total::integer = 1 THEN '10-20'::text
           WHEN search_agg.serp_results_total::integer = 2 THEN '10-20'::text
           WHEN search_agg.serp_results_total::integer = 3 THEN '20-50'::text
           WHEN search_agg.serp_results_total::integer = 4 THEN '50-100'::text
           WHEN search_agg.serp_results_total::integer = 5 THEN '100-250'::text
           WHEN search_agg.serp_results_total::integer = 6 THEN '250-500'::text
           WHEN search_agg.serp_results_total::integer = 7 THEN '500-1000'::text
           WHEN search_agg.serp_results_total::integer = 8 THEN '1000-5000'::text
           WHEN search_agg.serp_results_total::integer = 9 THEN '5000+'::text
           ELSE NULL::text
           END ,
       dic_search_source.name,
       CASE
           WHEN keywords.modified_q_kw = 2 THEN keywords.q_kw
           WHEN keywords.modified_q_kw = 1 THEN keywords.q_kw
           WHEN keywords.modified_q_kw = 0 THEN 'Other'::character varying
           ELSE NULL::character varying
           END ,
        CASE
           WHEN keywords.modified_q_kw = 2 THEN ir.name
           WHEN keywords.modified_q_kw = 1 THEN ir.name
           WHEN keywords.modified_q_kw = 0 THEN 'Other'::character varying
           ELSE NULL::character varying
           END,
       CASE
           WHEN search_agg.empty_searches = 0 THEN 'empty keyword'::text
           WHEN search_agg.empty_searches = 1 THEN 'empty region'::text
           WHEN search_agg.empty_searches = 2 THEN 'empty keyword and region'::text
           WHEN search_agg.empty_searches = 3 THEN 'not empty keyword and region'::text
           ELSE NULL::text
           END
         ;

alter table aggregation.v_search_agg
    owner to ono;

grant select on aggregation.v_search_agg to readonly;

grant select on aggregation.v_search_agg to write_ono;

grant select on aggregation.v_search_agg to writeonly_pyscripts;

grant select on aggregation.v_search_agg to readonly_aggregation;

