create view aggregation.v_account_email_job_click as
select country_id as country_id,
       account_id,
       verify_datediff,
       sum(case when letter_type = 1 and job_click_datediff-verify_datediff <=3 then away_cnt+jdp_cnt end) as letter_1_job_click_0_3,
       sum(case when letter_type = 8 and job_click_datediff-verify_datediff <=3 then away_cnt+jdp_cnt end) as letter_8_job_click_0_3,
       sum(case when letter_type = 71 and job_click_datediff-verify_datediff <=3 then away_cnt+jdp_cnt end) as letter_71_job_click_0_3,
       sum(case when letter_type = 1 and job_click_datediff-verify_datediff <=7 then away_cnt+jdp_cnt end) as letter_1_job_click_0_7,
       sum(case when letter_type = 8 and job_click_datediff-verify_datediff <=7 then away_cnt+jdp_cnt end) as letter_8_job_click_0_7,
       sum(case when letter_type = 71 and job_click_datediff-verify_datediff <=7 then away_cnt+jdp_cnt end) as letter_71_job_click_0_7
from aggregation.letter_job_click
group by country_id,
       account_id,
       verify_datediff
;