create view aggregation.v_alert_early_revenue
            (table_name, scheme_name, metric_name, country_id, country_name_eng, yesterday_metric, is_data) as
SELECT source.table_name,
       source.scheme_name,
       source.metric_name,
       source.country_id,
       COALESCE(countries.name_country_eng, 'Other'::character varying) AS country_name_eng,
       source.yesterday_metric,
       CASE
           WHEN source.yesterday_metric::double precision > 0::double precision THEN 1
           ELSE 0
           END                                                          AS is_data
FROM (WITH startdate AS (SELECT fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 1 AS date_d,
                                fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 8 AS date_d8,
                                CURRENT_DATE - 1                                                AS date,
                                CURRENT_DATE - 8                                                AS date8),
           cluster_affiliate AS (SELECT c.id                                   AS id_country,
                                        lower(c.alpha_2::text)                 AS country_code,
                                        "substring"(s.server_code::text, 1, 2) AS cluster_code
                                 FROM dimension.countries c
                                          LEFT JOIN dimension.product_database d ON c.product_database_id = d.id
                                          LEFT JOIN dimension.product_database_server s ON s.id = d.server_id)
      SELECT 'adv_revenue_by_placement_and_src'::text AS table_name,
             'imp'::text                              AS scheme_name,
             'revenue_usd'::text                      AS metric_name,
             eaa.country                              AS country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) = 1
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                         AS yesterday_metric
      FROM imp.adv_revenue_by_placement_and_src eaa
      WHERE eaa.date_diff >= ((SELECT startdate.date_d8
                               FROM startdate))
      GROUP BY eaa.country
      UNION ALL
      SELECT 'adv_revenue_by_placement_and_src_analytics'::text AS table_name,
             'aggregation'::text                                AS scheme_name,
             'revenue_usd'::text                                AS metric_name,
             eaa.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - eaa.date_diff) = 1
                             THEN eaa.revenue_usd
                         ELSE NULL::integer::numeric
                         END)                                   AS yesterday_metric
      FROM aggregation.adv_revenue_by_placement_and_src_analytics eaa
      WHERE eaa.date_diff >= ((SELECT startdate.date_d8
                               FROM startdate))
      GROUP BY eaa.country_id
      UNION ALL
      SELECT 'jdp_away_clicks_agg'::text AS table_name,
             'aggregation'::text         AS scheme_name,
             'revenue_usd'::text         AS metric_name,
             jac.country_id,
             sum(
                     CASE
                         WHEN (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - jac.action_datediff) = 1
                             THEN jac.revenue_usd
                         ELSE NULL::integer::numeric
                         END)            AS yesterday_metric
      FROM aggregation.jdp_away_clicks_agg jac
      WHERE jac.action_datediff >= ((SELECT startdate.date_d8
                                     FROM startdate))
      GROUP BY jac.country_id) source
         LEFT JOIN dimension.countries ON source.country_id = countries.id
WHERE COALESCE(countries.name_country_eng, 'Other'::character varying)::text <> 'Russia'::text
  AND COALESCE(countries.name_country_eng, 'Other'::character varying)::text <> 'Belarus'::text;

alter table aggregation.v_alert_early_revenue
    owner to ono;

grant select on aggregation.v_alert_early_revenue to readonly;

grant select on aggregation.v_alert_early_revenue to pbi;

grant select on aggregation.v_alert_early_revenue to readonly_aggregation;

grant select on aggregation.v_alert_early_revenue to "pavlo.kvasnii";
