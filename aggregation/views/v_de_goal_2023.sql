create or replace view aggregation.v_de_goal_2023
            (date, country, channel, traffic_source, paid_clicks, free_clicks, total_clicks, revenue, sessions, budget,
             cost, paid_click_share_goal, click_per_session_goal, revenue_goal, sessions_goal, budget_goal, cost_goal,
             adsense_revenue, conversions, conversion_aways, conversion_revenue)
as
with
    click as (
        select
            date(fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff)) as date,
            countries.alpha_2                                                          as country,
            coalesce(u_traffic_source.channel, 'Undefined'::character varying)         as channel,
            u_traffic_source.name                                                      as traffic_source,
            sum(
                    case
                        when jdp_away_clicks_agg.revenue_usd > 0::numeric then jdp_away_clicks_agg.jdp_away_count
                        else 0
                        end)                                                           as paid_clicks,
            sum(
                    case
                        when jdp_away_clicks_agg.revenue_usd = 0::numeric then jdp_away_clicks_agg.jdp_away_count
                        else 0
                        end)                                                           as free_clicks,
            sum(jdp_away_clicks_agg.jdp_away_count)                                    as total_clicks,
            sum(jdp_away_clicks_agg.revenue_usd)                                       as revenue
        from
            aggregation.jdp_away_clicks_agg
            left join dimension.u_traffic_source
                      on jdp_away_clicks_agg.country_id = u_traffic_source.country and
                         jdp_away_clicks_agg.id_current_traf_source = u_traffic_source.id
            join dimension.countries
                 on jdp_away_clicks_agg.country_id = countries.id
        where
                fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff) >=
                '2023-01-01 00:00:00'::timestamp without time zone
        group by
            (date(fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff))),
            jdp_away_clicks_agg.country_id, countries.alpha_2, u_traffic_source.name,
            (coalesce(u_traffic_source.channel, 'Undefined'::character varying))
    ),
    adsense as (
        select
            adsense_afs.action_date                                 as create_date,
            sum(adsense_afs.estimated_earnings_usd)                 as estimated_income_usd,
            coalesce(countries.alpha_2, 'Other'::character varying) as country
        from
            imp_api.adsense_afs
            left join ono.dic_countries_adsense
                      on adsense_afs.country_name::text = dic_countries_adsense.country_as_in_reports
            left join dimension.countries
                      on lower(dic_countries_adsense.two_digits) = lower(countries.alpha_2::text)
        where
            adsense_afs.action_date >= '2023-01-01'::date
        group by adsense_afs.action_date, (coalesce(countries.alpha_2, 'Other'::character varying))
        union all
        select
            adsense_afc.action_date                                 as create_date,
            sum(adsense_afc.estimated_earnings_usd)                 as estimated_income_usd,
            coalesce(countries.alpha_2, 'Other'::character varying) as "coalesce"
        from
            imp_api.adsense_afc
            left join ono.dic_countries_adsense
                      on "left"(
                                 case
                                     when adsense_afc.domain_code::text = 'jooble.org'::text
                                         then 'us.jooble.org'::character varying
                                     when adsense_afc.domain_code::text = 'ja.jooble.org'::text
                                         then 'jp.jooble.org'::character varying
                                     else adsense_afc.domain_code
                                     end::text, 2) = dic_countries_adsense.country_as_in_reports
            left join dimension.countries
                      on lower(dic_countries_adsense.two_digits) = lower(countries.alpha_2::text)
        where
            adsense_afc.action_date >= '2023-01-01'::date
        group by adsense_afc.action_date, (coalesce(countries.alpha_2, 'Other'::character varying))
    ),
    sessions as (
        select
            date(fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff)) as date,
            coalesce(u_traffic_source.channel, 'Undefined'::character varying)       as channel,
            u_traffic_source.name                                                    as traffic_source,
            countries.alpha_2                                                        as country,
            sum(coalesce(session_daily_agg.total_session_cnt, 0) -
                coalesce(session_daily_agg.bot_session_cnt, 0))                      as sessions
        from
            aggregation.session_daily_agg
            left join dimension.u_traffic_source
                      on session_daily_agg.country_id = u_traffic_source.country and
                         session_daily_agg.id_current_traf_source = u_traffic_source.id
            join dimension.countries
                 on session_daily_agg.country_id = countries.id
        where
                fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff) >=
                '2023-01-01 00:00:00'::timestamp without time zone
        group by
            (date(fn_get_timestamp_from_date_diff(session_daily_agg.action_datediff))), countries.alpha_2,
            u_traffic_source.name, (coalesce(u_traffic_source.channel, 'Undefined'::character varying))
    ),
    budget as (
        select
            user_budget.date,
            user_budget.country,
            sum(
                    case
                        when coalesce(user_budget.budget, 0::double precision) = 0::double precision and
                             user_budget.revenue_usd > 0::double precision then user_budget.revenue_usd
                        else user_budget.budget
                        end) as budget
        from
            (select
                 v_budget_and_revenue.date,
                 v_budget_and_revenue.id_user,
                 v_budget_and_revenue.country,
                 sum(v_budget_and_revenue.revenue_usd) as revenue_usd,
                 sum(v_budget_and_revenue.budget)      as budget
             from
                 aggregation.v_budget_and_revenue
             where
                 v_budget_and_revenue.date >= '2023-01-01'::date
             group by v_budget_and_revenue.date, v_budget_and_revenue.country, v_budget_and_revenue.id_user) user_budget
        group by user_budget.date, user_budget.country
    ),
    cost as (
        select
            paid_traffic_metrics_agg.date,
            upper(paid_traffic_metrics_agg.country::text) as country,
            'Paid Search'::text                           as channel,
            sum(paid_traffic_metrics_agg.cost_usd)        as cost_usd,
            sum(paid_traffic_metrics_agg.clicks)          as ppc_clicks
        from
            aggregation.paid_traffic_metrics_agg
        where
            paid_traffic_metrics_agg.date >= '2023-01-01'::date
        group by paid_traffic_metrics_agg.date, paid_traffic_metrics_agg.country
        union all
        select
            v_main_metrics_agg.click_date,
            v_main_metrics_agg.country,
            'Affiliate'::text                          as channel,
            sum(v_main_metrics_agg.certified_cost_usd) as cost,
            0                                          as paid_clicks
        from
            affiliate.v_main_metrics_agg
            left join dimension.info_project ip
                      on ip.country = v_main_metrics_agg.id_country and ip.id = v_main_metrics_agg.id_project
        where
              v_main_metrics_agg.click_date >= '2023-01-01'
          and not coalesce(lower(ip.name), '') like 'j-vers.%'
        group by
            v_main_metrics_agg.click_date,
            v_main_metrics_agg.country
    ),
    conversions as (
        select
            project_conversions_daily.session_date      as date,
            countries.alpha_2::text                     as country,
            u_traffic_source.channel,
            u_traffic_source.name                       as traffic_source,
            sum(project_conversions_daily.away_revenue) as conversion_revenue,
            sum(project_conversions_daily.conversions)  as conversions,
            sum(project_conversions_daily.aways)        as conversion_aways,
            case
                when project_conversions_daily.session_date >= min(
                                                               case
                                                                   when sum(project_conversions_daily.conversions) > 0
                                                                       then project_conversions_daily.session_date
                                                                   else null::date
                                                                   end)
                                                               over (partition by (countries.alpha_2::text), project_conversions_daily.project_id) and
                     project_conversions_daily.session_date <= max(
                                                               case
                                                                   when sum(project_conversions_daily.conversions) > 0
                                                                       then project_conversions_daily.session_date + '7 days'::interval
                                                                   else null::timestamp without time zone
                                                                   end)
                                                               over (partition by (countries.alpha_2::text), project_conversions_daily.project_id)
                    then 1
                else 0
                end                                     as first_day_with_conversion,
            case
                when project_conversions_daily.metric::text = 'aways'::text then 'From clients'::text
                else
                    case
                        when sum(
                             case
                                 when project_conversions_daily.metric::text = 'applies'::text
                                     then sum(project_conversions_daily.away_revenue)
                                 else 0::numeric
                                 end)
                             over (partition by (countries.alpha_2::text), project_conversions_daily.project_id) > 0::numeric
                            then 'Apply on Jooble'::text
                        else 'Easy Apply'::text
                        end
                end                                     as away_type,
            case
                when sum(project_conversions_daily.conversions)
                     over (partition by (countries.alpha_2::text), project_conversions_daily.project_id, project_conversions_daily.metric) >
                     5 and max(
                           case
                               when sum(project_conversions_daily.conversions) > 0
                                   then project_conversions_daily.session_date
                               else null::date
                               end)
                           over (partition by (countries.alpha_2::text), project_conversions_daily.project_id, project_conversions_daily.metric) >=
                           (((select
                                  max(project_conversions_daily_1.session_date) as max
                              from
                                  aggregation.project_conversions_daily project_conversions_daily_1)) -
                            '14 days'::interval) then 'yes'::text
                else 'no'::text
                end                                     as we_have_conv_info
        from
            aggregation.project_conversions_daily
            left join dimension.countries
                      on project_conversions_daily.country_id = countries.id
            left join dimension.u_traffic_source
                      on project_conversions_daily.country_id = u_traffic_source.country and
                         project_conversions_daily.id_current_traf_source = u_traffic_source.id
        where
                project_conversions_daily.session_date >= '2023-01-01 00:00:00'::timestamp without time zone
        group by
            project_conversions_daily.session_date, countries.alpha_2, u_traffic_source.channel, u_traffic_source.name,
            project_conversions_daily.metric, project_conversions_daily.project_id,
            project_conversions_daily.conversions
    ),
    fact as (
        select
            budget.date,
            budget.country,
            null::character varying as channel,
            null::character varying as traffic_source,
            null::bigint            as paid_clicks,
            null::bigint            as free_clicks,
            null::bigint            as total_clicks,
            null::numeric           as revenue,
            null::bigint            as sessions,
            budget.budget,
            0                       as cost,
            null::bigint            as conversions,
            null::bigint            as conversion_aways,
            null::numeric           as conversion_revenue,
            null::numeric           as ppc_clicks
        from
            budget
        union all
        select
            click.date,
            click.country,
            click.channel,
            click.traffic_source,
            click.paid_clicks,
            click.free_clicks,
            click.total_clicks,
            click.revenue,
            null::bigint           as sessions,
            null::double precision as budget,
            0                      as cost,
            null::bigint           as conversions,
            null::bigint           as conversion_aways,
            null::numeric          as conversion_revenue,
            null::numeric          as ppc_clicks
        from
            click
        union all
        select
            sessions.date,
            sessions.country,
            sessions.channel,
            sessions.traffic_source,
            null::bigint           as paid_clicks,
            null::bigint           as free_clicks,
            null::bigint           as total_clicks,
            null::numeric          as revenue,
            sessions.sessions,
            null::double precision as budget,
            0                      as cost,
            null::bigint           as conversions,
            null::bigint           as conversion_aways,
            null::numeric          as conversion_revenue,
            null::numeric          as ppc_clicks
        from
            sessions
        union all
        select
            cost.date,
            cost.country,
            cost.channel,
            null::character varying as traffic_source,
            null::bigint            as paid_clicks,
            null::bigint            as free_clicks,
            null::bigint            as total_clicks,
            null::numeric           as revenue,
            null::bigint            as sessions,
            null::double precision  as budget,
            cost.cost_usd           as cost,
            null::bigint            as conversions,
            null::bigint            as conversion_aways,
            null::numeric           as conversion_revenue,
            cost.ppc_clicks
        from
            cost
        union all
        select
            conversions.date,
            conversions.country,
            conversions.channel,
            conversions.traffic_source,
            null::bigint           as paid_clicks,
            null::bigint           as free_clicks,
            null::bigint           as total_clicks,
            null::numeric          as revenue,
            null::bigint           as sessions,
            null::double precision as budget,
            0                      as cost,
            conversions.conversions,
            conversions.conversion_aways,
            conversions.conversion_revenue,
            null::numeric          as ppc_clicks
        from
            conversions
        where
              conversions.first_day_with_conversion = 1
          and conversions.away_type <> 'Easy Apply'::text
          and conversions.we_have_conv_info = 'yes'::text
    )
select
    fact.date,
    fact.country,
    fact.channel,
    fact.traffic_source,
    fact.paid_clicks,
    fact.free_clicks,
    fact.total_clicks,
    fact.revenue,
    fact.sessions,
    fact.budget,
    fact.cost,
    null::numeric as paid_click_share_goal,
    null::numeric as click_per_session_goal,
    null::numeric as revenue_goal,
    null::numeric as sessions_goal,
    null::numeric as budget_goal,
    null::numeric as cost_goal,
    0             as adsense_revenue,
    fact.conversions,
    fact.conversion_aways,
    fact.conversion_revenue,
    fact.ppc_clicks
from
    fact
union all
select
    de_goal_metrics.date::date        as date,
    'DE'::character varying           as country,
    de_goal_metrics.channel,
    null::character varying           as traffic_source,
    null::bigint                      as paid_clicks,
    null::bigint                      as free_clicks,
    null::bigint                      as total_clicks,
    null::numeric                     as revenue,
    null::bigint                      as sessions,
    null::double precision            as budget,
    null::numeric                     as cost,
    de_goal_metrics.paid_click_share  as paid_click_share_goal,
    de_goal_metrics.click_per_session as click_per_session_goal,
    de_goal_metrics.revenue           as revenue_goal,
    de_goal_metrics.sessions          as sessions_goal,
    de_goal_metrics.budget            as budget_goal,
    de_goal_metrics.cost              as cost_goal,
    null::integer                     as adsense_revenue,
    null::bigint                      as conversions,
    null::bigint                      as conversion_aways,
    null::numeric                     as conversion_revenue,
    null::numeric                     as ppc_clicks
from
    ono.de_goal_metrics
union all
select
    adsense.create_date          as date,
    adsense.country,
    null::character varying      as channel,
    null::character varying      as traffic_source,
    null::bigint                 as paid_clicks,
    null::bigint                 as free_clicks,
    null::bigint                 as total_clicks,
    null::numeric                as revenue,
    null::bigint                 as sessions,
    null::double precision       as budget,
    null::numeric                as cost,
    null::numeric                as paid_click_share_goal,
    null::numeric                as click_per_session_goal,
    null::numeric                as revenue_goal,
    null::numeric                as sessions_goal,
    null::numeric                as budget_goal,
    null::numeric                as cost_goal,
    adsense.estimated_income_usd as adsense_revenue,
    null::bigint                 as conversions,
    null::bigint                 as conversion_aways,
    null::numeric                as conversion_revenue,
    null::numeric                as ppc_clicks
from
    adsense;

alter table aggregation.v_de_goal_2023
    owner to ono;
