create or replace view aggregation.v_outreach_banner_stat
            (country_cc, country_name_eng, country_id, banner_id, partner_id, place_id, url, donor_name,
             activation_date, deactivation_date, priority, flags, replace_adx, click_price, account_email, account_name,
             account_team, on_keywords, on_region, active, limit_max_show, limit_max_click, limit_max_show_per_day,
             limit_max_click_per_day, limit_max_day, date_diff, dates, last_date_show, last_date_click, shows, clicks)
as
SELECT lower(cc.alpha_2::text) AS country_cc,
       cc.name_country_eng     AS country_name_eng,
       b.country_id,
       b.banner_id,
       b.partner_id,
       b.place_id,
       b.url,
       b.donor_name,
       b.activation_date,
       b.deactivation_date,
       b.priority,
       CASE
           WHEN (b.flags & 8) = 8 THEN 'disabled temporary'::text
           WHEN (b.flags & 4) = 4 THEN 'disabled'::text
           WHEN (b.flags & 1) = 1 THEN 'keyword boost'::text
           WHEN (b.flags & 2) = 2 THEN 'region boost'::text
           ELSE 'other'::text
           END                 AS flags,
       b.replace_adx,
       b.click_price,
       b.manager_email         AS account_email,
       details.manager_name    AS account_name,
       details.manager_team    AS account_team,
       b.on_keywords,
       b.on_region,
       b.active,
       b.limit_max_show,
       b.limit_max_click,
       b.limit_max_show_per_day,
       b.limit_max_click_per_day,
       b.limit_max_day,
       bs.date_diff,
       ic.dt                   AS dates,
       NULL::date              AS last_date_show,
       NULL::date              AS last_date_click,
       bs.shows,
       bs.clicks
FROM traffic.banner_info b
         LEFT JOIN dimension.countries cc ON cc.id = b.country_id
         LEFT JOIN traffic.banner_stat_history bs ON b.country_id = bs.country_id AND b.banner_id = bs.banner_id
         LEFT JOIN dimension.info_calendar ic ON ic.date_diff = bs.date_diff
         LEFT JOIN vnov.banner_email_detail details ON details.manager_email = b.manager_email::text
UNION ALL
SELECT lower(cc.alpha_2::text) AS country_cc,
       cc.name_country_eng     AS country_name_eng,
       b.country_id,
       b.banner_id,
       b.partner_id,
       b.place_id,
       b.url,
       b.donor_name,
       b.activation_date,
       b.deactivation_date,
       b.priority,
       CASE
           WHEN (b.flags & 8) = 8 THEN 'disabled temporary'::text
           WHEN (b.flags & 4) = 4 THEN 'disabled'::text
           WHEN (b.flags & 1) = 1 THEN 'keyword boost'::text
           WHEN (b.flags & 2) = 2 THEN 'region boost'::text
           ELSE 'other'::text
           END                 AS flags,
       b.replace_adx,
       b.click_price,
       b.manager_email         AS account_email,
       details.manager_name    AS account_name,
       details.manager_team    AS account_team,
       b.on_keywords,
       b.on_region,
       b.active,
       b.limit_max_show,
       b.limit_max_click,
       b.limit_max_show_per_day,
       b.limit_max_click_per_day,
       b.limit_max_day,
       bsd.date_diff,
       icc.dt                  AS dates,
       bsd.last_date_show,
       bsd.last_date_click,
       bsd.shows,
       bsd.clicks
FROM traffic.banner_info b
         LEFT JOIN dimension.countries cc ON cc.id = b.country_id
         LEFT JOIN traffic.banner_stat_today bsd ON b.country_id = bsd.country_id AND b.banner_id = bsd.banner_id
         LEFT JOIN dimension.info_calendar icc ON icc.date_diff = bsd.date_diff
         LEFT JOIN vnov.banner_email_detail details ON details.manager_email = b.manager_email::text
UNION ALL
SELECT NULL::text                        AS country_cc,
       NULL::character varying(25)       AS country_name_eng,
       NULL::integer                     AS country_id,
       NULL::integer                     AS banner_id,
       NULL::integer                     AS partner_id,
       NULL::integer                     AS place_id,
       NULL::character varying(500)      AS url,
       NULL::character varying(50)       AS donor_name,
       NULL::timestamp without time zone AS activation_date,
       NULL::timestamp without time zone AS deactivation_date,
       NULL::integer                     AS priority,
       NULL::text                        AS flags,
       NULL::integer                     AS replace_adx,
       NULL::numeric                     AS click_price,
       NULL::character varying(100)      AS account_email,
       NULL::text                        AS account_name,
       NULL::text                        AS account_team,
       NULL::boolean                     AS on_keywords,
       NULL::boolean                     AS on_region,
       NULL::integer                     AS active,
       NULL::integer                     AS limit_max_show,
       NULL::integer                     AS limit_max_click,
       NULL::integer                     AS limit_max_show_per_day,
       NULL::integer                     AS limit_max_click_per_day,
       NULL::integer                     AS limit_max_day,
       NULL::integer                     AS date_diff,
       ic.dt                             AS dates,
       NULL::date                        AS last_date_show,
       NULL::date                        AS last_date_click,
       NULL::integer                     AS shows,
       NULL::integer                     AS clicks
FROM dimension.info_calendar ic
WHERE ic.dt_year = 2022;

alter table aggregation.v_outreach_banner_stat
    owner to ono;

grant select on aggregation.v_outreach_banner_stat to readonly;

grant select on aggregation.v_outreach_banner_stat to pbi;

grant select on aggregation.v_outreach_banner_stat to readonly_aggregation;

grant select on aggregation.v_outreach_banner_stat to "pavlo.kvasnii";

grant select on aggregation.v_outreach_banner_stat to bdu;

grant select on aggregation.v_outreach_banner_stat to vnov;

