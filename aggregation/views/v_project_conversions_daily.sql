create or replace view aggregation.v_project_conversions_daily
as
SELECT pcd.country_id,
       pcd.metric,
       pcd.session_date,
       pcd.project_id,
       pcd.project_name,
       pcd.away_type,
       pcd.is_mobile,
       pcd.traffic_name,
       pcd.traffic_is_paid,
       case when pcd.session_date >=(current_date) - 30 then pcd.campaign_id else null end campaign_id,
       case when pcd.session_date >=(current_date) - 30 then pcd.campaign_name else null end campaign_name,
       pcd.channel,
       CASE
           WHEN pcd.ip_cc::text = lower(countries.alpha_2::text) OR
                pcd.ip_cc::text = 'gb'::text AND lower(countries.alpha_2::text) = 'uk'::text THEN 1
           ELSE 0
           END                               AS ip_cc,
       pcd.name,
       sum(pcd.away_revenue)                 AS away_revenue,
       sum(pcd.away_revenue_origin_currency) AS away_revenue_origin_currency,
       sum(pcd.aways)                        AS aways,
       sum(pcd.conversions)                  AS conversions,
       countries.name_country_eng            AS country,
       countries.alpha_2,
       affiliate_manager.manager
FROM aggregation.project_conversions_daily pcd
         JOIN dimension.countries ON pcd.country_id = countries.id
         LEFT JOIN (SELECT partner_daily_snapshot.partner,
                           max(partner_daily_snapshot.manager::text) AS manager
                    FROM affiliate.partner_daily_snapshot
                    WHERE partner_daily_snapshot.manager IS NOT NULL
                      AND partner_daily_snapshot.date_diff >= 44560
                    GROUP BY partner_daily_snapshot.partner) affiliate_manager
                   ON pcd.traffic_name::text = affiliate_manager.partner::text
WHERE pcd.session_date >= '2022-01-01'::date
GROUP BY pcd.country_id, pcd.metric, pcd.session_date, pcd.project_id, pcd.project_name, pcd.away_type, pcd.is_mobile,
         case when pcd.session_date >=(current_date) - 30 then pcd.campaign_id else null end,
         case when pcd.session_date >=(current_date) - 30 then pcd.campaign_name else null end,
         pcd.traffic_name, pcd.traffic_is_paid, pcd.channel, pcd.name,
         countries.name_country_eng, countries.alpha_2,
         affiliate_manager.manager,
         (
             CASE
                 WHEN pcd.ip_cc::text = lower(countries.alpha_2::text) OR
                      pcd.ip_cc::text = 'gb'::text AND lower(countries.alpha_2::text) = 'uk'::text THEN 1
                 ELSE 0
                 END);

alter table aggregation.v_project_conversions_daily
    owner to ono;

grant select on aggregation.v_project_conversions_daily to readonly;

grant delete, insert, select, update on aggregation.v_project_conversions_daily to write_ono;

grant select on aggregation.v_project_conversions_daily to ypr;

grant select on aggregation.v_project_conversions_daily to readonly_aggregation;

grant select on aggregation.v_project_conversions_daily to "pavlo.kvasnii";
