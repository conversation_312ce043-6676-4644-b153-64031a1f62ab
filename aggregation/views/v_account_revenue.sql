create or replace view aggregation.v_account_revenue
            (country, account_date, revenue_date, source, source_name, device_type_id, new_account_cnt, revenue_account_cnt,
             away_cnt, account_revenue, new_verified_account_cnt, email_revenue, serp_revenue, account_traffic_source)
as
SELECT countries.name_country_eng                                                       AS country,
       account_revenue.account_date::date                                               AS account_date,
       account_revenue.revenue_date::date                                               AS revenue_date,
       account_revenue.source,
       btrim(COALESCE(auth_source.source_name, 'other'::text::character varying)::text) AS source_name,
       account_revenue.device_type_id,
       account_revenue.new_account_cnt,
       account_revenue.revenue_account_cnt,
       account_revenue.away_cnt,
       account_revenue.account_revenue,
       account_revenue.new_verified_account_cnt,
       CASE
           WHEN account_revenue.revenue_date::text >= '2022-03-01'::text THEN account_revenue.email_revenue
           ELSE NULL::numeric
           END                                                                          AS email_revenue,
       CASE
           WHEN account_revenue.revenue_date::text >= '2022-03-01'::text THEN account_revenue.serp_revenue
           ELSE NULL::numeric
           END                                                                          AS serp_revenue,
       COALESCE(
               CASE
                   WHEN account_revenue.account_date::text > '2023-04-06'::text THEN u_traffic_source.channel
                   ELSE NULL::character varying
                   END, 'Undefined'::character varying)                                 AS account_traffic_source
FROM aggregation.account_revenue
LEFT JOIN dimension.countries ON account_revenue.country_id = countries.id
LEFT JOIN dimension.auth_source ON account_revenue.source = auth_source.id
LEFT JOIN dimension.u_traffic_source
          ON account_revenue.country_id = u_traffic_source.country AND u_traffic_source.id = account_revenue.id_traf_src
WHERE account_revenue.account_date::date >= '2021-01-01'::date
  AND (account_revenue.revenue_date IS NULL OR
       account_revenue.revenue_date::date >= account_revenue.account_date::date);

alter table aggregation.v_account_revenue
    owner to ono;
