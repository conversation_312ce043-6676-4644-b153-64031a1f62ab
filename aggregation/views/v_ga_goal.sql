create or replace view aggregation.v_ga_goal
            (country, country_name_eng, date, year, source, organic_sessions_wo_job_apply, jobs_apply,
             min_predict_traffic, max_predict_traffic, target_filter, organic_revenue_usd, organic_search_revenue_usd)
as
WITH targets AS (
    SELECT f.country,
           f.target_month                AS date_diff,
           d.dt                          AS date,
           date_part('year'::text, d.dt) AS year,
           f.min_predict_traffic,
           f.max_predict_traffic,
           f.filter                      AS target_filter
    FROM aggregation.ga_organic_traffic_fcst f
             JOIN dimension.info_calendar d ON d.date_diff = f.target_month
),
     ga_data AS (
         SELECT ga.country_code                  AS country,
                ga.date,
                date_part('year'::text, ga.date) AS year,
                ga.is_local,
                sum(
                        CASE
                            WHEN ga.channelgrouping::text = 'Organic Search'::text AND
                                 ga.sourcemedium::text !~~ '%google_jobs_apply%'::text THEN ga.session_cnt
                            ELSE 0::numeric
                            END)                 AS organic_sessions_wo_job_apply,
                sum(
                        CASE
                            WHEN ga.channelgrouping::text = 'Organic Search'::text AND
                                 ga.sourcemedium::text ~~ '%google_jobs_apply%'::text THEN ga.session_cnt
                            ELSE 0::numeric
                            END)                 AS jobs_apply
         FROM aggregation.v_ga_session_data ga
         WHERE ga.date >= '2020-01-01'::date
           AND ga.date <= '2023-12-31'::date
         GROUP BY ga.country_code, ga.date, (date_part('year'::text, ga.date)), ga.is_local
     ),
     internal_data AS (
         SELECT lower(v_country_info.alpha_2::text) AS country,
                ic.dt                               AS date,
                date_part('year'::text, ic.dt)      AS year,
                sum(
                        CASE
                            WHEN si.current_channel::text = 'Organic Search'::text AND
                                 si.current_traffic_source_name::text !~~ '%Google_for_Jobs%'::text
                                THEN si.total_session_cnt - COALESCE(si.bot_session_cnt, 0)
                            ELSE 0
                            END)                    AS organic_sessions_wo_job_apply,
                sum(
                        CASE
                            WHEN si.current_channel::text = 'Organic Search'::text AND
                                 si.current_traffic_source_name::text = 'Google_for_Jobs'::text
                                THEN si.total_session_cnt - COALESCE(si.bot_session_cnt, 0)
                            ELSE 0
                            END)                    AS jobs_apply
         FROM aggregation.v_session_daily_agg si
                  LEFT JOIN dimension.info_calendar ic ON si.action_datediff = ic.date_diff
                  LEFT JOIN dimension.v_country_info ON si.country_id = v_country_info.country_id
         WHERE ic.dt >= '2022-01-01'::date
           AND ic.dt <= '2023-12-31'::date
         GROUP BY (lower(v_country_info.alpha_2::text)), ic.dt, (date_part('year'::text, ic.dt))
     ),
     adv_revenue AS (
         SELECT lower(c.alpha_2::text)        AS country,
                d.dt                          AS date,
                date_part('year'::text, d.dt) AS year,
                sum(
                        CASE
                            WHEN COALESCE(t.channel, 'Undefined'::text::character varying)::text =
                                 'Organic Search'::text AND rev.placement::text = 'search'::text THEN rev.revenue_usd
                            ELSE 0::numeric
                            END)              AS organic_search_revenue_usd,
                sum(
                        CASE
                            WHEN COALESCE(t.channel, 'Undefined'::text::character varying)::text = 'Organic Search'::text
                                THEN rev.revenue_usd
                            ELSE 0::numeric
                            END)              AS organic_revenue_usd
         FROM imp.adv_revenue_by_placement_and_src rev
                  LEFT JOIN dimension.countries c ON rev.country = c.id
                  LEFT JOIN dimension.info_calendar d ON rev.date_diff = d.date_diff
                  LEFT JOIN dimension.u_traffic_source t ON rev.country = t.country AND rev.id_traf_source = t.id
         WHERE d.dt >= '2021-02-01'::date
           AND d.dt < '2022-05-01'::date
         GROUP BY (lower(c.alpha_2::text)), d.dt, (date_part('year'::text, d.dt))
         UNION ALL
         SELECT lower(c.alpha_2::text)        AS country,
                d.dt                          AS date,
                date_part('year'::text, d.dt) AS year,
                sum(
                        CASE
                            WHEN COALESCE(t.channel, 'Undefined'::text::character varying)::text =
                                 'Organic Search'::text AND rev.placement::text = 'search'::text THEN rev.revenue_usd
                            ELSE 0::numeric
                            END)              AS organic_search_revenue_usd,
                sum(
                        CASE
                            WHEN COALESCE(t.channel, 'Undefined'::text::character varying)::text = 'Organic Search'::text
                                THEN rev.revenue_usd
                            ELSE 0::numeric
                            END)              AS organic_revenue_usd
         FROM aggregation.adv_revenue_by_placement_and_src_analytics rev
                  LEFT JOIN dimension.countries c ON rev.country_id = c.id
                  LEFT JOIN dimension.info_calendar d ON rev.date_diff = d.date_diff
                  LEFT JOIN dimension.u_traffic_source t
                            ON rev.country_id = t.country AND rev.id_current_traf_source = t.id
         WHERE d.dt >= '2022-05-01'::date
           AND d.dt <= '2023-12-31'::date
         GROUP BY (lower(c.alpha_2::text)), d.dt, (date_part('year'::text, d.dt))
     ),
     unions AS (
         SELECT t.country,
                t.date,
                t.year,
                'Target'::text AS source,
                NULL::bigint   AS organic_sessions_wo_job_apply,
                NULL::numeric  AS jobs_apply,
                t.min_predict_traffic,
                t.max_predict_traffic,
                t.target_filter,
                NULL::numeric  AS organic_revenue_usd,
                NULL::numeric  AS organic_search_revenue_usd
         FROM targets t
         UNION ALL
         SELECT g.country,
                g.date,
                g.year,
                'GA & GA4'::text AS source,
                g.organic_sessions_wo_job_apply,
                g.jobs_apply,
                NULL::bigint     AS min_predict_traffic,
                NULL::bigint     AS max_predict_traffic,
                NULL::integer    AS target_filter,
                NULL::numeric    AS organic_revenue_usd,
                NULL::numeric    AS organic_search_revenue_usd
         FROM ga_data g
         WHERE g.country <> 'ru'::text
           AND g.country <> 'by'::text
         UNION ALL
         SELECT id.country,
                id.date,
                id.year,
                'Internal sessions'::text AS source,
                id.organic_sessions_wo_job_apply,
                id.jobs_apply,
                NULL::bigint              AS min_predict_traffic,
                NULL::bigint              AS max_predict_traffic,
                NULL::integer             AS target_filter,
                NULL::numeric             AS organic_revenue_usd,
                NULL::numeric             AS organic_search_revenue_usd
         FROM internal_data id
         WHERE id.country <> 'ru'::text
           AND id.country <> 'by'::text
         UNION ALL
         SELECT a.country,
                a.date,
                a.year,
                'Target'::text AS source,
                NULL::bigint   AS organic_sessions_wo_job_apply,
                NULL::numeric  AS jobs_apply,
                NULL::bigint   AS min_predict_traffic,
                NULL::bigint   AS max_predict_traffic,
                NULL::integer  AS target_filter,
                a.organic_revenue_usd,
                a.organic_search_revenue_usd
         FROM adv_revenue a
         WHERE a.country <> 'ru'::text
           AND a.country <> 'by'::text
         UNION ALL
         SELECT NULL::text     AS country,
                c.dt           AS date,
                c.dt_year      AS year,
                'Target'::text AS source,
                NULL::bigint   AS organic_sessions_wo_job_apply,
                NULL::numeric  AS jobs_apply,
                NULL::bigint   AS min_predict_traffic,
                NULL::bigint   AS max_predict_traffic,
                NULL::integer  AS target_filter,
                NULL::numeric  AS organic_revenue_usd,
                NULL::numeric  AS organic_search_revenue_usd
         FROM dimension.info_calendar c
         WHERE c.dt >= '2023-01-01'::date
           AND c.dt <= '2023-12-31'::date
     )
SELECT uu.country,
       cc.name_country_eng AS country_name_eng,
       uu.date,
       uu.year,
       uu.source,
       uu.organic_sessions_wo_job_apply,
       uu.jobs_apply,
       uu.min_predict_traffic,
       uu.max_predict_traffic,
       uu.target_filter,
       uu.organic_revenue_usd,
       uu.organic_search_revenue_usd
FROM unions uu
         LEFT JOIN dimension.countries cc ON lower(cc.alpha_2::text) = uu.country;

alter table aggregation.v_ga_goal
    owner to ono;

grant select on aggregation.v_ga_goal to readonly;

grant select on aggregation.v_ga_goal to writeonly_pyscripts;

