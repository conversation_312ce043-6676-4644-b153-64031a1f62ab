create or replace view aggregation.v_crm_account_opportunity
            (account_id, account_name, opportunity_country, account_owner_name, account_owner_email,
             account_client_segment, account_sales_potential, account_found_in, account_source_type, opportunity_id,
             opportunity_name, opportunity_owner_email, opportunity_owner_name, opportunity_created_email,
             opportunity_created_name, opportunity_stage, opportunity_amount, opportunity_found_in,
             opportunity_close_reason, opportunity_cancel_reason, opportunity_stage_before_losing, opportunity_due_date,
             opportunity_losing_date, opportunity_created_date, activity_id, activity_result, activity_start_date,
             activity_due_date)
as
SELECT crm_account_opportunity.account_id,
       crm_account_opportunity.account_name,
       CASE
           WHEN COALESCE(crm_account_opportunity.opportunity_country, crm_account_opportunity.account_country)::text <>
                'GB'::text THEN COALESCE(crm_account_opportunity.opportunity_country,
                                         crm_account_opportunity.account_country)
           ELSE 'UK'::character varying
           END AS opportunity_country,
       crm_account_opportunity.account_owner_name,
       crm_account_opportunity.account_owner_email,
       crm_account_opportunity.account_client_segment,
       crm_account_opportunity.account_sales_potential,
       crm_account_opportunity.account_found_in,
       crm_account_opportunity.account_source_type,
       crm_account_opportunity.opportunity_id,
       crm_account_opportunity.opportunity_name,
       crm_account_opportunity.opportunity_owner_email,
       crm_account_opportunity.opportunity_owner_name,
       crm_account_opportunity.opportunity_created_email,
       crm_account_opportunity.opportunity_created_name,
       crm_account_opportunity.opportunity_stage,
       crm_account_opportunity.opportunity_amount,
       crm_account_opportunity.opportunity_found_in,
       crm_account_opportunity.opportunity_close_reason,
       crm_account_opportunity.opportunity_cancel_reason,
       crm_account_opportunity.opportunity_stage_before_losing,
       crm_account_opportunity.opportunity_due_date,
       crm_account_opportunity.opportunity_losing_date,
       crm_account_opportunity.opportunity_created_date,
       crm_account_opportunity.activity_id,
       crm_account_opportunity.activity_result,
       crm_account_opportunity.activity_start_date,
       crm_account_opportunity.activity_due_date
FROM aggregation.crm_account_opportunity
WHERE crm_account_opportunity.opportunity_created_date >= '2021-08-01 00:00:00'::timestamp without time zone
  AND crm_account_opportunity.opportunity_owner_email::text <> '<EMAIL>'::text
  AND (crm_account_opportunity.account_found_in IS NULL OR
       crm_account_opportunity.account_found_in::text <> 'Jooble перекуп'::text)
  AND (crm_account_opportunity.opportunity_cancel_reason IS NULL OR
       (crm_account_opportunity.opportunity_cancel_reason::text <> ALL
        (ARRAY ['Случайно запустил продажу'::character varying::text, 'Go to Sales Force'::character varying::text])))
   AND (COALESCE(crm_account_opportunity.opportunity_country, crm_account_opportunity.account_country)::text = ANY
       (ARRAY ['US'::character varying::text, 'DE'::character varying::text, 'NL'::character varying::text, 'GB'::character varying::text, 'FR'::character varying::text, 'PL'::character varying::text, 'CA'::character varying::text, 'AT'::character varying::text, 'CH'::character varying::text, 'BE'::character varying::text, 'BR'::character varying::text, 'IT'::character varying::text, 'SE'::character varying::text, 'ES'::character varying::text, 'CZ'::character varying::text, 'HU'::character varying::text, 'IN'::character varying::text, 'MX'::character varying::text, 'CL'::character varying::text, 'KZ'::character varying::text, 'AU'::character varying::text, 'SG'::character varying::text, 'DK'::character varying::text]))
 AND (crm_account_opportunity.opportunity_stage::text = ANY
       (ARRAY ['Cancelled'::character varying::text, 'Successfully completed'::character varying::text, 'Unsuccessfully completed'::character varying::text]));

alter table aggregation.v_crm_account_opportunity
    owner to mb;

grant select on aggregation.v_crm_account_opportunity to readonly;
