create view v_ga_goal_2022
            (country, country_name_eng, date, year, organic_sessions_wo_job_apply, jobs_apply, min_predict_traffic,
             max_predict_traffic, organic_revenue_usd, organic_search_revenue_usd)
as
WITH targets AS (
    SELECT f.country,
           f.target_month                AS date_diff,
           d.dt                          AS date,
           date_part('year'::text, d.dt) AS year,
           f.min_predict_traffic,
           f.max_predict_traffic
    FROM vnov.organic_search_traffic_fcst_2022 f
             JOIN dimension.info_calendar d ON d.date_diff = f.target_month
),
     ga_data AS (
         SELECT ga.country_code                  AS country,
                ga.date,
                date_part('year'::text, ga.date) AS year,
                ga.is_local,
                sum(
                        CASE
                            WHEN ga.channelgrouping::text = 'Organic Search'::text AND
                                 ga.sourcemedium::text !~~ '%google_jobs_apply%'::text THEN ga.session_cnt
                            ELSE 0::numeric
                            END)                 AS organic_sessions_wo_job_apply,
                sum(
                        CASE
                            WHEN ga.channelgrouping::text = 'Organic Search'::text AND
                                 ga.sourcemedium::text ~~ '%google_jobs_apply%'::text THEN ga.session_cnt
                            ELSE 0::numeric
                            END)                 AS jobs_apply
         FROM aggregation.v_ga_session_data ga
         WHERE ga.date >= '2020-01-01'::date
           AND ga.date <= '2022-12-31'::date
         GROUP BY ga.country_code, ga.date, (date_part('year'::text, ga.date)), ga.is_local
     ),
     adv_revenue AS (
         SELECT lower(c.alpha_2::text)        AS country,
                d.dt                          AS date,
                date_part('year'::text, d.dt) AS year,
                sum(
                        CASE
                            WHEN COALESCE(t.channel, 'Undefined'::text::character varying)::text =
                                 'Organic Search'::text AND rev.placement::text = 'search'::text THEN rev.revenue_usd
                            ELSE 0::numeric
                            END)              AS organic_search_revenue_usd,
                sum(
                        CASE
                            WHEN COALESCE(t.channel, 'Undefined'::text::character varying)::text = 'Organic Search'::text
                                THEN rev.revenue_usd
                            ELSE 0::numeric
                            END)              AS organic_revenue_usd
         FROM imp.adv_revenue_by_placement_and_src rev
                  LEFT JOIN dimension.countries c ON rev.country = c.id
                  LEFT JOIN dimension.info_calendar d ON rev.date_diff = d.date_diff
                  LEFT JOIN dimension.u_traffic_source t ON rev.country = t.country AND rev.id_traf_source = t.id
         WHERE d.dt >= '2021-02-01'::date
           AND d.dt <= '2022-12-31'::date
         GROUP BY (lower(c.alpha_2::text)), d.dt, (date_part('year'::text, d.dt))
     ),
     unions AS (
         SELECT t.country,
                t.date,
                t.year,
                NULL::bigint  AS organic_sessions_wo_job_apply,
                NULL::numeric AS jobs_apply,
                t.min_predict_traffic,
                t.max_predict_traffic,
                NULL::numeric AS organic_revenue_usd,
                NULL::numeric AS organic_search_revenue_usd
         FROM targets t
         UNION ALL
         SELECT g.country,
                g.date,
                g.year,
                g.organic_sessions_wo_job_apply,
                g.jobs_apply,
                NULL::bigint  AS min_predict_traffic,
                NULL::bigint  AS max_predict_traffic,
                NULL::numeric AS organic_revenue_usd,
                NULL::numeric AS organic_search_revenue_usd
         FROM ga_data g
         UNION ALL
         SELECT a.country,
                a.date,
                a.year,
                NULL::bigint  AS organic_sessions_wo_job_apply,
                NULL::numeric AS jobs_apply,
                NULL::bigint  AS min_predict_traffic,
                NULL::bigint  AS max_predict_traffic,
                a.organic_revenue_usd,
                a.organic_search_revenue_usd
         FROM adv_revenue a
         UNION ALL
         SELECT NULL::text    AS country,
                c.dt          AS date,
                c.dt_year     AS year,
                NULL::bigint  AS organic_sessions_wo_job_apply,
                NULL::numeric AS jobs_apply,
                NULL::bigint  AS min_predict_traffic,
                NULL::bigint  AS max_predict_traffic,
                NULL::numeric AS organic_revenue_usd,
                NULL::numeric AS organic_search_revenue_usd
         FROM dimension.info_calendar c
         WHERE c.dt >= '2022-01-01'::date
           AND c.dt <= '2022-12-31'::date
     )
SELECT uu.country,
       cc.name_country_eng AS country_name_eng,
       uu.date,
       uu.year,
       uu.organic_sessions_wo_job_apply,
       uu.jobs_apply,
       uu.min_predict_traffic,
       uu.max_predict_traffic,
       uu.organic_revenue_usd,
       uu.organic_search_revenue_usd
FROM unions uu
         LEFT JOIN dimension.countries cc ON lower(cc.alpha_2::text) = uu.country;

alter table v_ga_goal_2022
    owner to vnov;

grant select on v_ga_goal_2022 to readonly;
