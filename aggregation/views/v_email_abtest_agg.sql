create view v_email_abtest_agg
            (country, action_date, account_test_num, letter_type, is_mobile, value, metric_name, account_age,
             account_active, is_valid, union_group)
as
SELECT c.name_country_eng              AS country,
       eaa.action_date,
       eaa.account_test_num,
       eaa.letter_type,
       eaa.is_mobile,
       eaa.metric_cnt                  AS value,
       btrim(eaa.metric_name::text)    AS metric_name,
       btrim(eaa.account_age::text)    AS account_age,
       btrim(eaa.account_active::text) AS account_active,
       eaa.is_valid,
       1                               AS union_group
FROM aggregation.email_abtest_agg eaa
         LEFT JOIN dimension.countries c ON eaa.country_id = c.id
UNION ALL
SELECT c.name_country_eng              AS country,
       eaa.action_date,
       eaa.account_test_num,
       eaa.letter_type,
       eaa.is_mobile,
       eaa.metric_cnt                  AS value,
       btrim(eaa.metric_name::text)    AS metric_name,
       btrim(eaa.account_age::text)    AS account_age,
       btrim(eaa.account_active::text) AS account_active,
       eaa.is_valid,
       2                               AS union_group
FROM aggregation.email_abtest_agg eaa
         LEFT JOIN dimension.countries c ON eaa.country_id = c.id;

alter table v_email_abtest_agg
    owner to ono;

grant select on v_email_abtest_agg to readonly;

grant select on v_email_abtest_agg to tma;

