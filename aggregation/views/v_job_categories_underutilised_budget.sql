create or replace view aggregation.v_job_categories_underutilised_budget
as

WITH paid_jobs_by_category AS (SELECT jobs_stat_daily.date::date                                        AS date,
                                      jobs_stat_daily.id_country,
                                      countries.alpha_2                                                 AS country,
                                      COALESCE(jobs_stat_daily.job_category_id::integer, '-1'::integer) AS id_job_category,
                                      jobs_stat_daily.id_project,
                                      sum(jobs_stat_daily.paid_job_count)                               AS paid_jobs
                               FROM aggregation.jobs_stat_daily
                               LEFT JOIN dimension.countries
                                   ON jobs_stat_daily.id_country = countries.id
                               WHERE (jobs_stat_daily.date::date = ANY
                                      (ARRAY ['2023-07-31'::date, '2023-08-31'::date, '2023-09-30'::date, '2023-10-31'::date, '2023-11-30'::date, '2023-12-31'::date, '2024-01-31'::date, '2024-02-29'::date, '2024-03-31'::date]))
                               GROUP BY (jobs_stat_daily.date::date),
                                        jobs_stat_daily.id_country,
                                        countries.alpha_2,
                                        (COALESCE(jobs_stat_daily.job_category_id::integer, '-1'::integer)),
                                        jobs_stat_daily.id_project
                               HAVING sum(jobs_stat_daily.paid_job_count) > 0),

     paid_jobs_by_project AS (SELECT paid_jobs_by_category.date,
                                     paid_jobs_by_category.id_country,
                                     paid_jobs_by_category.country,
                                     paid_jobs_by_category.id_project,
                                     sum(paid_jobs_by_category.paid_jobs) AS total
                              FROM paid_jobs_by_category
                              GROUP BY paid_jobs_by_category.date,
                                       paid_jobs_by_category.id_project,
                                       paid_jobs_by_category.id_country,
                                       paid_jobs_by_category.country),

    percentage AS (SELECT paid_jobs_by_category.paid_jobs::numeric / paid_jobs_by_project.total AS percent,
                           paid_jobs_by_category.country,
                           paid_jobs_by_category.id_country,
                           paid_jobs_by_category.id_project,
                           paid_jobs_by_category.date,
                           paid_jobs_by_category.id_job_category
                    FROM paid_jobs_by_category
                             JOIN paid_jobs_by_project
                                  ON paid_jobs_by_category.id_project = paid_jobs_by_project.id_project
                                  AND paid_jobs_by_category.date = paid_jobs_by_project.date
                                  AND paid_jobs_by_category.id_country = paid_jobs_by_project.id_country),

     underutilised AS (SELECT v_budget_and_revenue.date,
                              v_budget_and_revenue.country,
                              countries.id                                                        AS id_country,
                              v_budget_and_revenue.id_project,
                              sum(v_budget_and_revenue.budget - v_budget_and_revenue.revenue_usd) AS underutilised_budget
                       FROM aggregation.v_budget_and_revenue
                       LEFT JOIN dimension.countries
                                ON lower(v_budget_and_revenue.country::text) = lower(countries.alpha_2::text)
                       WHERE (v_budget_and_revenue.date = ANY
                              (ARRAY ['2023-07-31'::date, '2023-08-31'::date, '2023-09-30'::date, '2023-10-31'::date, '2023-11-30'::date, '2023-12-31'::date, '2024-01-31'::date, '2024-02-29'::date, '2024-03-31'::date]))
                       GROUP BY v_budget_and_revenue.date,
                                v_budget_and_revenue.country,
                                countries.id,
                                v_budget_and_revenue.id_project
                       HAVING sum(v_budget_and_revenue.budget - v_budget_and_revenue.revenue_usd) > 0::double precision)
                       
SELECT underutilised.date,
       underutilised.country,
       underutilised.id_country,
       percentage.id_job_category,
       COALESCE(job_kaiju_category.child_name, 'Other'::text)                                AS child_category_name,
       COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text)                                                               AS parent_category_name,
       sum(underutilised.underutilised_budget *
           percentage.percent::double precision)::numeric(19, 4) AS underutilised_budget
FROM underutilised
    JOIN percentage
              ON underutilised.id_project = percentage.id_project
              AND underutilised.date = percentage.date
              AND percentage.id_country = underutilised.id_country
    LEFT JOIN (SELECT job_kaiju_category_1.id_child,
                           job_kaiju_category_1.child_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1
                    UNION
                    SELECT job_kaiju_category_1.id_parent,
                           job_kaiju_category_1.parent_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
                   ON percentage.id_job_category = job_kaiju_category.id_child
         LEFT JOIN aggregation.v_job_kaiju_category
                   ON job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
GROUP BY underutilised.date,
         underutilised.country,
         underutilised.id_country,
         percentage.id_job_category,
         COALESCE(job_kaiju_category.child_name, 'Other'::text),
         COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text);

