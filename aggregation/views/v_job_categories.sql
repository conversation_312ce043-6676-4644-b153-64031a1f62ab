create view aggregation.v_job_categories
            (country_id, country_name, date, revenue_usd, job_cnt, job_with_away_cnt, job_with_revenue_cnt, away_cnt,
             away_conversion_cnt, conversion_cnt, median_cpc_usd, job_project_id, job_project_name,
             avg_cpc_for_paid_job_count_usd, conversion_revenue_usd, child_category_name, parent_category_name,
             is_job_ad_exchange_project)
as
SELECT job_categories.country_id,
       job_categories.country_name,
       fn_get_timestamp_from_date_diff(job_categories.date_diff)                                                   AS date,
       job_categories.revenue_usd,
       job_categories.job_cnt,
       job_categories.job_with_away_cnt,
       job_categories.job_with_revenue_cnt,
       job_categories.away_cnt,
       job_categories.away_conversion_cnt,
       job_categories.conversion_cnt,
       job_categories.median_cpc_usd,
       job_categories.id_project                                                                                   AS job_project_id,
       info_project.name                                                                                           AS job_project_name,
       job_categories.avg_cpc_for_paid_job_count_usd,
       job_categories.conversion_revenue_usd,
       COALESCE(job_kaiju_category.child_name, 'Other'::text)                                                      AS child_category_name,
       COALESCE(COALESCE(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name),
                'Other'::text)                                                                                     AS parent_category_name,
       CASE
           WHEN info_project.hide_in_search THEN 1
           ELSE 0
           END                                                                                                     AS is_job_ad_exchange_project
FROM aggregation.job_categories
         LEFT JOIN dimension.info_project
                   ON job_categories.country_id = info_project.country AND job_categories.id_project = info_project.id
         LEFT JOIN (SELECT job_kaiju_category_1.id_child,
                           job_kaiju_category_1.child_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1
                    UNION
                    SELECT job_kaiju_category_1.id_parent,
                           job_kaiju_category_1.parent_name
                    FROM dimension.job_kaiju_category job_kaiju_category_1) job_kaiju_category
                   ON job_categories.id_category = job_kaiju_category.id_child
         LEFT JOIN aggregation.v_job_kaiju_category
                   ON job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE fn_get_timestamp_from_date_diff(job_categories.date_diff) >= (CURRENT_DATE - 90);

alter table aggregation.v_job_categories
    owner to ono;

