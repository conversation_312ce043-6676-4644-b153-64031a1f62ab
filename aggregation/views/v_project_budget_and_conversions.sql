create view aggregation.v_project_budget_and_conversions
            (country_id, project_id, country, monthly_budget, budget_left, avg_cpc_usd, cpa_current, conv_rate_current,
             cpa_90_d, conv_rate_90_d, cpa_target, conv_rate_target)
as
with
    conv_raw as (
        select
            countries.alpha_2 as country,
            pcd.country_id,
            pcd.project_id,
            case
                when sum(pcd.aways) = 0 then 0::numeric
                else 1.0 * sum(pcd.conversions)::numeric / sum(pcd.aways)::numeric
                end           as conv_rate_90_d,
            case
                when sum(pcd.conversions) = 0 then 0::numeric
                else sum(pcd.away_revenue) / sum(pcd.conversions)::numeric
                end           as cpa_90_d,
            case
                when sum(
                             case
                                 when pcd.session_date >=
                                      date_trunc('month'::text, current_date::timestamp with time zone)::date
                                     then pcd.aways
                                 else null::integer
                                 end) = 0 then 0::numeric
                else 1.0 * sum(
                        case
                            when pcd.session_date >= date_trunc('month'::text, current_date::timestamp with time zone)::date
                                then pcd.conversions
                            else null::integer
                            end)::numeric / sum(
                             case
                                 when pcd.session_date >=
                                      date_trunc('month'::text, current_date::timestamp with time zone)::date
                                     then pcd.aways
                                 else null::integer
                                 end)::numeric
                end           as conv_rate_current,
            case
                when sum(
                             case
                                 when pcd.session_date >=
                                      date_trunc('month'::text, current_date::timestamp with time zone)::date
                                     then pcd.conversions
                                 else null::integer
                                 end) = 0 then 0::numeric
                else sum(
                             case
                                 when pcd.session_date >=
                                      date_trunc('month'::text, current_date::timestamp with time zone)::date
                                     then pcd.away_revenue
                                 else null::numeric
                                 end) / sum(
                             case
                                 when pcd.session_date >=
                                      date_trunc('month'::text, current_date::timestamp with time zone)::date
                                     then pcd.conversions
                                 else null::integer
                                 end)::numeric
                end           as cpa_current
        from
            aggregation.project_conversions_daily pcd
            join aggregation.v_postback_projects pp
                 on pp.id_country = pcd.country_id and pp.project_id = pcd.project_id and pp.conversion_cnt > 5 and
                    pcd.session_date >= fn_get_date_from_date_diff(pp.conversion_start_date_diff) and
                    pcd.session_date <= fn_get_date_from_date_diff(pp.conversion_max_date_diff + 7)
            left join dimension.countries
                      on pcd.country_id = countries.id
        where
            pcd.session_date >= (current_date - 90)
        group by pcd.country_id, pcd.project_id, countries.alpha_2
    ),
    conversions as (
        select
            cr.country_id,
            cr.country,
            cr.project_id,
            cr.conv_rate_current,
            cr.cpa_current,
            cr.conv_rate_90_d,
            cr.cpa_90_d,
            crm.benchmark_cr,
            crm.benchmark_cpa
        from
            conv_raw cr
            left join (select
                           crm_client_account.soska_project_link,
                           "left"(crm_client_account.soska_project_link::text, 2)                                                as country,
                           substr(crm_client_account.soska_project_link::text, 3,
                                  length(crm_client_account.soska_project_link::text) -
                                  2)                                                                                             as id_project,
                           crm_client_account.benchmark_cr,
                           crm_client_account.benchmark_cpa,
                           crm_client_account.target_action,
                           row_number()
                           over (partition by crm_client_account.soska_project_link order by crm_client_account.created_on desc) as num
                       from
                           aggregation.crm_client_account
                       where
                             (crm_client_account.benchmark_cr > 0::numeric or
                              crm_client_account.benchmark_cpa > 0::numeric)
                         and crm_client_account.soska_project_link is not null
                         and length(crm_client_account.soska_project_link::text) > 2) crm
                      on cr.country::text = crm.country and
                         cr.project_id = nullif(crm.id_project, ''::text)::integer and crm.num = 1
    ),
    budgets_raw as (
        select
            brda.country_id,
            brda.project_id,
            max(brda.is_unlim_cmp)              as is_unlim_cmp,
            sum(brda.revenue_usd)               as revenue_usd,
            max(brda.user_budget_month_usd)     as user_budget_month_usd,
            sum(brda.campaign_budget_month_usd) as campaign_budget_month_usd,
            count(distinct brda.id_campaign)    as campaign_cnt,
            count(distinct
                  case
                      when coalesce(brda.campaign_budget_month_usd, 0::double precision) > 0::double precision
                          then brda.id_campaign
                      else null::character varying
                      end)                      as campaign_with_budget_cnt
        from
            aggregation.budget_revenue_daily_agg brda
        where
                brda.action_date = ((select
                                         max(budget_revenue_daily_agg.action_date) as max
                                     from
                                         aggregation.budget_revenue_daily_agg))
        group by brda.country_id, brda.project_id
    ),
    budgets as (
        select
            budgets_raw.country_id,
            budgets_raw.project_id,
            budgets_raw.revenue_usd,
            case
                when budgets_raw.campaign_cnt = budgets_raw.campaign_with_budget_cnt and
                     budgets_raw.campaign_budget_month_usd > 0::double precision and
                     budgets_raw.campaign_budget_month_usd < budgets_raw.user_budget_month_usd
                    then budgets_raw.campaign_budget_month_usd
                when budgets_raw.is_unlim_cmp = 1 and
                     budgets_raw.user_budget_month_usd > budgets_raw.campaign_budget_month_usd
                    then budgets_raw.user_budget_month_usd
                when budgets_raw.user_budget_month_usd = 0::double precision then budgets_raw.campaign_budget_month_usd
                else budgets_raw.user_budget_month_usd
                end as monthly_budget
        from
            budgets_raw
    ),
    jobs_data as (
        select
            jobs_stat_daily.id_country,
            jobs_stat_daily.id_project,
            sum(jobs_stat_daily.avg_cpc_for_paid_job_count_usd * jobs_stat_daily.paid_job_count::numeric) /
            sum(jobs_stat_daily.paid_job_count)::numeric as avg_cpc_usd
        from
            aggregation.jobs_stat_daily
        where
                jobs_stat_daily.date::date >= date_trunc('month'::text, current_date::timestamp with time zone)::date
        group by jobs_stat_daily.id_country, jobs_stat_daily.id_project
        having
            sum(jobs_stat_daily.paid_job_count) > 0
    )
select
    c.country_id,
    c.project_id,
    c.country,
    b.monthly_budget,
    case
        when (b.monthly_budget - coalesce(b.revenue_usd, 0::double precision)) < 0::double precision
            then 0::double precision
        else b.monthly_budget - coalesce(b.revenue_usd, 0::double precision)
        end                            as budget_left,
    j.avg_cpc_usd,
    c.cpa_current,
    100::numeric * c.conv_rate_current as conv_rate_current,
    c.cpa_90_d,
    100::numeric * c.conv_rate_90_d    as conv_rate_90_d,
    c.benchmark_cpa                    as cpa_target,
    c.benchmark_cr                     as conv_rate_target
from
    conversions c
    left join budgets b
              on c.country_id = b.country_id and c.project_id = b.project_id
    left join jobs_data j
              on j.id_country = c.country_id and j.id_project = c.project_id;
