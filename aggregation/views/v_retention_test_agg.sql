create view aggregation.v_retention_test_agg
            (country, country_id, test_group_list, is_new_user, is_mobile, is_local, session_create_page_type_id,
             first_session_datediff, first_test_session_datediff, user_cnt, user_retained_day_1_3_cnt,
             user_retained_day_1_7_cnt, user_retained_day_1_14_cnt, user_managed_retained_day_1_3_cnt,
             user_managed_retained_day_1_7_cnt, user_managed_retained_day_1_14_cnt, user_unmanaged_retained_day_1_3_cnt,
             user_unmanaged_retained_day_1_7_cnt, user_unmanaged_retained_day_1_14_cnt,
             user_artificial_retained_day_1_3_cnt, user_artificial_retained_day_1_7_cnt,
             user_artificial_retained_day_1_14_cnt, type, first_session_date)
as
WITH retention AS (SELECT retention_test_agg.country_id,
                          retention_test_agg.test_group_list,
                          retention_test_agg.is_new_user,
                          retention_test_agg.is_mobile,
                          retention_test_agg.is_local,
                          retention_test_agg.session_create_page_type_id,
                          retention_test_agg.first_session_datediff,
                          retention_test_agg.first_test_session_datediff,
                          retention_test_agg.user_cnt,
                          retention_test_agg.user_retained_day_1_3_cnt,
                          retention_test_agg.user_retained_day_1_7_cnt,
                          retention_test_agg.user_retained_day_1_14_cnt,
                          retention_test_agg.user_managed_retained_day_1_3_cnt,
                          retention_test_agg.user_managed_retained_day_1_7_cnt,
                          retention_test_agg.user_managed_retained_day_1_14_cnt,
                          retention_test_agg.user_unmanaged_retained_day_1_3_cnt,
                          retention_test_agg.user_unmanaged_retained_day_1_7_cnt,
                          retention_test_agg.user_unmanaged_retained_day_1_14_cnt,
                          retention_test_agg.user_artificial_retained_day_1_3_cnt,
                          retention_test_agg.user_artificial_retained_day_1_7_cnt,
                          retention_test_agg.user_artificial_retained_day_1_14_cnt,
                          'union 1'::text AS type
                   FROM aggregation.retention_test_agg
                   WHERE retention_test_agg.first_session_datediff >=
                         (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 30)
                   UNION ALL
                   SELECT retention_test_agg.country_id,
                          retention_test_agg.test_group_list,
                          retention_test_agg.is_new_user,
                          retention_test_agg.is_mobile,
                          retention_test_agg.is_local,
                          retention_test_agg.session_create_page_type_id,
                          retention_test_agg.first_session_datediff,
                          retention_test_agg.first_test_session_datediff,
                          retention_test_agg.user_cnt,
                          retention_test_agg.user_retained_day_1_3_cnt,
                          retention_test_agg.user_retained_day_1_7_cnt,
                          retention_test_agg.user_retained_day_1_14_cnt,
                          retention_test_agg.user_managed_retained_day_1_3_cnt,
                          retention_test_agg.user_managed_retained_day_1_7_cnt,
                          retention_test_agg.user_managed_retained_day_1_14_cnt,
                          retention_test_agg.user_unmanaged_retained_day_1_3_cnt,
                          retention_test_agg.user_unmanaged_retained_day_1_7_cnt,
                          retention_test_agg.user_unmanaged_retained_day_1_14_cnt,
                          retention_test_agg.user_artificial_retained_day_1_3_cnt,
                          retention_test_agg.user_artificial_retained_day_1_7_cnt,
                          retention_test_agg.user_artificial_retained_day_1_14_cnt,
                          'union 2'::text AS type
                   FROM aggregation.retention_test_agg
                   WHERE retention_test_agg.first_session_datediff >=
                         (fn_get_date_diff(CURRENT_DATE::timestamp without time zone) - 30))
SELECT countries.name_country_eng                                        AS country,
       retention.country_id,
       retention.test_group_list,
       retention.is_new_user,
       retention.is_mobile,
       retention.is_local,
       retention.session_create_page_type_id,
       retention.first_session_datediff,
       retention.first_test_session_datediff,
       retention.user_cnt,
       retention.user_retained_day_1_3_cnt,
       retention.user_retained_day_1_7_cnt,
       retention.user_retained_day_1_14_cnt,
       retention.user_managed_retained_day_1_3_cnt,
       retention.user_managed_retained_day_1_7_cnt,
       retention.user_managed_retained_day_1_14_cnt,
       retention.user_unmanaged_retained_day_1_3_cnt,
       retention.user_unmanaged_retained_day_1_7_cnt,
       retention.user_unmanaged_retained_day_1_14_cnt,
       retention.user_artificial_retained_day_1_3_cnt,
       retention.user_artificial_retained_day_1_7_cnt,
       retention.user_artificial_retained_day_1_14_cnt,
       retention.type,
       fn_get_timestamp_from_date_diff(retention.first_session_datediff) AS first_session_date
FROM retention
         JOIN dimension.countries ON retention.country_id = countries.id;

alter table aggregation.v_retention_test_agg
    owner to ono;

grant select on aggregation.v_retention_test_agg to readonly;

grant select on aggregation.v_retention_test_agg to user_agg_team;

grant select on aggregation.v_retention_test_agg to readonly_aggregation;

grant select on aggregation.v_retention_test_agg to "pavlo.kvasnii";

