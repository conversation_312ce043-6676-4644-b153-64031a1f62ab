create view aggregation.v_retention_agg
            (first_user_visit_date, country, groups, is_mobile, is_local, session_create_page_type, first_channel,
             current_channel, session_cnt, user_cnt, week_retention_user_cnt, two_week_retention_user_cnt,
             three_week_retention_user_cnt, four_retention_user_cnt, month_retention_user_cnt)
as
SELECT fn_get_timestamp_from_date_diff(retention_agg.first_visit_date_diff) AS first_user_visit_date,
       countries.name_country_eng                                           AS country,
       retention_agg.groups,
       retention_agg.is_mobile,
       retention_agg.is_local,
       retention_agg.session_create_page_type,
       u_traffic_source.channel                                             AS first_channel,
       u_current_traffic_source.channel                                     AS current_channel,
       sum(retention_agg.session_cnt)                                       AS session_cnt,
       sum(retention_agg.user_cnt)                                          AS user_cnt,
       sum(retention_agg.week_retention_user_cnt)                           AS week_retention_user_cnt,
       sum(retention_agg.second_week_retention_user_cnt)                    AS two_week_retention_user_cnt,
       sum(retention_agg.third_week_retention_user_cnt)                     AS three_week_retention_user_cnt,
       sum(retention_agg.fourth_week_retention_user_cnt)                    AS four_retention_user_cnt,
       sum(retention_agg.month_retention_user_cnt)                          AS month_retention_user_cnt
FROM aggregation.retention_agg
         LEFT JOIN dimension.countries ON retention_agg.country_id = countries.id
         LEFT JOIN dimension.u_traffic_source ON retention_agg.country_id = u_traffic_source.country AND
                                                 retention_agg.id_traffic_source = u_traffic_source.id
         LEFT JOIN dimension.u_traffic_source u_current_traffic_source
                   ON retention_agg.country_id = u_current_traffic_source.country AND
                      retention_agg.id_current_traffic_source = u_current_traffic_source.id
GROUP BY (fn_get_timestamp_from_date_diff(retention_agg.first_visit_date_diff)), countries.name_country_eng,
         retention_agg.groups, retention_agg.is_mobile, retention_agg.is_local, retention_agg.session_create_page_type,
         u_traffic_source.channel, u_current_traffic_source.channel;

alter table aggregation.v_retention_agg
    owner to ono;

grant select on aggregation.v_retention_agg to readonly;

grant select on aggregation.v_retention_agg to readonly_aggregation;

grant select on aggregation.v_retention_agg to "pavlo.kvasnii";

