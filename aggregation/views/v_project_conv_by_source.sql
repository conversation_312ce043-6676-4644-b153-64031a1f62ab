create view aggregation.v_project_conv_by_source
            (country, id_project, project_name, traffic_source, min_date, max_date, away_revenue, aways, conversions,
             cpa, conversion_rate)
as
with
    conversion_source as (
        select
            conversions.date,
            conversions.country,
            conversions.id_project                  as project_id,
            info_project.name                       as project_name,
            conversions_source.id_traffic_source,
            max(conversions.conversions)            as total_conversions,
            sum(conversions_source.cnt_conversions) as conversions
        from
            imp_statistic.conversions
            join imp_statistic.conversions_source
                 on conversions.id = conversions_source.id_conversion
            left join dimension.countries countries_1
                      on conversions.country::text = countries_1.alpha_2::text
            left join dimension.info_project
                      on countries_1.id = info_project.country and conversions.id_project = info_project.id
            left join dimension.u_traffic_source
                      on countries_1.id = u_traffic_source.country and
                         conversions_source.id_traffic_source = u_traffic_source.id
        where
              conversions.id_date_period = 1
          and conversions.date >= (current_date - 30)
        group by
            conversions.date, conversions.country, conversions.id_project, info_project.name,
            conversions_source.id_traffic_source
    ),
    source_revenue as (
        select
            jdp_away_clicks_agg.country_id,
            fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff) as date,
            jdp_away_clicks_agg.project_id                                       as id_project,
            jdp_away_clicks_agg.project_name                                     as site,
            jdp_away_clicks_agg.id_current_traf_source                           as id_traf_source,
            sum(jdp_away_clicks_agg.jdp_away_count)                              as click_count,
            sum(jdp_away_clicks_agg.revenue_usd)                                 as total_value
        from
            aggregation.jdp_away_clicks_agg
        where
              jdp_away_clicks_agg.away_type::text <> 'JDP only'::text
          and jdp_away_clicks_agg.action_datediff >= (fn_get_date_diff(current_date::timestamp without time zone) - 30)
        group by
            jdp_away_clicks_agg.country_id, (fn_get_timestamp_from_date_diff(jdp_away_clicks_agg.action_datediff)),
            jdp_away_clicks_agg.project_id, jdp_away_clicks_agg.project_name, jdp_away_clicks_agg.id_current_traf_source
    ),
    conversion_source_agg as (
        select
            countries.alpha_2                 as country,
            revenue.id_project,
            revenue.site                      as project_name,
            u_traffic_source.name             as traffic_source,
            revenue.date,
            sum(revenue.total_value)          as away_revenue,
            sum(revenue.click_count)          as aways,
            max(conversion_total.conversions) as conversions
        from
            source_revenue revenue
            join dimension.countries
                 on revenue.country_id = countries.id
            left join conversion_source conversion_total
                      on countries.alpha_2::text = conversion_total.country::text and
                         revenue.id_project = conversion_total.project_id and revenue.date = conversion_total.date and
                         revenue.id_traf_source = conversion_total.id_traffic_source
            left join dimension.u_traffic_source
                      on revenue.country_id = u_traffic_source.country and revenue.id_traf_source = u_traffic_source.id
        where
            (concat(countries.alpha_2, revenue.id_project) in (select distinct
                                                                   concat(conversion_source.country, conversion_source.project_id) as concat
                                                               from
                                                                   conversion_source))
        group by countries.alpha_2, revenue.site, revenue.id_project, u_traffic_source.name, revenue.date
    ),
    t as (
        select
            c.alpha_2                                   as country,
            pcd.project_id                              as id_project,
            pcd.project_name,
            u_traffic_source.name                       as traffic_source,
            min(pcd.session_date)                       as min_date,
            max(pcd.session_date)                       as max_date,
            sum(coalesce(pcd.away_revenue, 0::numeric)) as away_revenue,
            sum(coalesce(pcd.aways, 0))                 as aways,
            sum(coalesce(pcd.conversions, 0))           as conversions
        from
            aggregation.project_conversions_daily pcd
            join dimension.countries c
                 on c.id = pcd.country_id
            join aggregation.v_postback_projects vpp
                 on vpp.id_country = pcd.country_id and vpp.project_id = pcd.project_id and
                    vpp.conversion_start_date_diff <=
                    fn_get_date_diff(pcd.session_date::timestamp without time zone) and vpp.conversion_cnt >= 5 and
                    (vpp.conversion_max_date_diff + 7) >=
                    fn_get_date_diff(pcd.session_date::timestamp without time zone)
            left join dimension.u_traffic_source
                      on pcd.country_id = u_traffic_source.country and pcd.id_current_traf_source = u_traffic_source.id
        where
              pcd.metric::text = 'aways'::text
          and pcd.session_date >= (current_date - 30)
        group by c.alpha_2, pcd.project_id, pcd.project_name, u_traffic_source.name
        union all
        select
            conversion_source_agg.country,
            conversion_source_agg.id_project,
            conversion_source_agg.project_name,
            conversion_source_agg.traffic_source,
            min(conversion_source_agg.date)         as min_date,
            max(conversion_source_agg.date)         as max_date,
            sum(conversion_source_agg.away_revenue) as away_revenue,
            sum(conversion_source_agg.aways)        as aways,
            sum(conversion_source_agg.conversions)  as conversions
        from
            conversion_source_agg
        group by
            conversion_source_agg.country, conversion_source_agg.id_project, conversion_source_agg.project_name,
            conversion_source_agg.traffic_source
    )
select
    t.country,
    t.id_project,
    t.project_name,
    t.traffic_source,
    t.min_date,
    t.max_date,
    t.away_revenue,
    t.aways,
    t.conversions,
    case
        when t.conversions > 0::numeric then t.away_revenue / t.conversions
        else null::numeric
        end as cpa,
    case
        when t.aways > 0::numeric then 1.0 * t.conversions / t.aways
        else null::numeric
        end as conversion_rate
from
    t;
