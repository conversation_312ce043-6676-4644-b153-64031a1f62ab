create or replace view aggregation.v_salary_page_bounce_daily_agg(country_name, action_date, salary_page_type_id, traffic_source_ga, device_category, avg_session_duration, bounce_cnt, session_cnt) as
	SELECT c.name_country_eng AS country_name,
    glpb.action_date,
        CASE
            WHEN glpb.landing_page_path::text = '/salary'::text THEN 1
            ELSE 2
        END AS salary_page_type_id,
    glpb.source AS traffic_source_ga,
    glpb.device_category,
    sum(glpb.avg_session_duration) / sum(glpb.sessions)::numeric AS avg_session_duration,
    sum(glpb.bounces) AS bounce_cnt,
    sum(glpb.sessions) AS session_cnt
   FROM imp_api.ga_landing_page_bounce glpb
     LEFT JOIN dimension.countries c ON glpb.country_id = c.id
  WHERE (glpb.country_id = ANY (ARRAY[1, 2, 3, 11, 4, 7, 9, 18, 19, 20, 21, 26, 33, 35, 36, 39, 10, 51, 17, 55, 57, 59, 60, 61, 64, 66, 5, 6, 12, 13, 14, 15, 16])) AND glpb.action_date >= '2022-12-05'::date AND glpb.action_date <= '2023-06-30'::date
  GROUP BY c.name_country_eng, glpb.action_date, (
        CASE
            WHEN glpb.landing_page_path::text = '/salary'::text THEN 1
            ELSE 2
        END), glpb.source, glpb.device_category
UNION ALL
 SELECT c.name_country_eng AS country_name,
    ga4.action_date,
        CASE
            WHEN ga4.landing_page::text = '/salary'::text THEN 1
            ELSE 2
        END AS salary_page_type_id,
    ga4.session_source AS traffic_source_ga,
    ga4.device_category,
    sum(ga4.avg_session_duration * ga4.sessions::numeric) / sum(ga4.sessions)::numeric AS avg_session_duration,
    sum(ga4.bounce_rate * ga4.sessions::numeric) AS bounce_cnt,
    sum(ga4.sessions) AS session_cnt
   FROM imp_api.ga4_landing_page_bounce ga4
     LEFT JOIN dimension.countries c ON ga4.country_id = c.id
  WHERE (ga4.country_id = ANY (ARRAY[1, 2, 3, 11, 4, 7, 9, 18, 19, 20, 21, 26, 33, 35, 36, 39, 10, 51, 17, 55, 57, 59, 60, 61, 64, 66, 5, 6, 12, 13, 14, 15, 16])) AND ga4.action_date >= '2023-07-01'::date
  GROUP BY c.name_country_eng, ga4.action_date, (
        CASE
            WHEN ga4.landing_page::text = '/salary'::text THEN 1
            ELSE 2
        END), ga4.session_source, ga4.device_category;

alter table  aggregation.v_salary_page_bounce_daily_agg owner to dap;

grant select on  aggregation.v_salary_page_bounce_daily_agg to readonly;

grant select on  aggregation.v_salary_page_bounce_daily_agg to writeonly_pyscripts;

