create view aggregation.v_campaign_daily_metrics
            (country, id_user, id_project, id_campaign, campaign, site, currency, date, paid_job_count,
             organic_job_count, avg_cpc_for_paid_job_count_usd, flag_32_jobs_count, user_budget_month_usd,
             campaign_budget, campaign_daily_budget, is_price_per_job, campaign_flags, campaign_status, revenue_usd,
             click_count, is_unlim_cmp)
as
WITH auction AS (SELECT auction_click_statistic_analytics_1.id_user,
                        auction_click_statistic_analytics_1.id_project,
                        auction_click_statistic_analytics_1.id_campaign,
                        auction_click_statistic_analytics_1.campaign,
                        auction_click_statistic_analytics_1.site,
                        auction_click_statistic_analytics_1.currency,
                        auction_click_statistic_analytics_1.date,
                        auction_click_statistic_analytics_1.country_id,
                        sum(auction_click_statistic_analytics_1.total_value)      AS total_value,
                        sum(auction_click_statistic_analytics_1.click_count)      AS click_count,
                        sum(auction_click_statistic_analytics_1.test_count)       AS test_count,
                        sum(auction_click_statistic_analytics_1.duplicated_count) AS duplicated_count
                 FROM aggregation.auction_click_statistic_analytics auction_click_statistic_analytics_1
                 WHERE auction_click_statistic_analytics_1.date::text >= '2022-09-06'::text
                 GROUP BY auction_click_statistic_analytics_1.id_user, auction_click_statistic_analytics_1.id_project,
                          auction_click_statistic_analytics_1.id_campaign, auction_click_statistic_analytics_1.campaign,
                          auction_click_statistic_analytics_1.site, auction_click_statistic_analytics_1.currency,
                          auction_click_statistic_analytics_1.date, auction_click_statistic_analytics_1.country_id)
SELECT countries.alpha_2                                                             AS country,
       auction_click_statistic_analytics.id_user,
       auction_click_statistic_analytics.id_project,
       auction_click_statistic_analytics.id_campaign,
       auction_click_statistic_analytics.campaign,
       auction_click_statistic_analytics.site,
       auction_click_statistic_analytics.currency,
       auction_click_statistic_analytics.date::date                                  AS date,
       jobs_stat_daily.paid_job_count,
       jobs_stat_daily.organic_job_count,
       jobs_stat_daily.avg_cpc_for_paid_job_count_usd,
       jobs_stat_daily.flag_32_jobs_count,
       COALESCE(budget_revenue_daily_agg.user_budget_month_usd, 0::double precision) AS user_budget_month_usd,
       COALESCE(jobs_stat_daily.campaign_budget, 0::numeric)                         AS campaign_budget,
       COALESCE(jobs_stat_daily.campaign_daily_budget, 0::numeric)                   AS campaign_daily_budget,
       jobs_stat_daily.is_price_per_job,
       jobs_stat_daily.campaign_flags,
       jobs_stat_daily.campaign_status,
       auction_click_statistic_analytics.total_value + COALESCE(- (auction_click_statistic_analytics.total_value -
                                                                   (auction_click_statistic_analytics.click_count -
                                                                    COALESCE(auction_click_statistic_analytics.test_count, 0::bigint) -
                                                                    COALESCE(
                                                                            auction_click_statistic_analytics.duplicated_count,
                                                                            0::bigint))::numeric *
                                                                   (auction_click_statistic_analytics.total_value /
                                                                    COALESCE(auction_click_statistic_analytics.click_count, 1::bigint)::numeric)),
                                                                0::numeric)          AS revenue_usd,
       auction_click_statistic_analytics.click_count,
       COALESCE(budget_revenue_daily_agg.is_unlim_cmp, 0)                            AS is_unlim_cmp
FROM auction auction_click_statistic_analytics
         LEFT JOIN aggregation.jobs_stat_daily
                   ON jobs_stat_daily.id_country = auction_click_statistic_analytics.country_id AND
                      jobs_stat_daily.id_project = auction_click_statistic_analytics.id_project AND
                      jobs_stat_daily.date::text = auction_click_statistic_analytics.date::text AND
                      jobs_stat_daily.id_campaign = auction_click_statistic_analytics.id_campaign
         LEFT JOIN dimension.countries ON auction_click_statistic_analytics.country_id = countries.id
         LEFT JOIN aggregation.budget_revenue_daily_agg
                   ON jobs_stat_daily.id_country = budget_revenue_daily_agg.country_id AND
                      jobs_stat_daily.id_project = budget_revenue_daily_agg.project_id AND
                      jobs_stat_daily.date::date = budget_revenue_daily_agg.action_date
WHERE auction_click_statistic_analytics.date::text >= '2022-09-06'::text;

alter table aggregation.v_campaign_daily_metrics
    owner to ono;

grant select on aggregation.v_campaign_daily_metrics to user_agg_team;

