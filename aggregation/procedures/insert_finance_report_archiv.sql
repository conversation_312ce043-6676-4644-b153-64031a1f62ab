create procedure aggregation.insert_finance_report_archive(_datediff integer)
    language plpgsql
as
$$
begin

            truncate table aggregation.finance_report;


            insert into aggregation.finance_report(date, year_date, quarter_date, month_date, week_date, country, country_code,
                                                   revenue_type, revenue_usd, test_revenue_usd, adv_impression, adv_view, adv_click,
                                                   project_site, manager_name, cost_type, ga_channel, cost_usd, ga_users, ga_session,
                                                   project_site_budget_usd)
            With ea_sales_usd as (
                SELECT CAST(employer_balance_history.date_created AS date)     AS date,
                       countries.name_country_eng                              AS country,
                       countries.alpha_2 as country_code,
                       (employer_balance_history.value / currency_source.to_usd) /
                       (employer_balance.vat / 100 + 1)                        AS value_usd,
                       employer_balance_history.change_type,
                       employer_balance_history.value                          AS in_local_currency,
                       info_currency.iso_code                                  as currency,
                       employer_balance.vat,
                       employer_balance_history.value / currency_source.to_usd AS in_usd_with_vat,
                       employer_balance_history.sources                        as server,
                       employer_balance_history.id_employer
                FROM imp_employer.employer_balance_history
                         LEFT OUTER JOIN
                     imp_employer.employer_balance
                     ON employer_balance.sources = employer_balance_history.sources
                         AND employer_balance.id_employer = employer_balance_history.id_employer
                         LEFT OUTER JOIN
                     imp_employer.info_currency
                     on employer_balance.sources = info_currency.sources
                         and employer_balance.id_currency = info_currency.id
                         left join imp_statistic.currency_source
                                   ON currency_source.date = CAST(employer_balance_history.date_created AS date)
                                       AND currency_source.currency = info_currency.iso_code

                         LEFT OUTER JOIN
                     imp_employer.employer ON employer.id = employer_balance_history.id_employer
                         AND employer.sources = employer_balance_history.sources
                         left join dimension.countries
                                   on lower(employer.country_code) = lower(countries.alpha_2)
                where employer_balance_history.change_type = 0
            ),


                 Revenue as (
                     SELECT create_date                                   as date,
                            coalesce(countries.name_country_eng, 'Other') as country,
                            coalesce(countries.alpha_2, 'Other')          as country_code,
                            'AdSense for Search'                          as Revenue_type,
                            estimated_income_usd                          as revenue,
                            0                                             as total_test_value,
                            show                                          as impressions,
                            views_page                                    as views,
                            click                                         as clicks,
                            null                                          as manager_name,
                            null                                          as project_site,
                            0                                             as user_budget_usd
                     FROM imp_statistic.adsense_afs
                              left join dimension.info_country_adsense
                                        on adsense_afs.country = info_country_adsense.country_report_name
                              left join
                          dimension.countries
                          on lower(info_country_adsense.country_code) = lower(countries.alpha_2)
                     union all
                     Select create_date,
                            case
                                when site_address = 'jooble.org' then 'United States'
                                else coalesce(countries.name_country_eng, 'Other') end,
                            case
                                when site_address = 'jooble.org' then 'US'
                                else coalesce(countries.alpha_2, 'Other') end,
                            'AdSense for Content',
                            estimated_income_usd,
                            0 as total_test_value,
                            show      as impressions,
                            view_page as views,
                            click     as clicks,
                            null      as manager_name,
                            null      as project_site,
                            0         as user_budget_usd
                     from imp_statistic.adsense_afc
                              left join dimension.countries
                                        on left(adsense_afc.site_address, 2) = lower(countries.alpha_2)
                     union all
                     select cast(auction_click_statistic.date as date) as date,
                            name_country_eng,
                            countries.alpha_2,
                            'Auction',
                            auction_click_statistic.total_value,
                            COALESCE( (auction_click_statistic.total_value -
                                                                              (auction_click_statistic.click_count -
                                                                               auction_click_statistic.test_count -
                                                                               auction_click_statistic.to_jdp_count) *
                                                                              (auction_click_statistic.total_value /
                                                                               NULLIF(auction_click_statistic.click_count -
                                                                                      auction_click_statistic.to_jdp_count, 0))), 0)
                                                                       as total_test_value,
                            0                                          as impressions,
                            0                                          as views,
                            click_count                                as clicks,
                            crm_manageranddomain.manager_name          as manager_name,
                            auction_click_statistic.site               as project_site,
                            budget_and_revenue.user_budget_usd
                     from archive.auction_click_statistic
                              left join dimension.countries
                                        on auction_click_statistic.country = countries.alpha_2
                              left join imp_statistic.crm_manageranddomain
                                        on auction_click_statistic.site = trim(crm_manageranddomain.domain_name)
                              left join (
                                  Select action_date as date,
                                         country_code as country,
                                         user_id as id_user ,
                                         case when max(is_unlim_cmp) = 1 then 0
                                              when max(user_budget_month_usd) = 0 and sum(campaign_budget_month_usd) = 0 then 0
                                              when max(user_budget_month_usd) > sum(campaign_budget_month_usd) then max(user_budget_month_usd)
                                         else sum(campaign_budget_month_usd) end as user_budget_usd
                                  from aggregation.budget_revenue_daily_agg
                                  group by action_date,
                                           country,
                                           user_id
                         ) budget_and_revenue
                                        on auction_click_statistic.country = budget_and_revenue.country
                                         and    auction_click_statistic.date = budget_and_revenue.date
                                        and auction_click_statistic.id_user =  budget_and_revenue.id_user

                     union all
                     Select date,
                            country,
                            country_code,
                            'Employer Account' as revenue_type,
                            sum(value_usd)     as revenue,
                            0                  as total_test_value,
                            0                  as impressions,
                            0                  as views,
                            0                  as clicks,
                            null               as manager_name,
                            null               as project_site,
                            0                  as user_budget_usd
                     from ea_sales_usd

                     group by date,
                              country,
                              country_code
                     union all
                     SELECT
                           cast(payment_datetime as date) as date,
                           'Ukraine',
                           'UA',
                           'DTE 2',
                            sum(coalesce(payment_without_vat_uah,0) *coalesce(info_currency_history.value_to_usd,info_currency.value_to_usd) ) as revenue,
                            0                  as total_test_value,
                            0                  as impressions,
                            0                  as views,
                            0                  as clicks,
                            null               as manager_name,
                            null               as project_site,
                            0                  as user_budget_usd
                             FROM employer.jcoin_model_revenue_cashflow
                             left join dimension.info_currency_history
                            on cast(jcoin_model_revenue_cashflow.payment_datetime as date) = cast(info_currency_history.date as date)
                               and info_currency_history.country = 1
                               and info_currency_history.id_currency = 0
                     left join dimension.info_currency
                     on info_currency.country = 1
                               and info_currency.id = 0
                     group by
                     cast(payment_datetime as date)
                 ),
                 Cost as (
                     SELECT day                                                        as date,
                            countries.name_country_eng                                 as country,
                            countries.alpha_2                                          as country_code,
                            null                                                       as revenue_type,
                            'AdWords'                                                  as cost_type,
                            0                                                          as revenue,
                            impressions                                                as impressions,
                            0                                                          as views,
                            clicks                                                     as clicks,
                            cost / coalesce(currency_source.to_usd, avg_to_usd.to_usd) as cost

                     FROM imp_statistic.adwords
                              left join dimension.countries
                                        on coalesce(adwords.country, lower(left(adwords.campaign, 2))) = lower(countries.alpha_2)
                              left join imp_statistic.currency_source
                                        on adwords.day = currency_source.date
                                            and currency_source.currency = 'COP'
                              left join
                          (Select extract(year from date)  as year,
                                  extract(month from date) as month,
                                  avg(to_usd)              as to_usd

                           from imp_statistic.currency_source
                           where currency_source.currency = 'COP'
                           group by extract(year from date),
                                    extract(month from date)
                          ) avg_to_usd
                          on extract(year from adwords.day) = avg_to_usd.year
                              and extract(month from adwords.day) = avg_to_usd.month
                     union all
                     SELECT date_start                                                  as date,
                            countries.name_country_eng,
                            countries.alpha_2                                          as country_code,
                            null                                                        as revenue_type,
                            'FaceBook'                                                  as cost_type,
                            0                                                           as revenue,
                            impressions                                                 as impressions,
                            0                                                           as views,
                            clicks                                                      as clicks,
                            spend / coalesce(currency_source.to_usd, avg_to_usd.to_usd) as cost

                     FROM imp_statistic.facebook_2018
                              left join dimension.countries
                                        on lower(left(facebook_2018.campaign_name, 2)) = lower(countries.alpha_2)
                              left join imp_statistic.currency_source
                                        on facebook_2018.date_start = currency_source.date
                                            and currency_source.currency = 'COP'
                              left join
                          (Select extract(year from date)  as year,
                                  extract(month from date) as month,
                                  avg(to_usd)              as to_usd

                           from imp_statistic.currency_source
                           where currency_source.currency = 'COP'
                           group by extract(year from date),
                                    extract(month from date)
                          ) avg_to_usd
                          on extract(year from facebook_2018.date_start) = avg_to_usd.year
                              and extract(month from facebook_2018.date_start) = avg_to_usd.month
                     union all
                     SELECT yandex_2018.date,
                            countries.name_country_eng                                 as country,
                            countries.alpha_2                                          as country_code,
                            null                                                       as revenue_type,
                            'Yandex'                                                   as cost_type,
                            0                                                          as revenue,
                            impressions                                                as impressions,
                            0                                                          as views,
                            clicks                                                     as clicks,
                            cost / coalesce(currency_source.to_usd, avg_to_usd.to_usd) as cost

                     FROM imp_statistic.yandex_2018
                              left join dimension.countries
                                        on lower(left(replace(yandex_2018.login, 'yd', ''), 2)) = lower(countries.alpha_2)
                              left join imp_statistic.currency_source
                                        on yandex_2018.date = currency_source.date
                                            and currency_source.currency = 'RUB'
                              left join
                          (Select extract(year from date)  as year,
                                  extract(month from date) as month,
                                  avg(to_usd)              as to_usd

                           from imp_statistic.currency_source
                           where currency_source.currency = 'RUB'
                           group by extract(year from date),
                                    extract(month from date)
                          ) avg_to_usd
                          on extract(year from yandex_2018.date) = avg_to_usd.year
                              and extract(month from yandex_2018.date) = avg_to_usd.month
                 ),

                 Google_Analytics as
                     (
                         Select date,
                                countries.name_country_eng as country,
                                countries.alpha_2          as country_code,
                                channel,
                                users,
                                sessions

                         from (
                                  SELECT date,
                                         channelgrouping as channel,
                                         case
                                             when name = 'https://jooble.org' then 'US'
                                             else
                                                 upper(replace(replace(replace(replace(replace(name, 'https://', ''), '.jooble.org', ''),
                                                                         '.jooble.com', ''), 'http://', ''),'www.',''))  end as country,
                                         sum(users)                                                            As users,
                                         sum(sessions)                                                         as sessions
                                  FROM imp_statistic.google_analytics_general
                                  where table_num = 6
                                  group by date,
                                           case
                                               when name = 'https://jooble.org' then 'US'
                                               else
                                                   upper(replace(replace(replace(replace(replace(name, 'https://', ''), '.jooble.org', ''),
                                                                         '.jooble.com', ''), 'http://', ''),'www.','')) end,
                                           channelgrouping
                              ) GA
                                  left join dimension.countries
                                            on GA.country = countries.alpha_2
                     ),
                 All_Unions as
                     (
                         Select date,
                                country,
                                country_code,
                                Revenue_type as revenue_type,
                                abs(revenue)      as revenue_usd,
                                total_test_value as total_test_value ,
                                impressions as adv_impressions,
                                views       as adv_views,
                                clicks       as adv_clicks,
                                project_site,
                                manager_name,
                                null         as cost_type,
                                0            as cost_usd,
                                null         as channel,
                                0            as ga_users,
                                0            as ga_sessions,
                                user_budget_usd as user_budget_usd
                         from Revenue
                         union all
                         SELECT date,
                                country,
                                country_code,
                                null,
                                0,
                                0,
                                impressions,
                                views,
                                clicks,
                                null,
                                null,
                                cost_type,
                                cost as cost_usd,
                                null as channel,
                                0    as ga_users,
                                0    as ga_sessions,
                                0    as user_budget_usd
                         from Cost
                         union all
                         Select date,
                                country,
                                country_code,
                                null,
                                0,
                                0,
                                0,
                                0,
                                0,
                                null,
                                null,
                                null,
                                0,
                                channel,
                                users    as ga_users,
                                sessions as ga_sessions,
                                0        as user_budget_usd
                         from Google_Analytics
                     )
            SELECT
                   date,
                   extract(year from date) as year_date,
                   extract(quarter from date) as quarter_date,
                   extract(month from date) as month_date,
                   extract(week from date) as week_date,
                   country,
                   country_code,
                   revenue_type,
                   sum(revenue_usd)     as revenue_usd,
                   sum(total_test_value) as test_revenue_usd,
                   sum(adv_impressions) as adv_impression,
                   sum(adv_views)       as adv_view,
                   sum(adv_clicks)      as adv_click,
                   project_site,
                   manager_name,
                   cost_type,
                   channel              as ga_channel,
                   sum(cost_usd)        as cost_usd,
                   sum(ga_users)        as ga_users,
                   sum(ga_sessions)     as ga_session,
                   max(user_budget_usd) as project_site_budget_usd
            from All_Unions
            group by date,
                     extract(year from date),
                     extract(quarter from date),
                     extract(month from date),
                     extract(week from date),
                     country,
                     country_code,
                     revenue_type,
                     project_site,
                     manager_name,
                     cost_type,
                     channel;

    end;

$$;

alter procedure aggregation.insert_finance_report_archive(integer) owner to rlu;

