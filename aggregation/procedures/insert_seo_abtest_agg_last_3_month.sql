create procedure aggregation.insert_seo_abtest_agg_last_3_month()
    language plpgsql
as
$$
begin

        --delete previous 3 month
          delete from aggregation.seo_abtest_agg
          where date between (current_date - interval '3 month')::date and current_date;



        -- main load
        create temp table base as
        select gr.keys                            as url,
               concat_ws(' ', regexp_replace(uk.keyword, '[, -]*', ''), uk.region) as kw_region,
               gr.gr                              as group_num,
               gr.test_number                     as test_num,
               gr.iteration,
               gr.start_date,
               gr.end_date,
               lower(cc.alpha_2::text)            as test_country,
               cc.id                              as test_country_id,
               sum(gr.cnt)                        as group_cnt
        from aggregation.seo_test_groups gr
                 left join traffic.seo_test_url_keyword uk on gr.keys = uk.keys::text
                 left join dimension.countries cc on lower(gr.country) = lower(cc.alpha_2::text)
        where gr.start_date between (current_date - interval '3 month')::date and current_date
        group by gr.keys,
                concat_ws(' ', regexp_replace(uk.keyword, '[, -]*', ''), uk.region),
                gr.gr,
                gr.test_number, gr.iteration, gr.start_date,
                gr.end_date, (lower(cc.alpha_2::text)), cc.id;



         create temp table sample_info as
             select b.group_num,
                    b.iteration,
                    b.test_num,
                    b.start_date            as date,
                    b.test_country,
                    'group_cnt_start'::text as metric,
                    sum(b.group_cnt)        as value
             from base b
             group by b.group_num, b.iteration, b.test_num, b.start_date, b.test_country;


            insert into sample_info
             select b.group_num,
                    b.iteration,
                    b.test_num,
                    b.start_date                as date,
                    b.test_country,
                    'kwreg_cnt_start'::text     as metric,
                    count(distinct b.kw_region) as value
             from base b
             group by b.group_num, b.iteration, b.test_num, b.start_date, b.test_country;


             insert into sample_info
             select b.group_num,
                    b.iteration,
                    b.test_num,
                    b.start_date          as date,
                    b.test_country,
                    'url_cnt_start'::text as metric,
                    count(distinct b.url) as value
             from base b
             group by b.group_num, b.iteration, b.test_num, b.start_date, b.test_country;




        --(1) last 3 month of traffic.seo_ab_prod_metrics
        create temp table seo_ab_prod_metrics_new_temp_1 as
        select country_id,
               date_diff,
               q_kw,
               q_txt_region,
               is_returned,
               is_mobile,
               is_local,
               channel_traffic,
               channel_current_traffic,
               metric,
               value,
               concat(q_kw, ' ', q_txt_region) as concatenated
        from traffic.seo_ab_prod_metrics pm
        where date_diff between (current_date - interval '3 month')::date - '1900-01-01' and (current_date) - '1900-01-01';


            --last 3 month of seo_ab_prod_metrics
             insert into sample_info
             select b.group_num,
                    b.iteration,
                    b.test_num,
                    d.dt                 as date,
                    b.test_country,
                    'url_cnt_prod'::text as metric,
                    count(distinct
                          case
                              when pm.channel_current_traffic::text = 'Organic Search'::text then b.url
                              else null::text
                              end)       as value
             from base b
                      left join dimension.info_calendar d
                                on b.start_date <= d.dt
                                       and coalesce(b.end_date, current_date - 1) >= d.dt
                        left join seo_ab_prod_metrics_new_temp_1 pm
                                on pm.date_diff = d.date_diff
                                       and pm.country_id = b.test_country_id
                                       and b.kw_region = concatenated
             group by b.group_num, b.iteration, b.test_num, d.dt, b.test_country;





             insert into sample_info
             select b.group_num,
                    b.iteration,
                    b.test_num,
                    d.dt                      as date,
                    b.test_country,
                    'url_cnt_webmaster'::text as metric,
                    count(distinct
                          case
                              when wq.impressions > 0::numeric then b.url
                              else null::text
                              end)            as value
             from base b
                      left join dimension.info_calendar d
                                on b.start_date <= d.dt and coalesce(b.end_date, current_date - 1) >= d.dt
                      left join traffic.webmaster_statistic wq
                                on wq.date = d.dt
                                       and b.test_country_id = wq.country_id
                                       and wq.url::text = b.url
             where wq.date between (current_date - interval '3 month')::date and current_date
             group by b.group_num, b.iteration, b.test_num, d.dt, b.test_country;








        ---------------------------------------------------


         --(2) last 3 month of traffic.seo_ab_prod_metrics for 'Organic Search'
        create temp table seo_ab_prod_metrics_new_temp_2 as
        select country_id,
               date_diff,
               q_kw,
               q_txt_region,
               is_returned,
               is_mobile,
               is_local,
               channel_traffic,
               channel_current_traffic,
               metric,
               value,
               concat_ws(' ', regexp_replace(q_kw, '[, -]*', ''), q_txt_region) as concatenated
        from traffic.seo_ab_prod_metrics pm
        where date_diff between (current_date - interval '3 month')::date - '1900-01-01' and (current_date) - '1900-01-01'
            and pm.channel_current_traffic::text = 'Organic Search'::text;


        -- last 3 month of traffic.seo_ab_prod_metrics
         create temp table prod_metrics as
             select gr.group_num,
                    gr.iteration,
                    gr.test_num,
                    d.dt          as date,
                    gr.test_country,
                    pm.metric,
                    sum(pm.value) as value
             from base gr
                      left join dimension.info_calendar d
                                on gr.start_date <= d.dt and coalesce(gr.end_date, current_date - 1) >= d.dt
                      left join seo_ab_prod_metrics_new_temp_2 pm
                                on pm.date_diff = d.date_diff
                                       and pm.country_id = gr.test_country_id and
                                   gr.kw_region = concatenated
             group by gr.group_num, gr.iteration, gr.test_num, d.dt, gr.test_country, pm.metric;



        -- (1) limit of traffic.webmaster_statistic
         create temp table seo_statistic as
             select gr.group_num,
                    gr.iteration,
                    gr.test_num,
                    d.dt                                                      as date,
                    gr.test_country,
                    sum(wq.impressions)                                       as impressions,
                    sum(wq.clicks)                                            as clicks,
                    sum(wq."position" * wq.impressions) / sum(wq.impressions) as position_avg
             from base gr
                      left join dimension.info_calendar d
                                on gr.start_date <= d.dt and coalesce(gr.end_date, current_date - 1) >= d.dt
                      left join traffic.webmaster_statistic wq
                                on wq.date = d.dt
                                       and gr.test_country_id = wq.country_id
                                       and wq.url::text = gr.url
             where wq.date between (current_date - interval '3 month')::date and current_date
             group by gr.group_num, gr.iteration, gr.test_num, d.dt, gr.test_country;




         create temp table first_union as
             select pm.group_num,
                    pm.iteration,
                    pm.test_num,
                    pm.date,
                    pm.test_country,
                    pm.metric,
                    pm.value
             from prod_metrics pm
             union
             select ss.group_num,
                    ss.iteration,
                    ss.test_num,
                    ss.date,
                    ss.test_country,
                    'impressions'::text as metric,
                    ss.impressions      as value
             from seo_statistic ss
             union
             select ss.group_num,
                    ss.iteration,
                    ss.test_num,
                    ss.date,
                    ss.test_country,
                    'clicks'::text as metric,
                    ss.clicks      as value
             from seo_statistic ss
             union
             select ss.group_num,
                    ss.iteration,
                    ss.test_num,
                    ss.date,
                    ss.test_country,
                    'position_avg'::text as metric,
                    ss.position_avg      as value
             from seo_statistic ss;

         create temp table second_union as
             select f_1.group_num,
                    f_1.iteration,
                    f_1.test_num,
                    f_1.date,
                    f_1.test_country,
                    f_1.metric,
                    f_1.value
             from first_union f_1
             union
             select si.group_num,
                    si.iteration,
                    si.test_num,
                    si.date,
                    si.test_country,
                    si.metric,
                    si.value
             from sample_info si;

         create temp table final as
             select 'union 1'::text as union_group,
                    su.group_num,
                    su.iteration,
                    su.test_num,
                    su.date,
                    su.test_country,
                    su.metric,
                    su.value
             from second_union su
             union
             select 'union 2'::text as union_group,
                    su.group_num,
                    su.iteration,
                    su.test_num,
                    su.date,
                    su.test_country,
                    su.metric,
                    su.value
             from second_union su;



    insert into aggregation.seo_abtest_agg(union_group, group_num, iteration, test_num, date, test_country,
                                           metric,value)
    select f.union_group,
           f.group_num,
           f.iteration,
           f.test_num,
           f.date,
           f.test_country,
           f.metric,
           f.value
    from final f;


drop table base;
drop table sample_info;
drop table seo_ab_prod_metrics_new_temp_1;
drop table seo_ab_prod_metrics_new_temp_2;
drop table prod_metrics;
drop table seo_statistic;
drop table first_union;
drop table second_union;
drop table final;


end;$$;

alter procedure aggregation.insert_seo_abtest_agg_last_3_month() owner to postgres;

