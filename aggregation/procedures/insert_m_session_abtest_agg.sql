create procedure aggregation.insert_m_session_abtest_agg(_datediff integer, _sdate date)
    language plpgsql
as
$$
begin

        create temp table abtest_agg AS
        SELECT session_abtest_agg.country_id,
               session_abtest_agg.group_num,
               session_abtest_agg.action_datediff,
               session_abtest_agg.is_returned,
               session_abtest_agg.is_mobile,
               NULL::text                                                          AS is_local,
               NULL::text                                                          AS session_create_page_type,
               NULL::text                                                          AS is_anomalistic,
               session_abtest_agg.attribute_name,
               session_abtest_agg.attribute_value,
               session_abtest_agg.metric_name,
               sum(session_abtest_agg.metric_value)                                AS metric_value,
               countries.name_country_eng                                          AS country,
               fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff) AS action_date,
               NULL::text                                                          AS first_traffic_channel,
               NULL::text                                                          AS current_traffic_channel
        FROM aggregation.session_abtest_agg
        JOIN dimension.countries ON session_abtest_agg.country_id = countries.id
        LEFT JOIN dimension.u_traffic_source ON session_abtest_agg.country_id = u_traffic_source.country AND session_abtest_agg.traffic_source_id = u_traffic_source.id
        LEFT JOIN dimension.u_traffic_source current_u_traffic_source ON session_abtest_agg.country_id = current_u_traffic_source.country AND
                              session_abtest_agg.current_traffic_source_id = current_u_traffic_source.id
        WHERE session_abtest_agg.group_num IS NOT NULL AND session_abtest_agg.action_datediff >= _datediff
        GROUP BY session_abtest_agg.country_id, session_abtest_agg.group_num, session_abtest_agg.action_datediff,
                 session_abtest_agg.is_returned, session_abtest_agg.is_mobile, session_abtest_agg.attribute_name,
                 session_abtest_agg.attribute_value, session_abtest_agg.metric_name, countries.name_country_eng,
                 (fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff));

        create temp table adsense AS
        SELECT countries.id                                         AS country_id,
               concat(channel.serp_test, '-', channel.test_version) AS group_num,
               'adsense revenue'::text                              AS metric_name,
               sum(revenue.estimated_earnings_usd)                  AS metric_value,
               countries.name_country_eng                           AS country,
               revenue.action_date
        FROM imp_api.adsense_custom_channels_revenue revenue
        JOIN aggregation.dic_adsence_test_channels channel ON revenue.country_code::text = channel.country_code AND revenue.custom_channel_name::text = channel.custom_channel_name
                                                                  AND revenue.action_date >= channel."Date_add"::date
        JOIN dimension.countries ON revenue.country_code::text = countries.alpha_2::text
        WHERE channel.serp_test IS NOT NULL AND revenue.action_date >= _sdate
        GROUP BY countries.id, (concat(channel.serp_test, '-', channel.test_version)), countries.name_country_eng, revenue.action_date;

        truncate table aggregation.m_session_abtest_agg;

        insert into aggregation.m_session_abtest_agg (country_id, group_num, action_datediff, is_returned, is_mobile, is_local,
                                              session_create_page_type, is_anomalistic, attribute_name, attribute_value,
                                              metric_name, metric_value, country, action_date, first_traffic_channel,
                                              current_traffic_channel, union_group, sessions, avg_session_duration, bounce_rate)
        SELECT abtest_agg.country_id,
               abtest_agg.group_num,
               abtest_agg.action_datediff,
               abtest_agg.is_returned,
               abtest_agg.is_mobile,
               abtest_agg.is_local,
               abtest_agg.session_create_page_type,
               abtest_agg.is_anomalistic,
               abtest_agg.attribute_name,
               abtest_agg.attribute_value,
               abtest_agg.metric_name,
               abtest_agg.metric_value,
               abtest_agg.country,
               abtest_agg.action_date,
               abtest_agg.first_traffic_channel,
               abtest_agg.current_traffic_channel,
               'union 1'::text AS union_group,
               0               AS sessions,
               0               AS avg_session_duration,
               0               AS bounce_rate
        FROM abtest_agg
        UNION ALL
        SELECT abtest_agg.country_id,
               abtest_agg.group_num,
               abtest_agg.action_datediff,
               abtest_agg.is_returned,
               abtest_agg.is_mobile,
               abtest_agg.is_local,
               abtest_agg.session_create_page_type,
               abtest_agg.is_anomalistic,
               abtest_agg.attribute_name,
               abtest_agg.attribute_value,
               abtest_agg.metric_name,
               abtest_agg.metric_value,
               abtest_agg.country,
               abtest_agg.action_date,
               abtest_agg.first_traffic_channel,
               abtest_agg.current_traffic_channel,
               'union 2'::text AS union_group,
               0               AS sessions,
               0               AS avg_session_duration,
               0               AS bounce_rate
        FROM abtest_agg
        UNION ALL
        SELECT ga_user_and_adsense_tests.country_id,
               ga_user_and_adsense_tests.test_groups_session AS group_num,
               NULL::integer                                 AS action_datediff,
               NULL::integer                                 AS is_returned,
               NULL::integer                                 AS is_mobile,
               NULL::text                                    AS is_local,
               NULL::text                                    AS session_create_page_type,
               NULL::text                                    AS is_anomalistic,
               NULL::character varying                       AS attribute_name,
               NULL::integer                                 AS attribute_value,
               NULL::character varying                       AS metric_name,
               NULL::numeric                                 AS metric_value,
               countries.name_country_eng                    AS country,
               ga_user_and_adsense_tests.action_date,
               NULL::text                                    AS first_traffic_channel,
               NULL::text                                    AS current_traffic_channel,
               'union 1'::text                               AS union_group,
               ga_user_and_adsense_tests.sessions,
               ga_user_and_adsense_tests.avg_session_duration,
               ga_user_and_adsense_tests.bounce_rate
        FROM imp_api.ga_user_and_adsense_tests
                 JOIN dimension.countries ON ga_user_and_adsense_tests.country_id = countries.id
        WHERE ga_user_and_adsense_tests.action_date >= _sdate
        UNION ALL
        SELECT ga_user_and_adsense_tests.country_id,
               ga_user_and_adsense_tests.test_groups_session AS group_num,
               NULL::integer                                 AS action_datediff,
               NULL::integer                                 AS is_returned,
               NULL::integer                                 AS is_mobile,
               NULL::text                                    AS is_local,
               NULL::text                                    AS session_create_page_type,
               NULL::text                                    AS is_anomalistic,
               NULL::character varying                       AS attribute_name,
               NULL::integer                                 AS attribute_value,
               NULL::character varying                       AS metric_name,
               NULL::numeric                                 AS metric_value,
               countries.name_country_eng                    AS country,
               ga_user_and_adsense_tests.action_date,
               NULL::text                                    AS first_traffic_channel,
               NULL::text                                    AS current_traffic_channel,
               'union 2'::text                               AS union_group,
               ga_user_and_adsense_tests.sessions,
               ga_user_and_adsense_tests.avg_session_duration,
               ga_user_and_adsense_tests.bounce_rate
        FROM imp_api.ga_user_and_adsense_tests
                 JOIN dimension.countries ON ga_user_and_adsense_tests.country_id = countries.id
        WHERE ga_user_and_adsense_tests.action_date >= _sdate
        UNION ALL
        SELECT adsense.country_id,
               adsense.group_num,
               NULL::integer           AS action_datediff,
               NULL::integer           AS is_returned,
               NULL::integer           AS is_mobile,
               NULL::text              AS is_local,
               NULL::text              AS session_create_page_type,
               NULL::text              AS is_anomalistic,
               NULL::character varying AS attribute_name,
               NULL::integer           AS attribute_value,
               adsense.metric_name,
               adsense.metric_value,
               adsense.country,
               adsense.action_date,
               NULL::text              AS first_traffic_channel,
               NULL::text              AS current_traffic_channel,
               'union 1'::text         AS union_group,
               NULL::integer           AS sessions,
               NULL::numeric           AS avg_session_duration,
               NULL::numeric           AS bounce_rate
        FROM adsense
        UNION ALL
        SELECT adsense.country_id,
               adsense.group_num,
               NULL::integer           AS action_datediff,
               NULL::integer           AS is_returned,
               NULL::integer           AS is_mobile,
               NULL::text              AS is_local,
               NULL::text              AS session_create_page_type,
               NULL::text              AS is_anomalistic,
               NULL::character varying AS attribute_name,
               NULL::integer           AS attribute_value,
               adsense.metric_name,
               adsense.metric_value,
               adsense.country,
               adsense.action_date,
               NULL::text              AS first_traffic_channel,
               NULL::text              AS current_traffic_channel,
               'union 2'::text         AS union_group,
               NULL::integer           AS sessions,
               NULL::numeric           AS avg_session_duration,
               NULL::numeric           AS bounce_rate
        FROM adsense;

        drop table abtest_agg;
        drop table adsense;
end;

$$;

alter procedure aggregation.insert_m_session_abtest_agg(integer, date) owner to rlu;

