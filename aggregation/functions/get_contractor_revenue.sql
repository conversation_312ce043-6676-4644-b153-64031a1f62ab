create function aggregation.get_contractor_revenue()
    returns TABLE(contractor character varying, site_name text, id_project integer, manager text, id_user integer, country character varying, revenue_usd numeric,min_contractor_date date, avg_monthly_revenue numeric)
    language plpgsql
as
$$
begin

    create temp table t as
    select
        acs.id_project,
        acs.id_user,
        acs.site,
        sum((acs.total_value / acs.click_count) *
            (acs.click_count - acs.test_count - acs.duplicated_count)) as revenue_usd,
        au.company                                                     as contractor,
        vsm.sale_manager,
        c.alpha_2                                                      as country,
        au.flags                                                       as user_flags
    from
        aggregation.auction_click_statistic_analytics acs
        left join imp.auction_user au
                  on acs.id_user = au.id and acs.country_id = au.country
        left join aggregation.v_sale_manager vsm
                  on acs.country_id = vsm.country and acs.id_project = vsm.id_project
        left join dimension.countries c
                  on c.id = acs.country_id
    where
          click_price > 0
      and cast(date as date) >= (current_date - 90)
      and cast(date as date) <= (current_date - 1)
    group by
        acs.id_project, acs.id_user, site, au.company, vsm.sale_manager, c.alpha_2, au.flags;

    create temp table monthly_revenue as
    select
        au.company,
        min_date_contractor_table.min_contractor_date,
        sum((acs.total_value / acs.click_count) * (acs.click_count - acs.test_count - acs.duplicated_count)) /
        count(distinct date_trunc('month', cast(date as date))) as avg_monthly_revenue_usd
    from
        aggregation.auction_click_statistic_analytics acs
        left join imp.auction_user au
                  on acs.id_user = au.id and acs.country_id = au.country
    left join (
        select acsa.contractor,
       min(acsa.date) as min_contractor_date
        from aggregation.auction_click_statistic_analytics acsa
        where acsa.click_price > 0
        group by acsa.contractor) as min_date_contractor_table on
        au.company = min_date_contractor_table.contractor
    where
          click_price > 0
      and cast(date as date) >= (current_date - 90)
      and cast(date as date) <= (current_date - 1)
    group by
        au.company, min_date_contractor_table.min_contractor_date;

    create temp table t2 as
    select
        btrim(regexp_replace(acs.contractor, '\s+'::text, ' '::text, 'g'::text)) as contractor,
        max(acs.site::text)                                                      as site_name,
        max(acs.id_project)                                                      as id_project,
        max(acs.sale_manager::text)                                              as manager,
        acs.id_user,
        acs.country,
        sum(acs.revenue_usd)                                                     as revenue_usd
    from
        t acs
    where
          (acs.user_flags & 8) = 8
      and (acs.user_flags & 32) = 0
      and acs.revenue_usd > 0
    group by acs.contractor, acs.id_user, acs.country;


    return query
        select
            cast(t.contractor as varchar) as contractor,
            t.site_name,
            t.id_project,
            t.manager,
            t.id_user,
            t.country,
            t.revenue_usd,
            min_contractor_date,
            avg_monthly_revenue_usd
        from
            t2 t
        left join monthly_revenue mr on mr.company = t.contractor;


    drop table t;
    drop table t2;
    drop table monthly_revenue;


end;
$$;

alter function aggregation.get_contractor_revenue() owner to ono;

