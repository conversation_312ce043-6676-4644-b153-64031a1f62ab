declare @start_datediff int = ${dt_begin},
		@date_end int = ${dt_end},
        @country_id int = ${country_id};

        select @country_id 																		   as country_id,
               s.date_diff, 
               id_traf_source,
               id_current_traf_source,
               iif(s.flags & 16 = 16, 1, 0)                                          as is_mobile,
               iif(ip_cc = lower(substring(db_name(), 5, 2))
               or (ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'), 1, 0) as is_local,
               iif(s.flags & 2 = 2, 1, 0)                                            as is_returned,
               case
                   when si.id_search is not null then 'search'
                   when si.id_alertview is not null then 'alertview'
               end                                                                   as placement, 
               iif(si.id is not null, page * 10000 + si.position, 0)                 as page_position_encoded,
               count(click.id_click)                                                 as click_cnt,
               count(distinct si.id)                                                 as impression_cnt,
               count(distinct sios.id_impression)                                    as impr_on_screen_cnt
        from dbo.session s with (nolock)
             left join      dbo.session_impression si with (nolock)
             on s.date_diff = si.date
                 and s.id = si.id_session
                 and position <= 100
             left join dbo.session_impression_on_screen sios with (nolock)
             on si.date = sios.date_diff
                 and si.id = sios.id_impression
             left join
             (select date_diff,
              id_session,
              id_click,
              id_impression
              from (
              select date_diff,
                   id_session,
                   id as id_click,
                   id_impression
              from dbo.session_click with (nolock)
              where date_diff between @start_datediff and @date_end
              union
              select date_diff,
                   id_session,
                   id as id_click,
                   id_impression
              from dbo.session_click_no_serp with (nolock)
              where date_diff between @start_datediff and @date_end
              ) a
                 ) click
             on click.date_diff = si.date
                 and click.id_impression = si.id
        where si.date between @start_datediff and @date_end
          and s.flags & 1 = 0 /* no bot sessions */
        group by s.date_diff,
				 id_traf_source,
                 id_current_traf_source,
                 iif(s.flags & 16 = 16, 1, 0),
                 iif(ip_cc = lower(substring(db_name(), 5, 2))
                 or (ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'), 1, 0),
                 iif(s.flags & 2 = 2, 1, 0),
                 case
                     when si.id_search is not null then 'search'
                     when si.id_alertview is not null then 'alertview'
                 end, 
                 iif(si.id is not null, page * 10000 + si.position, 0)
