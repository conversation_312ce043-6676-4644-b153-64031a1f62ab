declare @dd int = ${dt_begin},
    @country_id int = ${country_id};


select distinct SAV.date_diff,
                SAV.id_session,
                SAV.version
into #sav
from dbo.session_adsense_version SAV with (nolock)
         inner join
     dbo.session S with (nolock)
     on
             SAV.id_session = S.id and
             SAV.date_diff = S.date_diff
         left join
     dbo.u_traffic_source TS with (nolock)
     on
         coalesce(S.id_traf_source, S.id_current_traf_source) = TS.id
where SAV.date_diff = @dd
  and S.flags & 1 = 0
  and TS.name not like 'LP_%'


select date_diff,
       id_session,
       id as id_search
into #searches
from dbo.session_search with (nolock)
where date_diff = @dd


select date_diff,
       id_search,
       id as id_click
into #clicks
from dbo.session_click with (nolock)
where date_diff = @dd
  and id_search is not null


select SA.date_diff,
       SA.id_session,
       SA.id_click                                     as id_click,
       SA.id                                         as id_away,
       CAC.id_session_away,
       coalesce(SA.click_price, 0) * IC.value_to_eur as revenue,
       cs.conversion_start
into #aways
from  dbo.session_away sa (nolock)
       left join  dbo.info_currency IC with (nolock) on SA.id_currency = IC.id
         left join
     (select distinct id_session_away
      from auction.conversion_away_connection CAC with (nolock)
     ) CAC
     on SA.id = CAC.id_session_away
         left join (
    select id_project,
           min(cac.date_diff) as conversion_start
    from auction.conversion_away_connection cac with (nolock)
             join dbo.session_away sa with (nolock)
                  on cac.date_diff = sa.date_diff
                      and cac.id_session_away = sa.id
    group by id_project
) cs
                   on SA.id_project = cs.id_project
where SA.date_diff = @dd
  and isnull(sa.flags, 0) & 2 = 0


Select SA.date_diff,
       SAV.version,
       count(distinct AA.id) as accounts
into #account
from dbo.session_account SA with (nolock)
         join
     dbo.account AA with (nolock)
     on
         SA.id_account = AA.id
         join dbo.session_adsense_version SAV with (nolock)
              on SA.date_diff = SAV.date_diff and
                 SAV.id_session = SA.id_session

where SA.date_diff = @dd
  and SA.date_diff = datediff(day, 0, AA.date_add)
group by SA.date_diff,
         SAV.version


Select distinct SA.id_account,
                SAV.version as version
into #all_accounts
from dbo.session_account SA with (nolock)
         join
     dbo.account A with (nolock)
     on
         SA.id_account = A.id
         join
     dbo.session_adsense_version SAV with (nolock)
     on
             SA.id_session = SAV.id_session
             and SAV.date_diff = datediff(day, 0, A.date_add)
where SAV.date_diff >= 44650

-- alertviews
select sv.date_diff, ea.id_account, sc.id as id_click, ea.version
into #email_click
from #all_accounts ea with (nolock)
         inner join dbo.email_alert ev with (nolock)
                    on ev.id_account = ea.id_account
         inner join dbo.session_alertview sv with (nolock)
                    on ev.id = sv.sub_id_alert
         inner join dbo.session_click sc with (nolock)
                    on sv.date_diff = sc.date_diff
                        and sv.id = sc.id_alertview
where sv.date_diff = @dd
union
-- lt8
Select sc.date_diff, a.id_account, sc.id as id_click, a.version
from dbo.session_click sc with (nolock)
         left join dbo.session_away sa with (nolock)
                   on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
         left join dbo.session_click_message scm with (nolock)
                   on sc.date_diff = scm.date_diff
                       and sc.id = scm.id_click
         left join dbo.session_away_message sam with (nolock)
                   on sc.date_diff = sam.date_diff
                       and sa.id = sam.id_away
         join dbo.email_sent es with (nolock)
              on coalesce(scm.id_message, sam.id_message) = es.id_message
         join #all_accounts a
              on es.id_account = a.id_account
where sc.date_diff = @dd


Select a.date_diff,
       ec.version,
       sum(revenue)                      as email_revenue,
       count(distinct a.id_away)         as email_aways,
       count(distinct a.id_session_away) as email_conversions
into #email_revenue
from #email_click ec
         join #aways a
              on ec.date_diff = a.date_diff
                  and ec.id_click = a.id_click
group by a.date_diff,
         ec.version


select A.date_diff                                       as date_diff,
       SAV.version,
       sum(coalesce(A.click_price, 0) * IC.value_to_eur) as account_revenue
into #account_revenue
from dbo.session_away A with (nolock)
         inner join
     dbo.info_currency IC with (nolock)
     on
         A.id_currency = IC.id
         inner join
     dbo.session_account SA with (nolock)
     on
             A.date_diff = SA.date_diff
             and A.id_session = SA.id_session
         inner join
     dbo.account AA with (nolock)
     on
             SA.id_account = AA.id
             and A.date_diff > datediff(day, 0, AA.date_add)
         inner join
     #all_accounts SAV
     on AA.id = SAV.id_account
where A.date_diff = @dd
group by A.Date_diff,
         SAV.version


Select session_alertview.date_diff,
       SAV.version,
       count(distinct session_alertview.id)                                        as alertview_cnt,
       count(distinct session_click.id)                                            as click_cnt,
       count(distinct session_away.id)                                             as away_cnt,
       count(distinct CAC.id_session_away)                                         as alertivew_conversion_cnt,
       sum(session_away.click_price * info_currency.value_to_eur)                  as alertview_revenue_eur,
       count(distinct case
                          when session_away.date_diff >= cs.conversion_start
                              then session_away.id end)                            as alertview_conversion_away_cnt,
       sum(case
               when session_away.date_diff >= cs.conversion_start
                   then session_away.click_price * info_currency.value_to_eur end) as alertivew_conversion_revenue_eur
into #alertview
from dbo.session_alertview with (nolock)
         join dbo.session_adsense_version SAV with (nolock)
              on session_alertview.date_diff = SAV.date_diff
                  and session_alertview.id_session = SAV.id_session
         join dbo.session with (nolock)
              on session_alertview.date_diff = session.date_diff
                  and session_alertview.id_session = session.id
         left join dbo.session_click with (nolock)
                   on session_alertview.date_diff = session_click.date_diff
                       and session_alertview.id = session_click.id_alertview
         left join dbo.session_jdp with (nolock)
                   on session_jdp.date_diff = session_click.date_diff
                       and session_jdp.id_click = session_click.id
         left join dbo.session_away with (nolock)
                   on session_away.date_diff = session_click.date_diff
                       and (session_away.id_click = session_click.id
                           or session_away.id_jdp = session_jdp.id)
         left join dbo.info_currency with (nolock)
                   on session_away.id_currency = info_currency.id
         left join
     (select distinct id_session_away
      from auction.conversion_away_connection CAC with (nolock)
     ) CAC
     on session_away.id = CAC.id_session_away
         left join (
    select id_project,
           min(cac.date_diff) as conversion_start
    from auction.conversion_away_connection cac with (nolock)
             join dbo.session_away sa with (nolock)
                  on cac.date_diff = sa.date_diff
                      and cac.id_session_away = sa.id
    group by id_project
) cs
                   on session_away.id_project = cs.id_project
where session_alertview.date_diff = @dd
  and session.flags & 1 = 0
group by SAV.version, session_alertview.date_diff


select @country_id                                                                    as country_id,
       SAV.date_diff                                                                  as action_datediff,
       SAV.version,
       count(distinct SAV.id_session)                                                 as sessions,
       count(distinct SS.id_search)                                                   as searches,
       count(distinct C.id_click)                                                     as clicks,
       count(distinct A.id_away)                                                      as aways,
       count(distinct A.id_session_away)                                              as conversions,
       sum(A.revenue)                                                                 as revenue_eur,
       AA.accounts,
       AR.account_revenue,
       er.email_aways,
       er.email_conversions,
       er.email_revenue,
       count(distinct case when a.date_diff >= a.conversion_start then A.id_away end) as conversion_aways,
       sum(case when a.date_diff >= a.conversion_start then A.revenue end)            as conversion_revenue_eur,
       al.alertview_cnt,
       al.click_cnt as alertview_click_cnt,
       al.away_cnt as alertview_away_cnt,
       al.alertview_revenue_eur,
       al.alertview_conversion_away_cnt,
       al.alertivew_conversion_revenue_eur,
       al.alertivew_conversion_cnt
from #sav SAV
         inner join
     #searches SS
     on
             SS.date_diff = SAV.date_diff and
             SS.id_session = SAV.id_session
         left join
     #account AA
     on
             SAV.date_diff = AA.date_diff and
             SAV.version = AA.version
         left join
     #clicks C
     on
             C.date_diff = SS.date_diff and
             C.id_search = SS.id_search
         left join
     #aways A
     on
             A.date_diff = C.date_diff and
             A.id_click = C.id_click
         left join
     #account_revenue AR
     on
             AR.date_diff = SAV.date_diff and
             AR.version = SAV.version
         left join #email_revenue er
                   on SAV.date_diff = er.date_diff and
                      SAV.version = ER.version
         left join #alertview al
                   on SAV.date_diff = al.date_diff and
                      SAV.version = al.version
group by SAV.version,
         SAV.date_diff,
         AR.account_revenue,
         AA.accounts,
         er.email_aways,
         er.email_revenue,
         er.email_conversions,
         al.alertview_cnt,
         al.click_cnt,
         al.away_cnt,
         al.alertview_revenue_eur,
         al.alertview_conversion_away_cnt,
         al.alertivew_conversion_revenue_eur,
         al.alertivew_conversion_cnt
