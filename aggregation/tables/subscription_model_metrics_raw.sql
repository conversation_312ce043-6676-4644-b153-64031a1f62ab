With employer_info as(
Select  e.id                    as employer_id,
        e.id_account            as account_id,
        date(e.date_created)    as date_created_emp_account,
        e.moderation_status     as moderation_status,
        ec.company_name         as company_name,
        ec.industry             as company_industry
FROM employer_account.employer e
    inner join employer_account.employer_cdp ec on ec.id = e.id_cdp
WHERE e.country_code = 'rs'
        and e.id not in (579123, 579781, 607380, 610207) -- test accounts
),

     jobs_info as (
Select j.id as job_id,
       e.employer_id,
	   e.moderation_status,
       e.date_created_emp_account,
       e.company_name,
       e.company_industry
from employer_account.job j
inner join employer_info e on e.employer_id = j.id_employer
),

     active_subs_period as (
Select  ps.id_employer,
        date(so.date_paid)      as date_paid,
        DATE_ADD(date(so.date_paid), INTERVAL pp.month_count month) as date_sub_end
FROM employer_account.packet_subscription ps
    inner join employer_account.subscription_order so on so.id_subscription = ps.id
    left join employer_account.packet_period pp on pp.id = ps.id_period
WHERE ps.id_packet <> 49
        and so.status = 2
            and so.date_refunded is null
     ),

     unions as (
select e.employer_id,
       e.date_created_emp_account,
       e.company_name,
       e.company_industry,
        null                    as emp_mod_status,
        s.id                    as session_id,
        null                    as job_id,
        'session'               as metric,
        s.date_started          as datetime,
        null                    as text,
        s.device,
        CASE WHEN date(s.date_started) between activ.date_paid and activ.date_sub_end
             THEN 1 ELSE 0 END  as status,
        s.id                    as metric_id
FROM employer_account.session s
    inner join employer_info e on s.id_account = e.account_id
    left join active_subs_period activ ON activ.id_employer = e.employer_id and date(s.date_started) >= activ.date_paid and date(s.date_started) <= activ.date_sub_end
where s.using_secret_key = 0
        and date(s.date_started) >= '2023-05-30'
group by e.employer_id, e.date_created_emp_account, e.company_name, e.company_industry,s.id,s.date_started, s.device, CASE WHEN date(s.date_started) between activ.date_paid and activ.date_sub_end THEN 1 ELSE 0 END

union all

select
	j.employer_id           as employer_id,
    j.date_created_emp_account,
    j.company_name,
    j.company_industry,
	j.moderation_status     as emp_mod_status,
    ja.id_session_apply     as session_id,
    j.job_id,
    'apply'                 as metric,
    ja.date                 as datetime,
    null                    as text,
    null                    as device,
    case
		when ja.date_seen is not NULL
		then 1 else 0
	end                     as status,
    ja.id                   as metric_id
from jobs_info j
left join employer_account.job_apply ja on j.job_id = ja.id_job
where date(ja.date) >= '2023-01-01'
group by j.employer_id, j.date_created_emp_account, j.company_name, j.company_industry, j.moderation_status, ja.id_session_apply, j.job_id, ja.date, case when ja.date_seen is not NULL then 1 else 0 end, ja.id

union all

select
	j.employer_id           as employer_id,
    j.date_created_emp_account,
    j.company_name,
    j.company_industry,
	j.moderation_status     as emp_mod_status,
    null                    as session_id,
    j.job_id,
    'message'               as metric,
    jam.date_created        as datetime,
    jam.text                as text,
    null                    as device,
    case when jam.flags =1 then 1 else 0 end    as status,
    jam.id                  as metric_id
from jobs_info j
left join employer_account.job_apply ja on j.job_id = ja.id_job
inner join employer_account.job_apply_message jam on ja.id = jam.id_apply
where date(jam.date_created) >= '2023-01-01'
            and jam.author_type = 0  -- is employer
group by j.employer_id, j.date_created_emp_account, j.company_name, j.company_industry,j.moderation_status, j.job_id, jam.date_created, jam.text,case when jam.flags =1 then 1 else 0 end, jam.id

Union all

select
	j.employer_id           as employer_id,
    j.date_created_emp_account,
    j.company_name,
    j.company_industry,
	j.moderation_status     as emp_mod_status,
    null                    as session_id,
    j.job_id,
    'note'                  as metric,
    jan.date_created        as datetime,
	jan.text                as text,
    null                    as device,
    0                       as status,
    jan.id                  as metric_id
from jobs_info j
left join employer_account.job_apply ja on j.job_id = ja.id_job
left join employer_account.job_apply_note jan on ja.id = jan.id_apply
where date(jan.date_created) >= '2023-01-01'
group by j.employer_id, j.date_created_emp_account, j.company_name, j.company_industry, j.moderation_status, j.job_id, jan.date_created, jan.text, jan.id
     )

Select  u.employer_id,
        u.date_created_emp_account,
        u.company_name,
        u.company_industry,
        u.emp_mod_status,
        u.session_id,
        u.job_id,
        u.metric,
        u.datetime,
        u.text,
        u.device,
        u.status,
        u.metric_id
FROM unions u
;
