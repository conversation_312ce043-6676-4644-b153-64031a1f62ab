-- Obtains data for the previous day
with sessions as (
    select s.id as session_id,
           s.date_diff,
           case
               when s.flags & 2 = 2 then 1
               else 0
           end  as is_returned,
           case
               when s.flags & 64 = 64 then 2 /*mobile app*/
               when s.flags & 16 = 16 then 1 /*mobile web*/
               else 0 /*desktop web*/
           end  as device_type_id
    from public.session s
    where s.flags & 1 = 0
      and s.date_diff = ${dt_diff}
)
select ${country_id} as country_id,
		date('1900-01-01') + s.date_diff                             as action_date,
       s.device_type_id,
       s.is_returned,
       count(distinct s.session_id)                                 as session_cnt,
       count(distinct case when sba.type = 1 then s.session_id end) as session_banner_showed_cnt,
       count(distinct case when sba.type = 3 then s.session_id end) as session_banner_clicked_cnt
from sessions s
     join public.session_banner_action sba
     on s.date_diff = sba.date_diff
         and s.session_id = sba.id_session
         and sba.banner_type = 14
         and sba.type in (1, 3) /*show and click*/
group by s.date_diff, s.device_type_id, s.is_returned;