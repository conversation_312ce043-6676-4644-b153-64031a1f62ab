-- countries: UK,FR,AT,DE,US 
-- new countries: PL, CA, CH, RO, HU, BE, NL (starting from 06/2023)
-- run: monthly

-- VARIABLES:
-- format(dateadd(month, datediff(month, 0, getdate())-1, 0), 'yyyy-MM-dd') as date_start,
-- format(eomonth(dateadd(month, datediff(month, 0, getdate())-1, 0)), 'yyyy-MM-dd') as date_end ,
-- datediff(day, 0, cast(DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE())-1, 0) as date)) as date_diff_start,
-- datediff(day, 0, eomonth(dateadd(month, datediff(month, 0, getdate())-1, 0))) as date_diff_end,
-- CONVERT(char(6), cast(DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE())-1, 0) as datetime), 112) as year_month

-- Note: As we don't have historical db for US, we imitate it from prod. Script: vacancy_job_history_agg_US_prod.sql
-----------
select *
into #session_temp
from dbo.session
where date_diff between ${DT_DIFF_START} and ${DT_DIFF_END};

select *
into #session_search_temp
from dbo.session_search
where date_diff between ${DT_DIFF_START} and ${DT_DIFF_END};

select *
into #session_filter_action_temp
from dbo.session_filter_action
where date_diff between ${DT_DIFF_START} and ${DT_DIFF_END};



select *
into #job_temp
from dbo.job j
WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
	and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}' );

select *
into #job_history_temp
FROM dbo.job_history j  with (nolock)
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
-----------



-- search_cnt by date_diff
SELECT   ss.date_diff,
         COUNT(ss.id)												as search_cnt
into #ss_by_date
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and s.flags & 1 = 0
GROUP BY ss.date_diff
;

-- search_cnt with remote status by date_diff
SELECT   ss.date_diff,
         COUNT(ss.id)												as search_cnt
into #ss_by_remote
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
left join dbo.info_region ir  with (nolock) on ss.q_id_region = ir.id
left join #session_filter_action_temp sfa with (nolock) on s.date_diff = sfa.date_diff and ss.id = sfa.id_search_prev
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and s.flags & 1 = 0
		and ( ir.name = 'Remote'
				or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
				or lower(ss.q_kw) like '%télétravail%'
				or lower(ss.q_kw) like '%travail à distance%'
				or lower(ss.q_kw) like '%travail à domicile%'
				or lower(ss.q_kw) like '%en ligne%'
				or lower(ss.q_kw) like '%remote%'
				or lower(ss.q_kw) like '%online% '
				or lower(ss.q_kw) like '%work from home%'
				or lower(ss.q_kw) like '%from home%'
				or lower(ss.q_kw) like '%homeoffice%'
				or (sfa.action in (0, 5) /* mobile and desktop */
					and sfa.filter_type = 2 /* location */
					and sfa.filter_value = 2 /* remote work */
					and sfa.id_search_prev is not null ))
GROUP BY ss.date_diff
;

-- search_cnt by region
SELECT	 ss.q_id_region,
		 ir.name																as region_name,
         case when ir.is_city = 0 then 'region_district' else 'city_town' end	as region_type,
         COUNT(ss.id)															as search_cnt
into #ss_region
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
left join dbo.info_region ir  with (nolock) on ss.q_id_region = ir.id
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
	  and s.flags & 1 = 0
      and ss.q_id_region != -1	/* excluding queries without location or foreign location */
	  and ir.is_city = 1		/* only cities are needed */
	  and ir.order_value >100000 /* update: population more than 100'000 people. Previous: population more than 20'000 people */
	  and ir.order_value != -1  /* excluding foreign cities */
GROUP BY ss.q_id_region,
		 ir.name,
         case when ir.is_city = 0 then 'region_district' else 'city_town' end
;

SELECT	*,
		row_number() over ( order by b.search_cnt desc ) AS TOPNo
into #ss_by_region_top
FROM (
			Select TOP 15 *
            FROM #ss_region a
			Order by a.search_cnt desc
			) b
;

SELECT	*,
		row_number() over ( order by b.search_cnt asc ) AS TOPNo
into #ss_by_region_bottom
FROM (
			Select TOP 15 *
            FROM #ss_region a
			Order by a.search_cnt asc
			) b
;

-- search_cnt by user query
SELECT   ss.q_kw,
		 COUNT(ss.id)	as search_cnt
into #ss_top
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
	  and s.flags & 1 = 0
	  and ss.q_kw !=''
GROUP BY ss.q_kw
;

SELECT	*,
		row_number() over ( order by b.search_cnt desc ) AS TOPNo
into #ss_TOP10
FROM (
			Select TOP 15 *
            FROM #ss_top a
			Order by a.search_cnt desc
			) b
;

SELECT	*,
		row_number() over ( order by b.search_cnt asc ) AS TOPNo
into #ss_bottom10
FROM (
			Select TOP 15 *
            FROM #ss_top a
			Order by a.search_cnt asc
			) b
;

-- search_cnt by user query with remote status
SELECT	*,
		row_number() over ( order by b.search_cnt desc ) AS TOPNo
into #ss_TOP10_remote
FROM (
			Select TOP 15 *
            FROM (
					SELECT   ss.q_kw,
							 COUNT(ss.id)	as search_cnt
					FROM #session_search_temp ss  with (nolock)
					join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
					left join dbo.info_region ir  with (nolock) on ss.q_id_region = ir.id
					left join #session_filter_action_temp sfa with (nolock) on s.date_diff = sfa.date_diff and ss.id = sfa.id_search_prev
					WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
						  and s.flags & 1 = 0
						  and ss.q_kw !=''
						  and ( ir.name = 'Remote'
								or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
								or lower(ss.q_kw) like '%télétravail%'
								or lower(ss.q_kw) like '%travail à distance%'
								or lower(ss.q_kw) like '%travail à domicile%'
								or lower(ss.q_kw) like '%en ligne%'
								or lower(ss.q_kw) like '%remote%'
								or lower(ss.q_kw) like '%online% '
								or lower(ss.q_kw) like '%work from home%'
								or lower(ss.q_kw) like '%from home%'
								or lower(ss.q_kw) like '%homeoffice%'
								or (sfa.action in (0, 5) /* mobile and desktop */
									and sfa.filter_type = 2 /* location */
									and sfa.filter_value = 2 /* remote work */
									and sfa.id_search_prev is not null ))
					GROUP BY ss.q_kw
				) a
			Order by a.search_cnt desc
		) b
;

-- job_cnt by region bottom-10
SELECT	*,
		row_number() over ( order by b.job_cnt asc ) AS TOPNo
into #job_by_region_bottom
FROM (
	Select TOP 30 *
	FROM (
				SELECT
						jr.id_region,
						ir.name													as region_name,
						case when ir.is_city = 1
						then 'city_town' else 'region_district' end				as region_type,
						COUNT(j.id)												as job_cnt
				FROM #job_temp j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}' )
						and ir.is_city = 1		/* only cities are needed */
	                    and ir.order_value >100000 /* update: population more than 100'000 people. Previous:population more than 20'000 people */
	                    and jr.id_region != -1  /* excluding foreign cities */

				GROUP BY jr.id_region,
						 ir.name,
						 case when ir.is_city = 1 then 'city_town' else 'region_district' end
						)a
	Order by a.job_cnt asc
		) b
;

-- job_cnt by date with remote status
SELECT  cast (j.date_created as date)						    as date,
        COUNT(j.id)												as job_cnt
into #job_by_remote
FROM #job_temp j  with (nolock)
join dbo.job_region jr  with (nolock) on jr.id_job = j.id
left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
		and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}')
		and ( ir.name = 'Remote'
				or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
				or lower(j.title) like '%télétravail%'
				or lower(j.title) like '%travail à distance%'
				or lower(j.title) like '%travail à domicile%'
				or lower(j.title) like '%en ligne%'
				or lower(j.title) like '%remote%'
				or lower(j.title) like '%online% '
				or lower(j.title) like '%work from home%'
				or lower(j.title) like '%from home%'
				or lower(j.title) like '%homeoffice%'
				or j.remote_type =1 )
GROUP BY cast (j.date_created as date)
;

-- TOP 10 job_cnt by date_diff with remote status
SELECT	*,
		row_number() over ( order by b.job_cnt desc ) AS TOPNo
into #job_TOP10_remote
FROM (
	Select TOP 30 *
	FROM (
				SELECT  j.title,
						j.salary_val1,
						j.salary_val2,
  						j.id_currency,
   						j.id_salary_rate,
						COUNT(j.id)												as job_cnt
				FROM #job_temp j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}')
						and ( ir.name = 'Remote'
								or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
								or lower(j.title) like '%télétravail%'
								or lower(j.title) like '%travail à distance%'
								or lower(j.title) like '%travail à domicile%'
								or lower(j.title) like '%en ligne%'
								or lower(j.title) like '%remote%'
								or lower(j.title) like '%online% '
								or lower(j.title) like '%work from home%'
								or lower(j.title) like '%from home%'
								or lower(j.title) like '%homeoffice%'
								or j.remote_type =1 )
				GROUP BY j.title, j.salary_val1, j.salary_val2,	j.id_currency, j.id_salary_rate
						)a
	Order by a.job_cnt desc
		) b
;

-- (NEW) job_history_cnt by date_diff with remote status
SELECT  j.date_diff											    as date_diff,
        COUNT(j.uid)											as job_cnt
into #job_history_by_remote
FROM #job_history_temp j  with (nolock)
left join dbo.info_region ir with (nolock) on j.id_region = ir.id
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and ( ir.name = 'Remote'
				or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
				or lower(j.title) like '%télétravail%'
				or lower(j.title) like '%travail à distance%'
				or lower(j.title) like '%travail à domicile%'
				or lower(j.title) like '%en ligne%'
				or lower(j.title) like '%remote%'
				or lower(j.title) like '%online% '
				or lower(j.title) like '%work from home%'
				or lower(j.title) like '%from home%'
				or lower(j.title) like '%homeoffice%' )
GROUP BY j.date_diff
;

-- (NEW) TOP 10 job_history_cnt by date_diff with remote status
SELECT	*,
		row_number() over ( order by b.job_cnt desc ) AS TOPNo
into #job_history_TOP10_remote
FROM (
	Select TOP 30 *
	FROM (
				SELECT  j.title,
						j.salary_val1,
  						j.id_currency,
   						j.id_salary_rate,
						COUNT(j.uid)								as job_cnt
				FROM #job_history_temp j  with (nolock)
				left join dbo.info_region ir with (nolock) on j.id_region = ir.id
				WHERE  j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
						and ( ir.name = 'Remote'
								or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
								or lower(j.title) like '%télétravail%'
								or lower(j.title) like '%travail à distance%'
								or lower(j.title) like '%travail à domicile%'
								or lower(j.title) like '%en ligne%'
								or lower(j.title) like '%remote%'
								or lower(j.title) like '%online% '
								or lower(j.title) like '%work from home%'
								or lower(j.title) like '%from home%'
								or lower(j.title) like '%homeoffice%')
				GROUP BY j.title, j.salary_val1, j.id_currency, j.id_salary_rate
						)a
	Order by a.job_cnt desc
		) b
;

-- (STUDENT) job_cnt by region bottom-10
SELECT	*,
		row_number() over ( order by b.job_cnt asc ) AS TOPNo
into #job_by_region_bottom_student
FROM (
	Select TOP 30 *
	FROM (
				SELECT
						jr.id_region,
						ir.name													as region_name,
						case when ir.is_city = 1
						then 'city_town' else 'region_district' end				as region_type,
						COUNT(j.id)												as job_cnt
				FROM #job_temp j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						and ir.is_city = 1		/* only cities are needed */
	                    and ir.order_value >100000 /* update: population more than 100'000 people. Previous:population more than 20'000 people */
	                    and jr.id_region != -1  /* excluding foreign cities */
						and  (lower(j.title) like '%étudiant%'
                                or lower(j.title) like '%etudiant%'
                                or lower(j.title) like '%student%') /* student jobs */
				GROUP BY jr.id_region,
						 ir.name,
						 case when ir.is_city = 1 then 'city_town' else 'region_district' end
						)a
	Order by a.job_cnt asc
		) b
;

-- (STUDENT) search_cnt by date_diff
SELECT   ss.date_diff,
         COUNT(ss.id)												as search_cnt
into #ss_by_date_student
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and s.flags & 1 = 0
		and  (lower(ss.q_kw) like '%étudiant%'
              or lower(ss.q_kw) like '%etudiant%'
              or lower(ss.q_kw) like '%student%') /* student searches */
GROUP BY ss.date_diff
;

-- (STUDENT) search_cnt by region
SELECT	 ss.q_id_region,
		 ir.name																as region_name,
         case when ir.is_city = 0 then 'region_district' else 'city_town' end	as region_type,
         COUNT(ss.id)															as search_cnt
into #ss_region_student
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
left join dbo.info_region ir  with (nolock) on ss.q_id_region = ir.id
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
	  and s.flags & 1 = 0
      and ss.q_id_region != -1	/* excluding queries without location or foreign location */
	  and ir.is_city = 1		/* only cities are needed */
	  and ir.order_value >100000 /* update: population more than 100'000 people. Previous: population more than 20'000 people */
	  and ir.order_value != -1  /* excluding foreign cities */
	  and  (lower(ss.q_kw) like '%étudiant%'
              or lower(ss.q_kw) like '%etudiant%'
              or lower(ss.q_kw) like '%student%') /* student searches */
GROUP BY ss.q_id_region,
		 ir.name,
         case when ir.is_city = 0 then 'region_district' else 'city_town' end
;

SELECT	*,
		row_number() over ( order by b.search_cnt desc ) AS TOPNo
into #ss_by_region_top_student
FROM (
			Select TOP 15 *
            FROM #ss_region_student a
			Order by a.search_cnt desc
			) b
;

SELECT	*,
		row_number() over ( order by b.search_cnt asc ) AS TOPNo
into #ss_by_region_bottom_student
FROM (
			Select TOP 15 *
            FROM #ss_region_student a
			Order by a.search_cnt asc
			) b
;

-- (STUDENT) search_cnt by user query
SELECT   lower(ss.q_kw)	as q_kw,
		 COUNT(ss.id)	as search_cnt
into #ss_top_student
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
	  and s.flags & 1 = 0
	  and ss.q_kw !=''
	  and  (lower(ss.q_kw) like '%étudiant%'
              or lower(ss.q_kw) like '%etudiant%'
              or lower(ss.q_kw) like '%student%') /* student searches */
GROUP BY lower(ss.q_kw)
;

SELECT	*,
		row_number() over ( order by b.search_cnt desc ) AS TOPNo
into #ss_TOP10_student
FROM (
			Select TOP 15 *
            FROM #ss_top_student a
			Order by a.search_cnt desc
			) b
;

-- (Internship) search_cnt by date_diff
SELECT   ss.date_diff,
         COUNT(ss.id)												as search_cnt
into #ss_by_date_intern
FROM #session_search_temp ss  with (nolock)
join #session_temp s with (nolock) on s.date_diff = ss.date_diff and s.id = ss.id_session
WHERE ss.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and s.flags & 1 = 0
		and  (lower(ss.q_kw) like '%stage%'
              or lower(ss.q_kw) like '%alternance%'
			  or lower(ss.q_kw) like '%internship%'
			  or lower(ss.q_kw) like '%intern %') /* internship searches */
GROUP BY ss.date_diff
;

-- (Internship) job_history_cnt by date_diff
SELECT  j.date_diff											    as date_diff,
        COUNT(j.uid)											as job_cnt
into #job_history_by_date_intern
FROM #job_history_temp j  with (nolock)
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and  (lower(j.title) like '%stage%'
              or lower(j.title) like '%alternance%'
			  or lower(j.title) like '%internship%'
			  or lower(j.title) like '%intern %') /* internship searches */
GROUP BY j.date_diff 
;

-- (Internship) salary statistics total by period
SELECT  j.id_salary_rate,
		j.id_currency,
		avg(j.salary_val1)											as salary_avg,
		min(j.salary_val1)											as salary_min,
		max(j.salary_val1)											as salary_max
into #salary_intern
FROM #job_history_temp j  with (nolock)
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and  (lower(j.title) like '%stage%'
              or lower(j.title) like '%alternance%'
			  or lower(j.title) like '%internship%'
			  or lower(j.title) like '%intern %') /* internship searches */
		and j.id_salary_rate is not null
		and j.salary_val1 != 0
GROUP BY j.id_salary_rate, j.id_currency
;

-- (STUDENT) salary statistics total by period
SELECT  j.id_salary_rate,
		j.id_currency,
		avg(j.salary_val1)											as salary_avg,
		min(j.salary_val1)											as salary_min,
		max(j.salary_val1)											as salary_max
into #salary_student
FROM #job_history_temp j  with (nolock)
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and  (lower(j.title) like '%étudiant%'
               or lower(j.title) like '%etudiant%'
               or lower(j.title) like '%student%') /* student jobs */
		and j.id_salary_rate is not null
		and j.salary_val1 != 0
GROUP BY j.id_salary_rate, j.id_currency
;

-- (Remote) salary statistics total by period
SELECT  j.id_salary_rate,
		j.id_currency,
		avg(j.salary_val1)											as salary_avg,
		min(j.salary_val1)											as salary_min,
		max(j.salary_val1)											as salary_max
into #salary_remote
FROM #job_history_temp j  with (nolock)
left join dbo.info_region ir with (nolock) on j.id_region = ir.id
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
		and ( ir.name = 'Remote'
				or (ir.is_toplevel = 1 and ir.order_value = 1000) /* ir.name = 'Télétravail' */
				or lower(j.title) like '%télétravail%'
				or lower(j.title) like '%travail à distance%'
				or lower(j.title) like '%travail à domicile%'
				or lower(j.title) like '%en ligne%'
				or lower(j.title) like '%remote%'
				or lower(j.title) like '%online% '
				or lower(j.title) like '%work from home%'
				or lower(j.title) like '%from home%'
				or lower(j.title) like '%homeoffice%' )
		and j.id_salary_rate is not null
		and j.salary_val1 != 0
GROUP BY j.id_salary_rate, j.id_currency
;

-- (Abroad) BSH SEARCH OF job in a country outside its borders
SELECT TOP 15
       ir.name region_name,
       count(ss.id) as search_cnt,
       ROW_NUMBER() OVER(ORDER BY count(ss.id) DESC) as TOPNo
into #search_job_for_country_outside
FROM #session_search_temp ss  with (nolock)
left join dbo.info_region ir with (nolock)
    on ss.q_id_region = ir.id
where ir.order_value = -1
GROUP BY ir.name
ORDER BY search_cnt DESC;

--(Abroad) BSH top 15 searches in the country, from abroad users
select rank_filter.* 
into #top_15_searches_for_country_outside	
	from
    (select ir.name region_name,
           lower(ss.q_kw) q_kw,
           count(ss.id) as search_cnt,
           row_number() OVER(PARTITION BY ir.name
                            ORDER BY count(ss.q_kw) DESC) TOPNo
    FROM #session_search_temp ss  with (nolock)
	left join dbo.info_region ir with (nolock)
        on ss.q_id_region = ir.id
    where order_value = -1
    and  ss.q_kw !=''
    GROUP BY ir.name, lower(ss.q_kw)) rank_filter
where TOPNo <=15
ORDER BY  region_name,search_cnt DESC;

with final_union as (

Select  s1.date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_date'			as metric,
		'total'						as metric_type,
		s1.search_cnt				as value
From #ss_by_date s1
union all
Select  s1.date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_date'			as metric,
		'remote'					as metric_type,
		s1.search_cnt				as value
From #ss_by_remote s1
union all
Select  null				as date_diff,
		null				as date,
		s2.q_id_region		as id_region,
		s2.region_name,
		s2.region_type,
		null				as q_kw,
		s2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_region_top'	    as metric,
		'total'						as metric_type,
		s2.search_cnt				as value
From #ss_by_region_top s2
union all
Select  null				as date_diff,
		null				as date,
		s2.q_id_region		as id_region,
		s2.region_name,
		s2.region_type,
		null				as q_kw,
		s2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_region_bottom'	as metric,
		'total'						as metric_type,
		s2.search_cnt				as value
From #ss_by_region_bottom s2
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		s3.q_kw,
		s3.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_TOP10'				as metric,
		'total'						as metric_type,
		s3.search_cnt				as value
From #ss_TOP10 s3
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		s3.q_kw,
		s3.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_Bottom10'			as metric,
		'total'						as metric_type,
		s3.search_cnt				as value
From #ss_bottom10 s3
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		s3.q_kw,
		s3.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_TOP10'				as metric,
		'remote'					as metric_type,
		s3.search_cnt				as value
From #ss_TOP10_remote s3
union all
Select  null				as date_diff,
		j.date				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_date'				as metric,
		'remote'					as metric_type,
		j.job_cnt					as value
From #job_by_remote j
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		j2.TOPNo,
		j2.title,
		j2.salary_val1,
		j2.salary_val2,
  		j2.id_currency,
   		j2.id_salary_rate,
		'jobs_top10'	            as metric,
		'remote'					as metric_type,
		j2.job_cnt					as value
From #job_TOP10_remote j2
union all
Select  null				as date_diff,
		null				as date,
		j2.id_region		as id_region,
		j2.region_name,
		j2.region_type,
		null				as q_kw,
		j2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_region_bottom'	    as metric,
		'total'						as metric_type,
		j2.job_cnt			    	as value
From #job_by_region_bottom j2
union all
Select  j.date_diff			as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_history_by_date'		as metric,
		'remote'					as metric_type,
		j.job_cnt					as value
From #job_history_by_remote j
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		j2.TOPNo,
		j2.title,
		j2.salary_val1,
		null				as salary_val2,
  		j2.id_currency,
   		j2.id_salary_rate,
		'jobs_history_top10'	    as metric,
		'remote'					as metric_type,
		j2.job_cnt					as value
From #job_history_TOP10_remote j2
union all
Select  null				as date_diff,
		null				as date,
		j2.id_region		as id_region,
		j2.region_name,
		j2.region_type,
		null				as q_kw,
		j2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_region_bottom'	    as metric,
		'student'					as metric_type,
		j2.job_cnt			    	as value
From #job_by_region_bottom_student j2
union all
Select  s1.date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_date'			as metric,
		'student'					as metric_type,
		s1.search_cnt				as value
From #ss_by_date_student s1
union all
Select  null				as date_diff,
		null				as date,
		s2.q_id_region		as id_region,
		s2.region_name,
		s2.region_type,
		null				as q_kw,
		s2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_region_top'	    as metric,
		'student'					as metric_type,
		s2.search_cnt				as value
From #ss_by_region_top_student s2
union all
Select  null				as date_diff,
		null				as date,
		s2.q_id_region		as id_region,
		s2.region_name,
		s2.region_type,
		null				as q_kw,
		s2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_region_bottom'	as metric,
		'student'					as metric_type,
		s2.search_cnt				as value
From #ss_by_region_bottom_student s2
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		s3.q_kw,
		s3.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_TOP10'				as metric,
		'student'					as metric_type,
		s3.search_cnt				as value
From #ss_TOP10_student s3
union all
Select  s1.date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_by_date'			as metric,
		'internship'				as metric_type,
		s1.search_cnt				as value
From #ss_by_date_intern s1
union all
Select  j.date_diff			as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_history_by_date'		as metric,
		'internship'				as metric_type,
		j.job_cnt					as value
From #job_history_by_date_intern j
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_avg'				as metric,
		'internship'				as metric_type,
		si.salary_avg				as value
From #salary_intern si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_min'				as metric,
		'internship'				as metric_type,
		si.salary_min				as value
From #salary_intern si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_max'				as metric,
		'internship'				as metric_type,
		si.salary_max				as value
From #salary_intern si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_avg'				as metric,
		'student'					as metric_type,
		si.salary_avg				as value
From #salary_student si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_min'				as metric,
		'student'					as metric_type,
		si.salary_min				as value
From #salary_student si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_max'				as metric,
		'student'					as metric_type,
		si.salary_max				as value
From #salary_student si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_avg'				as metric,
		'remote'					as metric_type,
		si.salary_avg				as value
From #salary_remote si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_min'				as metric,
		'remote'					as metric_type,
		si.salary_min				as value
From #salary_remote si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		si.id_currency,
   		si.id_salary_rate,
		'salary_max'				as metric,
		'remote'					as metric_type,
		si.salary_max				as value
From #salary_remote si
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		sja.region_name,
		null				as region_type,
		null				as q_kw,
		sja.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_TOP15_countries'				as metric,
		'abroad'					as metric_type,
		sja.search_cnt				as value
From #search_job_for_country_outside sja
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		sjtop.region_name,
		null				as region_type,
		sjtop.q_kw,
		sjtop.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'search_TOP15_keywords'				as metric,
		'abroad'					as metric_type,
		sjtop.search_cnt				as value
From #top_15_searches_for_country_outside sjtop
)

Select  ${country_id}   as country_id,
		'${YEAR_MONTH}'		as year_month,
		u.date,
		u.date_diff,
		u.id_region,
		u.region_name,
		u.region_type,
		CAST(u.q_kw as varchar) as q_kw,
		u.TOPNo,
		u.title,
		u.salary_val1,
		u.salary_val2,
  		u.id_currency,
   		u.id_salary_rate,
		u.metric,
		u.metric_type,
		CAST(u.value AS numeric(18,4)) as value
FROM final_union u
;

drop table #ss_by_date;
drop table #ss_by_remote;
drop table #ss_by_region_top;
drop table #ss_by_region_bottom;
drop table #ss_TOP10;
drop table #ss_bottom10;
drop table #ss_TOP10_remote;
drop table #job_by_region_bottom;
drop table #job_by_remote;
drop table #job_TOP10_remote;
drop table #job_history_by_remote;
drop table #job_history_TOP10_remote;
drop table #ss_region;
drop table #ss_top;

drop table #job_by_region_bottom_student
drop table #ss_by_date_student
drop table #ss_by_region_top_student
drop table #ss_by_region_bottom_student
drop table #ss_TOP10_student
drop table #ss_by_date_intern
drop table #job_history_by_date_intern
drop table #salary_intern
drop table #salary_student
drop table #salary_remote
drop table #ss_top_student
drop table #ss_region_student
drop table #search_job_for_country_outside
drop table #top_15_searches_for_country_outside

drop table #session_temp;
drop table #session_search_temp;
drop table #session_filter_action_temp;
drop table #job_temp;
drop table #job_history_temp;
