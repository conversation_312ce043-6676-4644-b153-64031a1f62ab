-- job_cnt by region TOP-10
SELECT	*,
		row_number() over ( order by b.job_cnt desc ) AS TOPNo
into #job_by_region_top
FROM (
	Select TOP 30 *
	FROM (
				SELECT
						jr.id_region,
						ir.name													as region_name,
						case when ir.is_city = 1
						then 'city_town' else 'region_district' end				as region_type,
						COUNT(j.id)												as job_cnt
				FROM dbo.job j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}' )
						and ir.is_city = 1		/* only cities are needed */
	                    and ir.order_value >=100000 /* population more than 100'000 people */
	                    and jr.id_region != -1  /* excluding foreign cities */

				GROUP BY jr.id_region,
						 ir.name,
						 case when ir.is_city = 1 then 'city_town' else 'region_district' end
						)a
	Order by a.job_cnt desc
		) b
;

-- TOP 10 job_cnt
SELECT	*,
		row_number() over ( order by b.job_cnt desc ) AS TOPNo
into #job_TOP10
FROM (
	Select TOP 30 *
	FROM (
				SELECT  j.title,
						j.salary_val1,
						j.salary_val2,
  						j.id_currency,
   						j.id_salary_rate,
						COUNT(j.id)												as job_cnt
				FROM dbo.job j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						--and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}')
				GROUP BY j.title, j.salary_val1, j.salary_val2,	j.id_currency, j.id_salary_rate
						)a
	Order by a.job_cnt desc
		) b
;
-- (Student) job_cnt by region TOP-10
SELECT	*,
		row_number() over ( order by b.job_cnt desc ) AS TOPNo
into #job_by_region_top_student
FROM (
	Select TOP 30 *
	FROM (
				SELECT
						jr.id_region,
						ir.name													as region_name,
						case when ir.is_city = 1
						then 'city_town' else 'region_district' end				as region_type,
						COUNT(j.id)												as job_cnt
				FROM dbo.job j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}' )
						and ir.is_city = 1		/* only cities are needed */
	                    and ir.order_value >=100000 /* population more than 100'000 people */
	                    and jr.id_region != -1  /* excluding foreign cities */
						and  lower(j.title) like '%student%' /* student jobs */

				GROUP BY jr.id_region,
						 ir.name,
						 case when ir.is_city = 1 then 'city_town' else 'region_district' end
						)a
	Order by a.job_cnt desc
		) b
;

-- (Student) TOP 10 job_cnt
SELECT	*,
		row_number() over ( order by b.job_cnt desc ) AS TOPNo
into #job_TOP10_student
FROM (
	Select TOP 30 *
	FROM (
				SELECT  j.title,
						j.salary_val1,
						j.salary_val2,
  						j.id_currency,
   						j.id_salary_rate,
						COUNT(j.id)												as job_cnt
				FROM dbo.job j  with (nolock)
				join dbo.job_region jr  with (nolock) on jr.id_job = j.id
				left join dbo.info_region ir with (nolock) on jr.id_region = ir.id
				WHERE cast (j.date_created as date) between '${DT_START}' and '${DT_END}'
						--and (j.date_expired is null or cast(j.date_expired as date) > '${DT_END}')
						and  lower(j.title) like '%student%' /* student jobs */
				GROUP BY j.title, j.salary_val1, j.salary_val2,	j.id_currency, j.id_salary_rate
						)a
	Order by a.job_cnt desc
		) b
;

-- job_history_cnt by date_diff
SELECT  j.date_diff											    as date_diff,
        COUNT(j.uid)											as job_cnt
into #job_history_by_date
FROM dbo.job_history j  with (nolock)
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
GROUP BY j.date_diff
;

-- (Student) job_history_cnt by date_diff
SELECT  j.date_diff											    as date_diff,
        COUNT(j.uid)											as job_cnt
into #job_history_by_date_student
FROM dbo.job_history j  with (nolock)
WHERE j.date_diff between ${DT_DIFF_START} and ${DT_DIFF_END}
			and  lower(j.title) like '%student%' /* student jobs */
GROUP BY j.date_diff
;

with final_union as (

Select  null				as date_diff,
		null				as date,
		j2.id_region		as id_region,
		j2.region_name,
		j2.region_type,
		null				as q_kw,
		j2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_region_top'	    as metric,
		'total'						as metric_type,
		j2.job_cnt			    	as value
From #job_by_region_top j2
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		j2.TOPNo,
		j2.title,
		j2.salary_val1,
		j2.salary_val2,
  		j2.id_currency,
   		j2.id_salary_rate,
		'jobs_top10'	            as metric,
		'total'						as metric_type,
		j2.job_cnt					as value
From #job_TOP10 j2
union all
Select  null				as date_diff,
		null				as date,
		j2.id_region		as id_region,
		j2.region_name,
		j2.region_type,
		null				as q_kw,
		j2.TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_region_top'	    as metric,
		'student'					as metric_type,
		j2.job_cnt			    	as value
From #job_by_region_top_student j2
union all
Select  null				as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		j2.TOPNo,
		j2.title,
		j2.salary_val1,
		j2.salary_val2,
  		j2.id_currency,
   		j2.id_salary_rate,
		'jobs_top10'	            as metric,
		'student'					as metric_type,
		j2.job_cnt					as value
From #job_TOP10_student j2
union all Select  j.date_diff			as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_date'				as metric,
		'total'						as metric_type,
		j.job_cnt					as value
From #job_history_by_date j
union all
Select  j.date_diff			as date_diff,
		null				as date,
		null				as id_region,
		null				as region_name,
		null				as region_type,
		null				as q_kw,
		null				as TOPNo,
		null				as title,
		null				as salary_val1,
		null				as salary_val2,
  		null				as id_currency,
   		null				as id_salary_rate,
		'jobs_by_date'				as metric,
		'student'					as metric_type,
		j.job_cnt					as value
From #job_history_by_date_student j

)

Select  ${country_id}		as country_id,
		'${YEAR_MONTH}'		as year_month,
		u.date,
		u.date_diff,
		u.id_region,
		u.region_name,
		u.region_type,
		CAST(u.q_kw as varchar) as q_kw,
		u.TOPNo,
		u.title,
		u.salary_val1,
		u.salary_val2,
  		u.id_currency,
   		u.id_salary_rate,
		u.metric,
		u.metric_type,
		CAST(u.value AS numeric(18,4)) as value
FROM final_union u
;

drop table #job_by_region_top;
drop table #job_TOP10;
drop table #job_by_region_top_student;
drop table #job_TOP10_student;
drop table #job_history_by_date;
drop table #job_history_by_date_student;
