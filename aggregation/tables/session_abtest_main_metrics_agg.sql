    create temp table tmp_serp_manual_conversion AS (
        SELECT countries.id                                                    AS country_id,
               replace(replace(replace(replace(replace(replace(replace(replace(conversions_source.serp_tests_groups::text,
                                                                               '[{"test_id": '::text, ''::text),
                                                                       ', "group_number": '::text, '-'::text),
                                                               ', "iteration_number": 0}, {"test_id": '::text, ' '::text),
                                                       ', "iteration_number": 1}, {"test_id": '::text, ' '::text),
                                               ', "iteration_number": 1}]'::text, ''::text),
                                       ', "iteration_number": 0}]'::text, ''::text), '['::text, ''::text), ']'::text,
                       ''::text)                                               AS group_num,
               fn_get_date_diff(conversions.date::timestamp without time zone) AS action_datediff,
               CASE
                   WHEN conversions_source.is_mobile IS TRUE THEN 1
                   ELSE 0
                   END                                                         AS device_type_id,
               CASE
                   WHEN conversions_source.is_non_local IS TRUE THEN 0
                   ELSE 1
                   END                                                         AS is_local,
               countries.name_country_eng                                      AS country_name,
               conversions_source.id_traffic_source                            AS current_traffic_source_id,
               sum(conversions_source.cnt_sessions)                            AS conversion_away_cnt,
               sum(conversions_source.cnt_conversions)                         AS conversion_cnt
        FROM imp_statistic.conversions_source
                 JOIN imp_statistic.conversions ON conversions_source.id_conversion = conversions.id
                 JOIN dimension.countries ON conversions.country::text = countries.alpha_2::text
        WHERE conversions_source.serp_tests_groups IS NOT NULL
          AND conversions_source.serp_tests_groups::text <> '[]'::text
          AND (CURRENT_DATE - conversions.date) = _conv_numer_of_dates
        GROUP BY countries.id, countries.name_country_eng,
                 (replace(replace(replace(replace(replace(replace(replace(replace(
                                                                                  conversions_source.serp_tests_groups::text,
                                                                                  '[{"test_id": '::text, ''::text),
                                                                          ', "group_number": '::text, '-'::text),
                                                                  ', "iteration_number": 0}, {"test_id": '::text,
                                                                  ' '::text),
                                                          ', "iteration_number": 1}, {"test_id": '::text, ' '::text),
                                                  ', "iteration_number": 1}]'::text, ''::text),
                                          ', "iteration_number": 0}]'::text, ''::text), '['::text, ''::text), ']'::text,
                          ''::text)), (fn_get_date_diff(conversions.date::timestamp without time zone)),
                 (
                     CASE
                         WHEN conversions_source.is_mobile IS TRUE THEN 1
                         ELSE 0
                         END),
                 (
                     CASE
                         WHEN conversions_source.is_non_local IS TRUE THEN 0
                         ELSE 1
                         END), conversions_source.id_traffic_source
    );




         create temp table tmp_abtest_agg AS (
             SELECT session_abtest_agg.country_id,
                    session_abtest_agg.group_num,
                    session_abtest_agg.action_datediff,
                    session_abtest_agg.is_returned,
                    session_abtest_agg.device_type_id                                   AS is_mobile,
                    NULL::text                                                          AS is_local,
                    NULL::text                                                          AS session_create_page_type,
                    NULL::text                                                          AS is_anomalistic,
                    session_abtest_agg.attribute_name,
                    session_abtest_agg.attribute_value,
                    session_abtest_agg.metric_name,
                    sum(session_abtest_agg.metric_value)                                AS metric_value,
                    countries.name_country_eng                                          AS country,
                    fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff) AS action_date,
                    NULL::text                                                          AS first_traffic_channel,
                    NULL::text                                                          AS current_traffic_channel
             FROM aggregation.session_abtest_agg
                      JOIN dimension.countries ON session_abtest_agg.country_id = countries.id
                      LEFT JOIN dimension.u_traffic_source ON session_abtest_agg.country_id = u_traffic_source.country AND
                                                              session_abtest_agg.traffic_source_id = u_traffic_source.id
                      LEFT JOIN dimension.u_traffic_source current_u_traffic_source
                                ON session_abtest_agg.country_id = current_u_traffic_source.country AND
                                   session_abtest_agg.current_traffic_source_id = current_u_traffic_source.id
             WHERE session_abtest_agg.group_num IS NOT NULL
               AND session_abtest_agg.action_datediff = _int_date
             GROUP BY session_abtest_agg.country_id,
                      session_abtest_agg.group_num,
                      session_abtest_agg.action_datediff,
                      session_abtest_agg.is_returned,
                      session_abtest_agg.device_type_id,
                      session_abtest_agg.attribute_name,
                      session_abtest_agg.attribute_value,
                      session_abtest_agg.metric_name,
                      countries.name_country_eng,
                      (fn_get_timestamp_from_date_diff(session_abtest_agg.action_datediff))
             UNION ALL
             SELECT serp_manual_conversion.country_id,
                    serp_manual_conversion.group_num,
                    serp_manual_conversion.action_datediff,
                    NULL::integer                                                           AS is_returned,
                    serp_manual_conversion.device_type_id                                   AS is_mobile,
                    NULL::text                                                              AS is_local,
                    NULL::text                                                              AS session_create_page_type,
                    NULL::text                                                              AS is_anomalistic,
                    NULL::character varying                                                 AS attribute_name,
                    NULL::integer                                                           AS attribute_value,
                    'conversion_away_cnt'::character varying                                AS metric_name,
                    serp_manual_conversion.conversion_away_cnt                              AS metric_value,
                    serp_manual_conversion.country_name                                     AS country,
                    fn_get_timestamp_from_date_diff(serp_manual_conversion.action_datediff) AS action_date,
                    NULL::text                                                              AS first_traffic_channel,
                    NULL::text                                                              AS current_traffic_channel
             FROM tmp_serp_manual_conversion serp_manual_conversion
             UNION ALL
             SELECT serp_manual_conversion.country_id,
                    serp_manual_conversion.group_num,
                    serp_manual_conversion.action_datediff,
                    NULL::integer                                                           AS is_returned,
                    serp_manual_conversion.device_type_id                                   AS is_mobile,
                    NULL::text                                                              AS is_local,
                    NULL::text                                                              AS session_create_page_type,
                    NULL::text                                                              AS is_anomalistic,
                    NULL::character varying                                                 AS attribute_name,
                    NULL::integer                                                           AS attribute_value,
                    'conversion_cnt'::character varying                                     AS metric_name,
                    serp_manual_conversion.conversion_cnt                                   AS metric_value,
                    serp_manual_conversion.country_name                                     AS country,
                    fn_get_timestamp_from_date_diff(serp_manual_conversion.action_datediff) AS action_date,
                    NULL::text                                                              AS first_traffic_channel,
                    NULL::text                                                              AS current_traffic_channel
             FROM tmp_serp_manual_conversion serp_manual_conversion
         );



         create temp table tmp_adsense AS (
             SELECT countries.id                                         AS country_id,
                    concat(channel.serp_test, '-', channel.test_version) AS group_num,
                    'adsense revenue'::text                              AS metric_name,
                    sum(revenue.estimated_earnings_usd)                  AS metric_value,
                    countries.name_country_eng                           AS country,
                    revenue.action_date
             FROM imp_api.adsense_custom_channels_revenue revenue
                      JOIN aggregation.dic_adsence_test_channels channel
                           ON revenue.country_code::text = channel.country_code AND
                              revenue.custom_channel_name::text = channel.custom_channel_name AND
                              revenue.action_date >= channel."Date_add"::date
                      JOIN dimension.countries ON revenue.country_code::text = countries.alpha_2::text
             WHERE channel.serp_test IS NOT NULL
               AND revenue.action_date = _date_date
             GROUP BY countries.id, (concat(channel.serp_test, '-', channel.test_version)), countries.name_country_eng,
                      revenue.action_date
         );


         create temp table tmp_account_revenue AS (
             SELECT account_revenue_abtest_agg.country_id,
                    fn_get_timestamp_from_date_diff(account_revenue_abtest_agg.date_diff) AS date,
                    account_revenue_abtest_agg.date_diff AS date_diff,
                    countries.name_country_eng                                            AS country,
                    account_revenue_abtest_agg.groups,
                    sum(account_revenue_abtest_agg.new_account_revenue)                   AS new_account_revenue,
                    sum(account_revenue_abtest_agg.new_serp_account_revenue)              AS new_serp_account_revenue,
                    sum(account_revenue_abtest_agg.new_email_account_revenue)             AS new_email_account_revenue,
                    sum(account_revenue_abtest_agg.new_account)                           AS new_account
             FROM aggregation.account_revenue_abtest_agg
                      JOIN dimension.countries ON account_revenue_abtest_agg.country_id = countries.id
             WHERE account_revenue_abtest_agg.date_diff = _int_date
             GROUP BY (fn_get_timestamp_from_date_diff(account_revenue_abtest_agg.date_diff)),
                      account_revenue_abtest_agg.date_diff, countries.name_country_eng,
                      account_revenue_abtest_agg.groups, account_revenue_abtest_agg.country_id
         );


         create temp table tmp_account_revenue_union AS (
             SELECT account_revenue.country_id,
                    account_revenue.date,
                    account_revenue.date_diff,
                    account_revenue.country,
                    account_revenue.groups,
                    NULL::text                          AS attribute_name,
                    NULL::text                          AS attribute_value,
                    'new_total_account_revenue'::text   AS metric_name,
                    account_revenue.new_account_revenue AS metric_value
             FROM tmp_account_revenue account_revenue
             UNION ALL
             SELECT account_revenue.country_id,
                    account_revenue.date,
                    account_revenue.date_diff,
                    account_revenue.country,
                    account_revenue.groups,
                    NULL::text                               AS attribute_name,
                    NULL::text                               AS attribute_value,
                    'new_serp_account_revenue'::text         AS metric_name,
                    account_revenue.new_serp_account_revenue AS metric_value
             FROM tmp_account_revenue account_revenue
             UNION ALL
             SELECT account_revenue.country_id,
                    account_revenue.date,
                    account_revenue.date_diff,
                    account_revenue.country,
                    account_revenue.groups,
                    NULL::text                                AS attribute_name,
                    NULL::text                                AS attribute_value,
                    'new_email_account_revenue'::text         AS metric_name,
                    account_revenue.new_email_account_revenue AS metric_value
             FROM tmp_account_revenue account_revenue
             UNION ALL
             SELECT account_revenue.country_id,
                    account_revenue.date,
                    account_revenue.date_diff,
                    account_revenue.country,
                    account_revenue.groups,
                    'account_flag'::text           AS attribute_name,
                    NULL::text   AS attribute_value,
                    'new_account_with_flags'::text AS metric_name,
                    account_revenue.new_account    AS metric_value
             FROM tmp_account_revenue account_revenue
         );



         create temp table tmp_final AS (
             -- 1
             SELECT abtest_agg.country_id,
                    abtest_agg.group_num,
                    abtest_agg.action_datediff,
                    abtest_agg.is_returned,
                    abtest_agg.is_mobile,
                    abtest_agg.is_local,
                    abtest_agg.session_create_page_type,
                    abtest_agg.is_anomalistic,
                    abtest_agg.attribute_name,
                    abtest_agg.attribute_value::text AS attribute_value,
                    abtest_agg.metric_name,
                    abtest_agg.metric_value,
                    abtest_agg.country,
                    abtest_agg.action_date,
                    abtest_agg.first_traffic_channel,
                    abtest_agg.current_traffic_channel,
                    'union 1'::text                  AS union_group,
                    0                                AS sessions,
                    0                                AS avg_session_duration,
                    0                                AS bounce_rate
             FROM tmp_abtest_agg abtest_agg
             UNION ALL
             SELECT abtest_agg.country_id,
                    abtest_agg.group_num,
                    abtest_agg.action_datediff,
                    abtest_agg.is_returned,
                    abtest_agg.is_mobile,
                    abtest_agg.is_local,
                    abtest_agg.session_create_page_type,
                    abtest_agg.is_anomalistic,
                    abtest_agg.attribute_name,
                    abtest_agg.attribute_value::text AS attribute_value,
                    abtest_agg.metric_name,
                    abtest_agg.metric_value,
                    abtest_agg.country,
                    abtest_agg.action_date,
                    abtest_agg.first_traffic_channel,
                    abtest_agg.current_traffic_channel,
                    'union 2'::text                  AS union_group,
                    0                                AS sessions,
                    0                                AS avg_session_duration,
                    0                                AS bounce_rate
             FROM tmp_abtest_agg abtest_agg
             UNION ALL
             SELECT ga_user_and_adsense_tests.country_id,
                    replace(replace(ga_user_and_adsense_tests.test_groups_session::text, '&#x2B'::text, ''::text),
                            '+'::text, ';'::text) AS group_num,
                    NULL::integer                 AS action_datediff,
                    NULL::integer                 AS is_returned,
                    NULL::integer                 AS is_mobile,
                    NULL::text                    AS is_local,
                    NULL::text                    AS session_create_page_type,
                    NULL::text                    AS is_anomalistic,
                    NULL::character varying       AS attribute_name,
                    NULL::text                    AS attribute_value,
                    NULL::character varying       AS metric_name,
                    NULL::numeric                 AS metric_value,
                    countries.name_country_eng    AS country,
                    ga_user_and_adsense_tests.action_date,
                    NULL::text                    AS first_traffic_channel,
                    NULL::text                    AS current_traffic_channel,
                    'union 1'::text               AS union_group,
                    ga_user_and_adsense_tests.sessions,
                    ga_user_and_adsense_tests.avg_session_duration,
                    ga_user_and_adsense_tests.bounce_rate
             FROM imp_api.ga_user_and_adsense_tests
                      JOIN dimension.countries ON ga_user_and_adsense_tests.country_id = countries.id
             WHERE ga_user_and_adsense_tests.action_date = _date_date
             AND ga_user_and_adsense_tests.action_date < '2023-07-01'::date
             UNION ALL
             SELECT ga_user_and_adsense_tests.country_id,
                    replace(replace(ga_user_and_adsense_tests.test_groups_session::text, '&#x2B'::text, ''::text),
                            '+'::text, ';'::text) AS group_num,
                    NULL::integer                 AS action_datediff,
                    NULL::integer                 AS is_returned,
                    NULL::integer                 AS is_mobile,
                    NULL::text                    AS is_local,
                    NULL::text                    AS session_create_page_type,
                    NULL::text                    AS is_anomalistic,
                    NULL::character varying       AS attribute_name,
                    NULL::text                    AS attribute_value,
                    NULL::character varying       AS metric_name,
                    NULL::numeric                 AS metric_value,
                    countries.name_country_eng    AS country,
                    ga_user_and_adsense_tests.action_date,
                    NULL::text                    AS first_traffic_channel,
                    NULL::text                    AS current_traffic_channel,
                    'union 2'::text               AS union_group,
                    ga_user_and_adsense_tests.sessions,
                    ga_user_and_adsense_tests.avg_session_duration,
                    ga_user_and_adsense_tests.bounce_rate
             FROM imp_api.ga_user_and_adsense_tests
                      JOIN dimension.countries ON ga_user_and_adsense_tests.country_id = countries.id
             WHERE ga_user_and_adsense_tests.action_date = _date_date
             AND ga_user_and_adsense_tests.action_date < '2023-07-01'::date
             UNION ALL
             -- 2
             SELECT adsense.country_id,
                    adsense.group_num,
                    NULL::integer           AS action_datediff,
                    NULL::integer           AS is_returned,
                    NULL::integer           AS is_mobile,
                    NULL::text              AS is_local,
                    NULL::text              AS session_create_page_type,
                    NULL::text              AS is_anomalistic,
                    NULL::character varying AS attribute_name,
                    NULL::text              AS attribute_value,
                    adsense.metric_name,
                    adsense.metric_value,
                    adsense.country,
                    adsense.action_date,
                    NULL::text              AS first_traffic_channel,
                    NULL::text              AS current_traffic_channel,
                    'union 1'::text         AS union_group,
                    NULL::integer           AS sessions,
                    NULL::numeric           AS avg_session_duration,
                    NULL::numeric           AS bounce_rate
             FROM tmp_adsense adsense
             UNION ALL
             SELECT adsense.country_id,
                    adsense.group_num,
                    NULL::integer           AS action_datediff,
                    NULL::integer           AS is_returned,
                    NULL::integer           AS is_mobile,
                    NULL::text              AS is_local,
                    NULL::text              AS session_create_page_type,
                    NULL::text              AS is_anomalistic,
                    NULL::character varying AS attribute_name,
                    NULL::text              AS attribute_value,
                    adsense.metric_name,
                    adsense.metric_value,
                    adsense.country,
                    adsense.action_date,
                    NULL::text              AS first_traffic_channel,
                    NULL::text              AS current_traffic_channel,
                    'union 2'::text         AS union_group,
                    NULL::integer           AS sessions,
                    NULL::numeric           AS avg_session_duration,
                    NULL::numeric           AS bounce_rate
             FROM tmp_adsense adsense
             UNION ALL
             -- 3
             SELECT account_revenue_union.country_id,
                    account_revenue_union.groups    AS group_num,
                    account_revenue_union.date_diff AS action_datediff,
                    NULL::integer                   AS is_returned,
                    NULL::integer                   AS is_mobile,
                    NULL::text                      AS is_local,
                    NULL::text                      AS session_create_page_type,
                    NULL::text                      AS is_anomalistic,
                    account_revenue_union.attribute_name,
                    account_revenue_union.attribute_value,
                    account_revenue_union.metric_name,
                    account_revenue_union.metric_value,
                    account_revenue_union.country,
                    account_revenue_union.date   AS action_date,
                    NULL::text                   AS first_traffic_channel,
                    NULL::text                   AS current_traffic_channel,
                    'union 1'::text              AS union_group,
                    NULL::integer                AS sessions,
                    NULL::numeric                AS avg_session_duration,
                    NULL::numeric                AS bounce_rate
             FROM tmp_account_revenue_union account_revenue_union
             UNION ALL
             SELECT account_revenue_union.country_id,
                    account_revenue_union.groups     AS group_num,
                    account_revenue_union.date_diff  AS action_datediff,
                    NULL::integer                    AS is_returned,
                    NULL::integer                    AS is_mobile,
                    NULL::text                       AS is_local,
                    NULL::text                       AS session_create_page_type,
                    NULL::text                       AS is_anomalistic,
                    account_revenue_union.attribute_name,
                    account_revenue_union.attribute_value,
                    account_revenue_union.metric_name,
                    account_revenue_union.metric_value,
                    account_revenue_union.country,
                    account_revenue_union.date   AS action_date,
                    NULL::text                   AS first_traffic_channel,
                    NULL::text                   AS current_traffic_channel,
                    'union 2'::text              AS union_group,
                    NULL::integer                AS sessions,
                    NULL::numeric                AS avg_session_duration,
                    NULL::numeric                AS bounce_rate
             FROM tmp_account_revenue_union account_revenue_union
             UNION ALL
         SELECT ga4_user_and_adsense_tests.country_id,
                replace(replace(ga4_user_and_adsense_tests.test_groups_session::text, '&#x2B'::text, ''::text),
                        '+'::text, ';'::text) AS group_num,
                NULL::integer                 AS action_datediff,
                NULL::integer                 AS is_returned,
                NULL::integer                 AS is_mobile,
                NULL::text                    AS is_local,
                NULL::text                    AS session_create_page_type,
                NULL::text                    AS is_anomalistic,
                NULL::character varying       AS attribute_name,
                NULL::text                    AS attribute_value,
                NULL::character varying       AS metric_name,
                NULL::numeric                 AS metric_value,
                countries.name_country_eng    AS country,
                ga4_user_and_adsense_tests.action_date,
                NULL::text                    AS first_traffic_channel,
                NULL::text                    AS current_traffic_channel,
                'union 1'::text               AS union_group,
                ga4_user_and_adsense_tests.sessions,
                ga4_user_and_adsense_tests.avg_session_duration,
                ga4_user_and_adsense_tests.bounce_rate
         FROM imp_api.ga4_user_and_adsense_tests
                  JOIN dimension.countries ON ga4_user_and_adsense_tests.country_id = countries.id
         WHERE ga4_user_and_adsense_tests.action_date  = _date_date
           AND ga4_user_and_adsense_tests.action_date >= '2023-07-01'::date
         UNION ALL
         SELECT ga4_user_and_adsense_tests.country_id,
                replace(replace(ga4_user_and_adsense_tests.test_groups_session::text, '&#x2B'::text, ''::text),
                        '+'::text, ';'::text) AS group_num,
                NULL::integer                 AS action_datediff,
                NULL::integer                 AS is_returned,
                NULL::integer                 AS is_mobile,
                NULL::text                    AS is_local,
                NULL::text                    AS session_create_page_type,
                NULL::text                    AS is_anomalistic,
                NULL::character varying       AS attribute_name,
                NULL::text                    AS attribute_value,
                NULL::character varying       AS metric_name,
                NULL::numeric                 AS metric_value,
                countries.name_country_eng    AS country,
                ga4_user_and_adsense_tests.action_date,
                NULL::text                    AS first_traffic_channel,
                NULL::text                    AS current_traffic_channel,
                'union 2'::text               AS union_group,
                ga4_user_and_adsense_tests.sessions,
                ga4_user_and_adsense_tests.avg_session_duration,
                ga4_user_and_adsense_tests.bounce_rate
         FROM imp_api.ga4_user_and_adsense_tests
                  JOIN dimension.countries ON ga4_user_and_adsense_tests.country_id = countries.id
         WHERE ga4_user_and_adsense_tests.action_date  = _date_date
           AND ga4_user_and_adsense_tests.action_date >= '2023-07-01'::date

         );


        create temp table tmp_table_to_insert as
        select country_id,
               country,
               group_num,
               action_datediff,
               is_returned,
               is_mobile,
               metric_name,
               union_group,
               attribute_value,
               sum(metric_value) as metric_value
        from tmp_final
        where metric_name in (
                              'intention_to_contact_from_jdp_cnt',
                              'intention_to_contact_from_serp_cnt',
                              'session_cnt',
                              'conversion_away_cnt',
                              'conversion_cnt',
                              'apply_cnt',
                              'apply_jdp_cnt',
                              'jdp_cnt',
                              'revenue',
                              'away_cnt',
                              'new_account_cnt',
                              'new_account_revenue',
                              'new_total_account_revenue')
            and (attribute_value = '7' or attribute_value is NULL)
        group by country_id,
                 country,
                 group_num,
                 action_datediff,
                 is_returned,
                 is_mobile,
                 metric_name,
                 union_group,
                 metric_value,
                 attribute_value;



        -- reload the day
        delete from aggregation.session_abtest_main_metrics_agg
        where action_datediff = _int_date;

        /* таблиця для зберігання даних по АБ тестам по Main Metrics за 6 місяців */
        insert into aggregation.session_abtest_main_metrics_agg(country_id, country, group_num, action_datediff,
                                                                  is_returned, is_mobile, metric_name, union_group,
                                                                  metric_value)
        select country_id,
               country,
               group_num,
               action_datediff,
               is_returned,
               is_mobile,
               metric_name,
               union_group,
               metric_value
        from tmp_table_to_insert;



    drop table if exists tmp_serp_manual_conversion;
    drop table if exists tmp_abtest_agg;
    drop table if exists tmp_adsense;
    drop table if exists tmp_account_revenue;
    drop table if exists tmp_account_revenue_union;

    drop table if exists tmp_final;
    drop table if exists tmp_table_to_insert;
