declare @current_datediff int = datediff(day, 0, dbo.ex_getdate()),
        @country_id int = (select id from openquery([dwh], 'select * from dimension.countries') where alpha_2 = substring(db_name(), 5, 2))


select id_project,
       min(cac.date_diff) as conversion_start_datediff
into #conversion_away_start
from auction.conversion_away_connection cac with (nolock)
         join dbo.session_away sa with (nolock)
              on cac.date_diff = sa.date_diff
                  and cac.id_session_away = sa.id
group by id_project

select @country_id       as country_id,
       @current_datediff as job_datediff,
       j.id_project,
       case
           when ip.apply_form = 1 and ip.apply_flags & 128 = 128 then 12/*new apply*/
           when ip.apply_form = 1 and ip.apply_flags & 128 = 0 then 11/*easy apply default*/
           when @current_datediff >= cas.conversion_start_datediff then 2/*away with conversions*/
           else 1 /*away without conversions*/
           end           as job_type,
       j.id              as job_id,
       jr.uid,
       j.id_similar_group
into #job_type
from job j with (nolock)
join job_region jr with (nolock)
  on jr.id_job = j.id
         join info_project ip with (nolock)
              on ip.id = j.id_project
         left join #conversion_away_start cas
                   on cas.id_project = j.id_project
where jr.inactive = 0
;

select country_id,
       job_datediff,
       id_project,
       job_type,
       count(distinct uid) as job_cnt,
       count(distinct id_similar_group) as unique_job_cnt
from #job_type
group by country_id,
       job_datediff,
       id_project,
       job_type
;
