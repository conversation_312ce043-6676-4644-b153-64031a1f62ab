SELECT DISTINCT e.id,
                e.country_code,
                ec.company_name,
                COALESCE(j.step, 0)                       AS step,
                COALESCE(s.on_moderation, 0)              AS has_been_in_moderation_queue,
                COALESCE(ss.additional_conf, 0)           AS additional_confirmation,
                COALESCE(successfull_mod.is_moderated, 0) AS successfull_moderation,
                COALESCE(account.is_verified, 0)          AS email_is_confirmed,
                COALESCE(job_count.job_count, 0)          AS has_job,
                COALESCE(confirmation_type.conf_type, 0)  AS conf_type,
                COALESCE(bil_info.billing, 0)             AS billing_info,
                COALESCE(bcc.card, 0)                     AS card_info,
                COALESCE(sub.bought_sub, 0)               AS bought_sub,
                COALESCE(sub_failed.failed_sub, 0)        AS failed_sub,
                COALESCE(trial.has_trial, 0)              AS used_trial,
                COALESCE(start.has_start, 0)              AS used_start,
                e.register_device,
                e.moderation_status,
                date(e.date_created)                      AS registration_date,
                e.id_traffic_source                       as traffic_source_id,
                uts.name                                  AS traffic_source
FROM employer_account.employer e
         LEFT JOIN employer_account.employer_cdp ec ON e.id_cdp = ec.id
         LEFT JOIN employer_account.u_traffic_source uts on uts.id = e.id_traffic_source
         LEFT JOIN employer_account.job_step j ON e.id = j.id_employer
         LEFT JOIN (SELECT statistics_employer_moderation.id_employer,
                           COUNT(*) AS on_moderation
                    FROM employer_account.statistics_employer_moderation
                    WHERE statistics_employer_moderation.moderation_status = 0
                      AND statistics_employer_moderation.action_type = 0
                    GROUP BY statistics_employer_moderation.id_employer) s ON e.id = s.id_employer
         LEFT JOIN (SELECT statistics_employer_moderation.id_employer,
                           COUNT(*)                                         AS additional_conf,
                           MAX(statistics_employer_moderation.date_created) AS max
                    FROM employer_account.statistics_employer_moderation
                    WHERE statistics_employer_moderation.moderation_status = 0
                      AND statistics_employer_moderation.action_type = 3
                    GROUP BY statistics_employer_moderation.id_employer) ss ON e.id = ss.id_employer
         LEFT JOIN (SELECT statistics_employer_moderation.id_employer,
                           COUNT(*)                                         AS is_moderated,
                           MAX(statistics_employer_moderation.date_created) AS max
                    FROM employer_account.statistics_employer_moderation
                    WHERE statistics_employer_moderation.moderation_status IN (1, 2)
                      AND statistics_employer_moderation.action_type = 3
                    GROUP BY statistics_employer_moderation.id_employer) successfull_mod
                   ON successfull_mod.id_employer = e.id
         LEFT JOIN account_service.account account ON account.id = e.id_account
         LEFT JOIN (SELECT j.id_employer,
                           COUNT(j.id) AS job_count
                    FROM employer_account.job j
                    GROUP BY j.id_employer) job_count ON job_count.id_employer = e.id
         LEFT JOIN (SELECT ect.id_employer,
                           MAX(ect.confirmation_type) AS conf_type
                    FROM employer_account.employer_confirmation_type ect
                    GROUP BY ect.id_employer) confirmation_type ON confirmation_type.id_employer = e.id
         LEFT JOIN (SELECT bi.id_employer,
                           COUNT(*) AS billing
                    FROM employer_account.billing_info bi
                    GROUP BY bi.id_employer) bil_info ON bil_info.id_employer = e.id
         LEFT JOIN (SELECT bi.id_employer,
                           COUNT(bc.id) AS card
                    FROM employer_account.billing_card bc
                             INNER JOIN employer_account.billing_agreement ba ON ba.id = bc.id_billing_agreement
                             INNER JOIN employer_account.billing_info bi ON bi.id = ba.id_billing_info
                    GROUP BY bi.id_employer) bcc ON bcc.id_employer = e.id
         LEFT JOIN (SELECT ps.id_employer,
                           COUNT(so.id) AS bought_sub
                    FROM employer_account.subscription_order so
                             INNER JOIN employer_account.packet_subscription ps ON so.id_subscription = ps.id
                    WHERE ps.id_packet IN (50, 51)
                      AND so.status = 2
                    GROUP BY ps.id_employer) sub ON sub.id_employer = e.id
         LEFT JOIN (SELECT ps.id_employer,
                           COUNT(so.id) AS failed_sub
                    FROM employer_account.subscription_order so
                             INNER JOIN employer_account.packet_subscription ps
                                        ON so.id_subscription = ps.id
                    WHERE ps.id_packet IN (50, 51)
                      AND so.status IN (3, 5)
                    GROUP BY ps.id_employer) sub_failed ON sub_failed.id_employer = e.id
         LEFT JOIN (SELECT ps.id_employer,
                           COUNT(ps.id) AS has_trial
                    FROM employer_account.packet_subscription ps
                    WHERE ps.id_packet = 61
                    GROUP BY ps.id_employer) trial ON trial.id_employer = e.id
         LEFT JOIN (SELECT ps.id_employer,
                           COUNT(ps.id) AS has_start
                    FROM employer_account.packet_subscription ps
                    INNER JOIN subscription_order so on so.id_subscription = ps.id
                    WHERE ps.id_packet = 62 and so.status = 2
                    GROUP BY ps.id_employer) start ON start.id_employer = e.id
WHERE e.country_code = 'rs';
