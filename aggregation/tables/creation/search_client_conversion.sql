-- auto-generated definition
create table search_client_conversion
(
    country                  smallint not null,
    search_date              date     not null,
    current_traf_source_id   integer,
    traffic_name             varchar(255),
    is_paid_traffic          boolean,
    search_query_kw          varchar(4000),
    search_query_text_region varchar(200),
    project_id               integer,
    away_revenue             numeric(18, 12),
    away_cnt                 integer,
    conversion_cnt           integer,
    conversion_percent       numeric(18, 12)
);

alter table search_client_conversion
    owner to postgres;

grant select on search_client_conversion to readonly;

grant delete, insert, select, update on search_client_conversion to ono;

grant delete, insert, references, select, trigger, truncate, update on search_client_conversion to vbe;

