create table cpc_cluster_range
(
    date               date    not null,
    country_id         integer not null,
    update_type        smallint not null,
    low_min_cpc_usd    numeric(14, 5),
    low_max_cpc_usd    numeric(14, 5),
    medium_min_cpc_usd numeric(14, 5),
    medium_max_cpc_usd numeric(14, 5),
    high_min_cpc_usd   numeric(14, 5),
    high_max_cpc_usd   numeric(14, 5),
    constraint cpc_cluster_range_pkey
        primary key (date, country_id, update_type)
);
