create table aggregation.crm_client_account
(
    id                    varchar(50),
    name                  varchar(100),
    created_on            timestamp,
    owner_name            varchar(50),
    owner_email           varchar(50),
    country               varchar(10),
    found_in              varchar(50),
    source_type           varchar(50),
    client_segment        varchar(50),
    specialization        varchar(100),
    target_action         varchar(100),
    problem_segments      varchar(100),
    is_target_cpa         smallint,
    benchmark_cpa         numeric(14,5),
    is_target_clicks      smallint,
    benchmark_clicks      numeric(14,5),
    is_target_conversions smallint,
    benchmark_conversions numeric(14,5),
    is_target_cr          smallint,
    benchmark_cr          numeric(14,5),
    is_buys_at_pja        smallint,
    via_pja               varchar(50),
    soska_user_link       varchar(50),
    soska_project_link    varchar(50),
    primary_contact_id    varchar(50),
    number_1c             varchar(50)
);