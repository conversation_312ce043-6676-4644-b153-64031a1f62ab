-- auto-generated definition
create table pyramid_agg
(
    country_id  smallint     not null,
    action_date date         not null,
    metric_name varchar(255) not null,
    metric_cnt  integer,
    is_dwh      smallint     not null,
    constraint pk_pyramid_agg
        primary key (country_id, action_date, metric_name, is_dwh)
);

alter table pyramid_agg
    owner to postgres;

grant select on pyramid_agg to readonly;

grant delete, insert, select, update on pyramid_agg to ono;

grant delete, insert, references, select, trigger, truncate, update on pyramid_agg to vbe;

