create table aggregation.project_keyword_stat
(
    country_id                  smallint       not null,
    date_diff                   integer        not null,
    id_project                  integer        not null,
    job_cpc_usd                 numeric(14, 6) not null,
    job_cnt                     integer,
    keyword_cnt                 integer,
    search_cnt                  integer,
    impression_cnt_10           integer,
    impression_cnt_20           integer,
    impression_cnt_30           integer,
    impression_cnt_40           integer,
    impression_cnt_50           integer,
    impression_on_screen_cnt_10 integer,
    impression_on_screen_cnt_20 integer,
    impression_on_screen_cnt_30 integer,
    impression_on_screen_cnt_40 integer,
    impression_on_screen_cnt_50 integer,
    click_cnt_10                integer,
    click_cnt_20                integer,
    click_cnt_30                integer,
    click_cnt_40                integer,
    click_cnt_50                integer,
    avg_cpc_usd_10              numeric(14, 5),
    avg_cpc_usd_20              numeric(14, 5),
    avg_cpc_usd_30              numeric(14, 5),
    avg_cpc_usd_40              numeric(14, 5),
    avg_cpc_usd_50              numeric(14, 5),
    constraint project_keyword_stat_pkey
        primary key (country_id, date_diff, id_project, job_cpc_usd)
);