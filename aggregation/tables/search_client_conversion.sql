declare @dd_start int = ${dt_begin},
		@country_id int = ${country_id};



         select session_away.id_project,
                min(conversion_away_connection.date_diff) as conv_date_diff
		 into #First_date_diff
         from dbo.session_away with (nolock)
                  join
              auction.conversion_away_connection with (nolock)
              on session_away.date_diff = conversion_away_connection.date_diff
                  and conversion_away_connection.id_session_away = session_away.id
         group by session_away.id_project

         SELECT session_search.q_kw,
                session_search.q_txt_region,
                session_away.id_project,
                session_away.date_diff,
                session_away.click_price,
                session_away.id         as id_away,
                session_away.id_session as id_session,
                session_away.id_currency,
                session_away.date,
                First_date_diff.conv_date_diff
		 into #Search
         from dbo.session_away with (nolock)
                  left join
              dbo.session_jdp (nolock)
              on session_away.date_diff = session_jdp.date_diff
                  and session_away.id_jdp = session_jdp.id
                  join
              dbo.session_click with (nolock)
              on session_away.date_diff = session_click.date_diff
                  and coalesce(session_away.id_click, session_jdp.id_click) = session_click.id
                  join
              dbo.session_search with (nolock)
              on session_click.date_diff = session_search.date_diff
                  and session_click.id_search = session_search.id
                  join #First_date_diff First_date_diff
                       on session_away.id_project = First_date_diff.id_project
         where session_away.date_diff = @dd_start
           and session_away.flags & 2 = 0 



         Select distinct date_diff,
                         id_session_away
		 into #Conversion
         from auction.conversion_away_connection with (nolock)
         where date_diff = @dd_start


         SELECT cast(Search.date as date)                                              as date,
                session.id_current_traf_source,
                u_traffic_source.name                                                  as traffic_name,
                u_traffic_source.is_paid                                               as traffic_is_paid,
                Search.q_kw,
                Search.q_txt_region,
                Search.id_project,
                Search.date_diff,
                coalesce(Search.click_price, 0) * coalesce(info_currency_history.value_to_usd,
                                                           info_currency.value_to_usd) as away_revenue,
                Search.id_away,
                conversion_away_connection.id_session_away                                          as id_conversion,
                conv_date_diff
		 into #Revenue
         from #Search Search
                  join
              dbo.session with (nolock)
              on Search.date_diff = session.date_diff
                  and Search.id_session = session.id
                  left join
              dbo.u_traffic_source with (nolock)
              on coalesce(session.id_current_traf_source, session.id_traf_source) = u_traffic_source.id
                  left join
              #Conversion conversion_away_connection
              on Search.date_diff = conversion_away_connection.date_diff
                  and conversion_away_connection.id_session_away = Search.id_away
                  left join
              dbo.info_currency with (nolock)
              on Search.id_currency = info_currency.id
                  LEFT JOIN
              dbo.info_currency_history WITH (NOLOCK)
              ON Search.id_currency = info_currency_history.id_currency
                  AND CAST(Search.date AS date) = CAST(info_currency_history.date AS date)
         where session.flags & 1 = 0


Select @country_id as country,
	   cast(date as datetime) as date,
       id_current_traf_source,
       traffic_name,
       traffic_is_paid,
       q_kw,
       q_txt_region,
       Revenue.id_project,
       sum(away_revenue)                                 as away_revenue,
       count(id_away)                                    as aways,
       count(id_conversion)                              as conversion,
       count(id_conversion) * 1.0 / count(id_away) * 100 as conversions_percent
from #Revenue Revenue
where Revenue.date_diff >= conv_date_diff
group by date,
         id_current_traf_source,
         traffic_name,
         traffic_is_paid,
         q_kw,
         q_txt_region,
         Revenue.id_project