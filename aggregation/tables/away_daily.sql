
SELECT countries.id                                                     as country_id,
               countries.name_country_eng                                       as country,
               countries.alpha_2                                                as country_2digits,
               cast(session_away.date as date)                                  as session_date,
               session_away.id_project,
               info_project.name                                                as project_name,
               campaign.name                                                    as campaign_name,
               session_away.ip_cc,
               case
                   when session_away.letter_type is not null
                       then concat('Letter Type ', session_away.letter_type)
                   when session_away.id_click is not null then 'Click'
                   when session_away.id_jdp is not null then 'Jdp'
                   when session_away.id_click_no_serp is not null then 'No serp'
                   else 'Other' end                                             as away_type,
               session_away.id_campaign,
               case when session_away.session_flags & 16 = 16 then 1 else 0 end as is_mobile,
               u_traffic_source.channel,
               u_traffic_source.name                                            as traffic_name,
               u_traffic_source.is_paid                                         as traffic_is_paid,
               coalesce(session_away.click_price, 0)                            as click_price,
               info_currency.value_to_usd,
               session_away.session_away_id,
               conversion_away_connection.id_session_away
        from imp.session_attached_session_away session_away
                 left join dimension.info_project
                           on session_away.session_away_country = info_project.country
                               and session_away.id_project = info_project.id
                 left join dimension.u_traffic_source
                           on session_away.session_away_country = u_traffic_source.country
                               and coalesce(session_away.id_current_traf_source,
                                            session_away.id_traf_source) = u_traffic_source.id
                 left join
             (select distinct country,
                              id_session_away
              from imp.conversion_away_connection
             ) conversion_away_connection
             on conversion_away_connection.country = session_away.session_away_country
                 and conversion_away_connection.id_session_away = session_away.session_away_id
                 left join imp.campaign
                           on session_away.session_away_country = campaign.country
                               and session_away.id_campaign = campaign.id
                 left join dimension.info_currency
                           on session_away.session_away_country = info_currency.country
                               and session_away.id_currency = info_currency.id
                 left join dimension.countries
                           on session_away.session_away_country = countries.id
        where 
              session_away.session_away_date_diff = ${DT_YES_INT}
	       	  and session_away.session_away_flags & 2 = 0
	          and session_away.session_flags & 1 = 0;