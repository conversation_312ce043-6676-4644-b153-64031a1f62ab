drop table if exists temp_info_currency;
drop table if exists temp_link_dbo_job;
drop table if exists temp_sa;
drop table if exists temp_link_dbo_job_region;
drop table if exists temp_link_auction_campaign;
drop table if exists temp_link_dbo_job_region_billing_info;
drop table if exists temp_link_auction_conversion_away_connection;

create temp table temp_info_currency as
select *
from link_dbo.info_currency;

CREATE INDEX INDEX_temp_info_currency ON temp_info_currency(id);

create temp table temp_link_dbo_job as
SELECT *
FROM link_dbo.job;

CREATE INDEX INDEX_temp_link_dbo_job ON temp_link_dbo_job(id, id_category, id_project);

create temp table temp_link_dbo_job_region as
SELECT *
FROM link_dbo.job_region;

CREATE INDEX INDEX_temp_link_dbo_job_region ON temp_link_dbo_job_region(id_job, uid);

create temp table temp_link_auction_campaign as
    SELECT *
    FROM link_auction.campaign;

CREATE INDEX INDEX_temp_link_auction_campaign ON temp_link_auction_campaign(id,currency);

create temp table temp_link_dbo_job_region_billing_info as
    SELECT *
    FROM link_dbo.job_region_billing_info;

CREATE INDEX INDEX_temp_link_dbo_job_region_billing_info ON temp_link_dbo_job_region_billing_info(uid_job);

CREATE TEMP TABLE temp_link_auction_conversion_away_connection AS
    SELECT *
    FROM link_auction.conversion_away_connection;

CREATE INDEX INDEX_temp_link_auction_conversion_away_connection
    ON temp_link_auction_conversion_away_connection(id_session_away, date_diff);

create temp table temp_sa as
            select sa.uid_job,
                   sum(sa.click_price * cast(ic.value_to_usd *1.0 as numeric))                                        as revenue_usd,
                   count(distinct sa.id)                                                        as away_cnt,
                   count(distinct case when sa.date_diff >= cs.conversion_start then sa.id end) as away_conversion_cnt,
                   count(conversion_away_connection.id_session_away)                            as conversion_cnt,
                   cast(sum(sa.click_price * ic.value_to_usd)/count(distinct sa.id)*1.0 as numeric)   as cpc_usd,
                   sum(case when sa.date_diff >= cs.conversion_start then sa.click_price * cast(ic.value_to_usd *1.0 as numeric) end) as conversion_revenue_usd
            from public.session_away sa
            left join public.session s on sa.date_diff = s.date_diff and sa.id_session = s.id and coalesce(s.flags, 0) & 1 = 0
            left join temp_info_currency ic on ic.id = sa.id_currency
            left join (select sa.id_project,
                                        min(cac.date_diff) as conversion_start
                                from temp_link_auction_conversion_away_connection as cac
                                join session_away as sa on cac.date_diff = sa.date_diff and cac.id_session_away = sa.id
                                group by sa.id_project) as cs on cs.id_project = sa.id_project
            left join (select distinct id_session_away
                        from temp_link_auction_conversion_away_connection) as conversion_away_connection on conversion_away_connection.id_session_away = sa.id
            where sa.date_diff = _dd_start /*потрібна функція для визначення попердньої дати, типу current_date - 1, замість точної дати*/
              and sa.flags & 512 = 0
              and sa.flags & 2 = 0
            group by sa.uid_job;

            create index temp_sa_idx on temp_sa (uid_job);

return query
            select
                   CAST(_dd_start AS INTEGER)          as date,
                   CAST(j.id_category as INTEGER),
                   CAST(j.id_project as INTEGER),
                   sum(sa.revenue_usd)::numeric            as revenue_usd,
                   CAST(count(distinct j.id) as BIGINT) as job_cnt,
                   CAST(count(distinct sa.uid_job) as BIGINT) as job_with_away_cnt,
                   CAST(sum(sa.away_cnt) as BIGINT)             as away_cnt,
                   CAST(sum(sa.away_conversion_cnt) as BIGINT)   as away_conversion_cnt,
                   CAST(sum(sa.conversion_cnt) as BIGINT)        as conversion_cnt,
                   CAST(count(distinct case when sa.revenue_usd>0 then j.id end) as BIGINT) as job_with_revenue_cnt,
                   PERCENTILE_CONT(0.5) WITHIN GROUP(ORDER BY case when cpc_usd=0 then null else cpc_usd end) as median_cpc_usd,
                   CAST(coalesce(sum(case when c.click_price = 0 and c.is_price_per_job = 1 then coalesce(jrbi.click_price, 0)* ic.value_to_usd else c.click_price* ic.value_to_usd end) / nullif(sum(case when c.click_price > 0 or coalesce(jrbi.click_price, 0) > 0 then 1 else 0 end),0),0) as numeric) as avg_cpc_for_paid_job_count_usd,
                   sum(sa.conversion_revenue_usd)  as conversion_revenue_usd
            from temp_link_dbo_job j
            left join temp_link_dbo_job_region jr
                on j.id = jr.id_job
            left join temp_sa sa on jr.uid = sa.uid_job
            left join temp_link_auction_campaign c
              on jr.id_campaign = c.id
            left join temp_info_currency ic
               on ic.id = c.currency
            left join temp_link_dbo_job_region_billing_info jrbi
               on jr.uid = jrbi.uid_job
            where (j.date_expired is null or cast(j.date_expired as date) > current_date)
                  and jr.inactive = 0
                  and (c.date_end is null or cast(c.date_end as date) > current_date)
                  and c.campaign_status = 0
            group by
                   j.id_category,
                   j.id_project;
