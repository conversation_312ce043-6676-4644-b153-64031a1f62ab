
with job_data as
         (select cl.job_id,
                 cl.id_similar_group,
                 job_region.uid,
                 (link_dbo.vw_job_billing_info.billing_click_price_in_usd)::numeric as cpc_usd,
                 case
                     when cnt_cluster < 2
                         then cnt_cluster
                     else 2
                     end                                                                            as cnt_cluster,
                 coalesce(job_cluster.id_cluster, 0)                                                as id_cluster,
                 coalesce(job_cluster.cluster, 'unknown')                                           as cluster
          from (select job.id                as job_id,
             job.id_similar_group,
             count(distinct job_cluster.id_cluster) as cnt_cluster
      from link_dbo.job
               inner join link_dbo.job_region
                          on job_region.id_job = job.id
               left join an.job_cluster
                         on job_cluster.id_job = job_region.uid
      where job_region.inactive = 0
        and (job.date_expired is null
          or job.date_expired >= current_date)
      group by job.id,
               job.id_similar_group) as cl
                   inner join link_dbo.job_region
                              on job_region.id_job = cl.job_id
                   left join an.job_cluster
                             on job_cluster.id_job = job_region.uid
                   left join link_dbo.vw_job_billing_info
                             on link_dbo.vw_job_billing_info.uid = job_region.uid),

     cpc_data as (select cnt_cluster,
                         case
                             when coalesce(cpc_usd::numeric, 0) > 0
                                 then 1
                             else 0
                             end               as is_paid,
                         avg(cpc_usd::numeric) as avg_cpc
                  from (select distinct uid,
                                        cnt_cluster,
                                        cpc_usd
                        from job_data) t
                  group by cnt_cluster,
                           case
                               when coalesce(cpc_usd::numeric, 0) > 0
                                   then 1
                               else 0
                               end),

     job_agg_data as (select cnt_cluster,
                             case
                                 when coalesce(cpc_usd::numeric, 0) > 0::numeric
                                     then 1
                                 else 0
                                 end                          as is_paid,
                             count(distinct job_id)           as cnt_job_id,
                             count(distinct uid)              as cnt_job_uid,
                             count(distinct id_similar_group) as cnt_simg
                      from job_data
                      group by cnt_cluster,
                               case
                                   when coalesce(cpc_usd, 0::numeric) > 0::numeric
                                       then 1
                                   else 0
                                   end),

     cpc_data_cluster as (select cnt_cluster,
                                 id_cluster,
                                 cluster,
                                 case
                                     when coalesce(cpc_usd, 0::numeric) > 0::numeric
                                         then 1
                                     else 0
                                     end               as is_paid,
                                 avg(cpc_usd::numeric) as avg_cpc
                          from (select distinct uid,
                                                cnt_cluster,
                                                id_cluster,
                                                cluster,
                                                cpc_usd
                                from job_data) as t
                          group by cnt_cluster,
                                   id_cluster,
                                   cluster,
                                   case
                                       when coalesce(cpc_usd, 0::numeric) > 0::numeric
                                           then 1
                                       else 0
                                       end),

     job_agg_data_cluster as (select cnt_cluster,
                                     id_cluster,
                                     cluster,
                                     case
                                         when coalesce(cpc_usd::numeric, 0) > 0
                                             then 1
                                         else 0
                                         end                          as is_paid,
                                     count(distinct job_id)           as cnt_job_id,
                                     count(distinct uid)              as cnt_job_uid,
                                     count(distinct id_similar_group) as cnt_simg
                              from job_data
                              group by cnt_cluster,
                                       id_cluster,
                                       cluster,
                                       case
                                           when coalesce(cpc_usd::numeric, 0) > 0
                                               then 1
                                           else 0
                                           end)

select ${country_id}                                 as country_id, --TODO!!!!! Set relevant country_id
       current_date - '1900/01/01'::date as datediff,
       job_agg_data.cnt_cluster          as cluster_cnt,
       null                              as local_cluster_id,
       'total'                           as local_cluster_name,
       job_agg_data.is_paid              as is_paid_uid,
       job_agg_data.cnt_job_id           as active_id_cnt,
       job_agg_data.cnt_job_uid          as active_uid_cnt,
       job_agg_data.cnt_simg             as active_simgroup_cnt,
       cpc_data.avg_cpc                  as avg_uid_cpc
from job_agg_data
         join cpc_data
              on cpc_data.cnt_cluster = job_agg_data.cnt_cluster
                  and cpc_data.is_paid = job_agg_data.is_paid

union all

select ${country_id}                                   as country_id,     --TODO!!!!! Set relevant country_id
       (current_date - '1900/01/01'::date) as datediff,
       jd.cnt_cluster                      as cluster_cnt,
       jd.id_cluster                       as local_cluster_id,
       jd.cluster                          as local_cluster_name,
       jd.is_paid                          as is_paid_uid,
       jd.cnt_job_id                       as active_id_cnt,
       jd.cnt_job_uid                      as active_uid_cnt,
       jd.cnt_simg                         as active_simgroup_cnt,
       cd.avg_cpc                          as avg_uid_cpc
from job_agg_data_cluster jd
         join cpc_data_cluster cd
              on cd.cnt_cluster = jd.cnt_cluster
                  and cd.is_paid = jd.is_paid
                  and cd.cluster = jd.cluster
                  and cd.id_cluster = jd.id_cluster;
