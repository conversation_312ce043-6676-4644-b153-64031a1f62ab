declare @date_diff int = :to_sqlcode_date_or_datediff_start;


with session as
    (Select id,
            date_diff
     from dbo.session with (nolock)
     where flags & 1 = 0
       and flags & 4 = 0
       and date_diff = @date_diff)

   , email_sent as
    (Select *
     from dbo.email_sent with (nolock)
     where date_diff = @date_diff)

   , email_open as
    (Select email_open.id_message,
            email_open.date_diff,
            email_sent.id_account
     from (Select email_open.id_message,
                  min(email_open.date_diff) as date_diff
           from (Select email_open.id_message,
                        email_open.date_diff
                 from dbo.email_open with (nolock)
                 union all
                 Select email_visit.id_message,
                        email_visit.date_diff
                 from dbo.email_visit with (nolock)) email_open
           group by email_open.id_message) email_open
              join
          dbo.email_sent email_sent
          on email_open.id_message = email_sent.id_message
     where email_open.date_diff = @date_diff)

   , email_visit as
    (Select email_visit.id_message,
            email_visit.date_diff,
            email_sent.id_account
     from (Select email_visit.id_message,
                  min(email_visit.date_diff) as date_diff
           from dbo.email_visit with (nolock)
           group by email_visit.id_message) email_visit
              join
          dbo.email_sent email_sent
          on email_visit.id_message = email_sent.id_message
     where email_visit.date_diff = @date_diff)

   , yesterday_emails as
    (Select id_message,
            id_account
     from email_sent email_sent
     union
     Select id_message,
            id_account
     from email_open email_open
     union
     Select id_message,
            id_account
     from email_visit email_visit)

   , away_click as
    (select coalesce(coalesce(session_away_message.id_message, session_alertview_message.id_message),
                     session_click_message.id_message)                                         as id_message,
            count(distinct session_away.id)                                                    as away_open_cnt,
            count(distinct case when session_away.id_jdp is not null then session_away.id end) as away_jdp_cnt
     from dbo.session_away with (nolock)
              join
          session session (nolock)
          on session_away.date_diff = session.date_diff
              and session_away.id_session = session.id
              left join
          dbo.session_away_message with (nolock)
          on session_away.date_diff = session_away_message.date_diff
              and session_away.id = session_away_message.id_away
              left join
          dbo.session_click with (nolock)
          on session_away.date_diff = session_click.date_diff
              and session_away.id_click = session_click.id
              left join
          dbo.session_click_message with (nolock)
          on session_click_message.date_diff = session_click.date_diff
              and session_click_message.id_click = session_click.id
              left join
          dbo.session_alertview_message with (nolock)
          on session_click.date_diff = session_alertview_message.date_diff
              and session_click.id_alertview = session_alertview_message.id_alertview
     where isnull(session_away.flags, 0) & 2 = 0
       and isnull(session_away.flags, 0) & 512 = 0
     group by coalesce(coalesce(session_away_message.id_message, session_alertview_message.id_message),
                       session_click_message.id_message))

   , email_conversion as
    (select email_sent.id_message,
            max(case when email_open.id_message is not null then 1 else 0 end)  as is_open,
            max(case when email_visit.id_message is not null then 1 else 0 end) as is_visit
     from yesterday_emails email_sent
              left join
          email_open email_open
          on email_sent.id_message = email_open.id_message
              left join
          email_visit email_visit
          on email_sent.id_message = email_visit.id_message
     group by email_sent.id_message)

   , alert as
    (select email_alert.id_account,
            count(email_alert.id) as alerts
     from dbo.email_alert with (nolock)
     where email_alert.id_type_unsub is null
     group by email_alert.id_account)

   , alertviews as
    (select email_sent.id_message,
            count(distinct id_alertview) as alertviews
     from dbo.session_alertview_message with (nolock)
              join
          yesterday_emails email_sent
          on session_alertview_message.id_message = email_sent.id_message
              and session_alertview_message.date_diff = @date_diff
     group by email_sent.id_message)

   , email_open_jdp as
    (SELECT message_id,
            count(distinct id_jdp)   as jdp_cnt,
            count(distinct id_apply) as apply_cnt
     FROM (select email_sent.id_message as message_id,
                  session_jdp.id        as id_jdp,
                  session_apply.id      as id_apply
           from dbo.email_sent with (nolock)
                    inner join
                dbo.session_alertview_message with (nolock)
                on session_alertview_message.id_message = email_sent.id_message
                    inner join
                dbo.session_click with (nolock)
                on session_alertview_message.id_alertview = session_click.id_alertview
                    inner join
                dbo.session_jdp with (nolock)
                on session_jdp.id_click = session_click.id
                    left join
                dbo.session_jdp_action with (nolock)
                on session_jdp.id = session_jdp_action.id_jdp
                    left join
                dbo.session_apply with (nolock)
                on session_jdp_action.id = session_apply.id_src_jdp_action
           where session_jdp.date_diff = @date_diff
           union all
           select email_sent.id_message as message_id,
                  session_jdp.id        as id_jdp,
                  session_apply.id      as id_apply
           from dbo.email_sent with (nolock)
                    inner join
                dbo.session_click_message with (nolock)
                on session_click_message.id_message = email_sent.id_message
                    inner join
                dbo.session_click with (nolock)
                on session_click_message.id_click = session_click.id
                    inner join
                dbo.session_jdp with (nolock)
                on session_jdp.id_click = session_click.id
                    left join
                dbo.session_jdp_action with (nolock)
                on session_jdp.id = session_jdp_action.id_jdp
                    left join
                dbo.session_apply with (nolock)
                on session_jdp_action.id = session_apply.id_src_jdp_action
           where session_jdp.date_diff = @date_diff) t
     GROUP BY message_id)


select @date_diff                                                                                   as date_diff,
       cast(cast(email_sent.date as date) as datetime)                                              as sent_date,
       email_sent.letter_type,
       case
           when email_sent.calc_result_count < 11 then calc_result_count
           when email_sent.calc_result_count > 10
               then 10 end                                                                          as calc_result_count,
       case
           when alert.alerts <= 5 then cast(alert.alerts as char)
           when alert.alerts > 5
               then '5+' end                                                                        as account_alerts,
       account.send_interval                                                                        as account_send_interval,
       account_info.id_traf_src                                                                     as account_traffic_source,
       cast(DATEFROMPARTS(year(account.date_add), month(account.date_add), 1) as datetime)          as account_date,
       sum(alertviews.alertviews)                                                                   as alertview_cnt,
       count(distinct account.id)                                                                   as account_cnt,
       count(distinct case when email_sent.date_diff = @date_diff then email_sent.id_message end)   as sent_msg,
       count(distinct case when email_conversion.is_open = 1 then email_conversion.id_message end)  as open_msg,
       count(distinct case when email_conversion.is_visit = 1 then email_conversion.id_message end) as visit_msg,
       sum(email_open_jdp.jdp_cnt)                                                                  as jdp_cnt,
       sum(email_open_jdp.apply_cnt)                                                                as apply_cnt,
       sum(away_click.away_open_cnt)                                                                as away_cnt,
       sum(away_jdp_cnt)                                                                            as away_jdp_cnt,
       count(distinct away_click.id_message)                                                        as message_with_away_cnt,
       count(distinct case
                          when alertviews.alertviews is not null
                              then email_sent.id_message end)                                       as message_with_alertview_cnt,
       count(distinct case
                          when email_open_jdp.jdp_cnt > 0
                              then email_open_jdp.message_id end)                                   as message_with_jdp_cnt,
       count(distinct case when away_jdp_cnt > 0 then away_click.id_message end)                    as message_with_jdp_away_cnt,
       null                                                                                         as account_unsub_cnt,
       null                                                                                         as alert_position,
       null                                                                                         as email_revenue,
       null                                                                                         as max_away_position
from yesterday_emails
         inner join
     dbo.email_sent with (nolock)
     on yesterday_emails.id_message = email_sent.id_message
         left join
     dbo.account with (nolock)
     on email_sent.id_account = account.id
         left join
     dbo.account_info with (nolock)
     on account.id = account_info.id_account
         left join
     email_open_jdp with (nolock)
     on email_open_jdp.message_id = email_sent.id_message
         left join
     away_click with (nolock)
     on away_click.id_message = email_sent.id_message
         left join
     email_conversion
     on email_conversion.id_message = email_sent.id_message
         left join
     alert
     on email_sent.id_account = alert.id_account
         left join
     alertviews
     on email_sent.id_message = alertviews.id_message
group by cast(email_sent.date as date), email_sent.letter_type,
         case
             when email_sent.calc_result_count < 11 then calc_result_count
             when email_sent.calc_result_count > 10 then 10 end,
         case
             when alert.alerts <= 5 then cast(alert.alerts as char)
             when alert.alerts > 5 then '5+' end,
         account.send_interval,
         DATEFROMPARTS(year(account.date_add), month(account.date_add), 1),
         account_info.id_traf_src
;
