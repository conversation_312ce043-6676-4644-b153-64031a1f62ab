with click_data as (
    select
        s2.id_job,
        s2.uid,
        s2.placement,
        s2.is_paid_source,
  		s2.channel,
        sum(s2.click_price_usd) revenue_usd,
        sum(is_paid_click) as cnt_paid_clicks,
        count(distinct id_away) as cnt_away,
        count(distinct s2.id_jdp) as cnt_jdp,
        s2.date_diff
    from(
        select distinct
            s1.is_paid_source,
      		s1.channel,
            s1.click_price_usd,
            s1.id_jdp,
            s1.id_away,
            s1.id_job,
            s1.uid,
            s1.date_diff,
            case
                when s1.letter_type = 8
                    then 'letter type 8'
                when s1.id_recommend is not null
                    then 'recommendations'
                when s1.id_alertview is not null
                    then 'alertview'
                when s1.id_search is not
                    null then 'search'
                when s1.id_external is not null
                    then 'external'
                else 'other'
            end as placement,
            case
                when s1.click_price_usd > 0
                    then 1
                else 0
            end as is_paid_click
        from (
            select
                coalesce(ts.is_paid::int, 0) as is_paid_source,
                ts.channel,
                jr.id_job as id_job,
                jr.uid as uid,
                case
                    when (sa.id_campaign = 0 or au.flags & 2 = 0) and coalesce(sa.flags, 0) & 2 = 0 and coalesce(sa.flags, 0) & 512 = 0
                    then sa.click_price::numeric * ic.value_to_usd::numeric
                    else 0
                end as click_price_usd,
                coalesce(sa.letter_type, sj.letter_type) as letter_type,
                coalesce(sc.id_recommend, scj.id_recommend) as id_recommend,
                coalesce(sc.id_alertview, scj.id_alertview) as id_alertview,
                coalesce(sc.id_search, scj.id_search) as id_search,
                ext.id as id_external,
                sj.id as id_jdp,
                sa.id as id_away,
                sa.date_diff
            from public.session_away sa
            inner join public.session s on sa.date_diff = s.date_diff
                and sa.id_session = s.id
            inner join link_dbo.info_currency ic on ic.id = sa.id_currency
            left join link_auction.campaign_raw ac on ac.id = sa.id_campaign
            left join link_auction.site ast on ac.id_site = ast.id
            left join link_auction.user_raw au on au.id = ast.id_user
            -- serp -> away
            left join public.session_click sc on sc.date_diff = sa.date_diff
                and sc.id = sa.id_click
            -- serp -> jdp -> away
            left join public.session_jdp sj on sj.date_diff = sa.date_diff
                and sj.id = sa.id_jdp
            left join public.session_click scj on scj.date_diff = sj.date_diff
                and scj.id = sj.id_click
            left join public.session_external ext on ext.date_diff = sa.date_diff
                and ext.id_away = sa.id
            left join link_dbo.job_region jr on sa.uid_job = jr.uid
            left join link_dbo.u_traffic_source ts on s.id_current_traf_source = ts.id -- on s.id_traf_source = ts.id
            where
                sa.date_diff between ${dt_begin} and ${dt_begin}
                and coalesce(s.flags, 0) & 1 = 0
                and
                (
                    sa.id_campaign = 0
                    or au.flags & 2 = 0 /*client uses his external statistics for traffic*/
                )

            union all

            select
                coalesce(ts.is_paid::int, 0) as is_paid_source,
                ts.channel,
                jr.id_job as id_job,
                jr.uid as uid,
                case
                    when (sc.id_campaign = 0 or au.flags & 2 = 2) and coalesce(sc.flags, 0) & 16 = 0 and coalesce(sc.flags,0)&4096=0
                    then sc.click_price::numeric * ic.value_to_usd::numeric
                    else 0
                end as click_price_usd,
                coalesce(sa.letter_type, sj.letter_type) as letter_type,
                sc.id_recommend,
                sc.id_alertview,
                sc.id_search,
                ext.id as id_external,
                sj.id as id_jdp,
                sa.id as id_away,
                sc.date_diff
            from public.session_click sc
            inner join public.session s on sc.date_diff = s.date_diff
                and sc.id_session = s.id
            inner join link_dbo.info_currency ic on ic.id = sc.id_currency
            left join link_auction.campaign_raw ac on ac.id = sc.id_campaign
            left join link_auction.site ast on ac.id_site = ast.id
            left join link_auction.user_raw au on au.id = ast.id_user
            left join link_dbo.session_away sa on sc.date_diff = sa.date_diff
                and sc.id = sa.id_click
            left join public.session_jdp sj on sj.date_diff = sa.date_diff
                and sj.id = sa.id_jdp
            left join public.session_external ext on ext.date_diff = sc.date_diff
                and (ext.id_away = sa.id or ext.id_jdp = sj.id)
            left join link_dbo.job_region jr on sa.uid_job = jr.uid
            left join link_dbo.u_traffic_source ts on s.id_traf_source = ts.id
            where
                sc.date_diff between ${dt_begin} and ${dt_begin}
                and coalesce(s.flags, 0) & 1 = 0
                and
                (
                  sc.id_campaign = 0
                  or au.flags & 2 = 2 /*client uses his external statistics for traffic*/
                )

            -- count jdp clicks that haven't completed with away click
            union all

            select
                coalesce(CAST(ts.is_paid as int), 0) as is_paid_source,
                ts.channel,
                jr.id_job as id_job,
                jr.uid as uid,
                0 as click_price_usd,
                sj.letter_type as letter_type,
                sc.id_recommend,
                sc.id_alertview,
                sc.id_search,
                null as id_external,
                sj.id as id_jdp,
                null as id_away,
                sj.date_diff
            from session_jdp sj
            inner join public.session s on sj.date_diff = s.date_diff
            and sj.id_session = s.id
            left join public.session_click sc on sj.date_diff = sc.date_diff
            and sj.id_click = sc.id
            left join link_dbo.job_region jr on sj.uid_job = jr.uid
            left join link_dbo.u_traffic_source ts on s.id_traf_source = ts.id
            left join public.session_away sa
                on sj.date_diff = sa.date_diff
                and sj.id = sa.id_jdp
            where
                sj.date_diff between ${dt_begin} and ${dt_begin}
                and sj.id_ref_action is null
                and coalesce(s.flags, 0) & 1 = 0
                and sa.id is null

            union all

            SELECT
                coalesce(ts.is_paid::int, 0) as is_paid_source,
                ts.channel,
                jr.id_job as id_job,
                jr.uid as uid,
                case
                    when (scns.id_campaign = 0 or au.flags & 2 = 2) and coalesce(scns.flags, 0) & 16 = 0 and coalesce(scns.flags,0)&4096=0
                    then scns.click_price::numeric * ic.value_to_usd::numeric
                    else 0
                end as click_price_usd,
                scns.letter_type as letter_type,
                scns.id_recommend,
                null as id_alertview,
                null as id_search,
                ext.id as id_external,
                sj.id as id_jdp,
                sa.id as id_away,
                scns.date_diff
            FROM link_dbo.session_click_no_serp scns
            inner join public.session s
                on scns.date_diff = s.date_diff
                and s.id = scns.id_session
            inner join link_dbo.info_currency ic
                on ic.id = scns.id_currency
            left join link_auction.campaign_raw ac on ac.id = scns.id_campaign
            left join link_auction.site ast on ac.id_site = ast.id
            left join link_auction.user_raw au on au.id = ast.id_user
            left join link_dbo.session_away sa on scns.date_diff = sa.date_diff
                and scns.id = sa.id_click_no_serp
            left join public.session_jdp sj on sj.id_click_no_serp = scns.id
                    and sj.date_diff = scns.date_diff
            left join public.session_external ext on ext.date_diff = scns.date_diff
                and (ext.id_away = sa.id or ext.id_jdp = sj.id)
            left join link_dbo.job_region jr on scns.uid_job = jr.uid
            left join link_dbo.u_traffic_source ts on s.id_traf_source = ts.id
            where
                (scns.click_flags & 256 > 0 or scns.click_flags & 512 > 0)
                and scns.date_diff between ${dt_begin} and ${dt_begin}
                and coalesce(s.flags, 0) & 1 = 0
                and au.flags & 2 = 2
            ) s1
    ) s2
    group by
        s2.id_job,
        s2.uid,
        s2.placement,
        s2.is_paid_source,
  		s2.channel,
        s2.date_diff
),

job_data as(
    select
  		cd.id_job,
        count(distinct jc.id_cluster) as cnt_cluster
    from click_data cd
    left join an.job_cluster jc
        on jc.id_job=cd.uid
    group by cd.id_job
)



select
	${country_id} as country_id, --TODO!!!!!!!!!!!!!! Set relevant id_country
    cd.date_diff as datediff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end as cluster_cnt,
    placement,
    is_paid_source,
    channel,
    NULL as local_cluster_id,
    'total' as local_cluster_name,
    sum(revenue_usd) as revenue_usd,
    sum(cnt_paid_clicks) as paid_clicks_cnt,
    sum(cnt_away) as away_clicks_cnt,
    sum(cnt_jdp) as jdp_clicks_cnt
from click_data cd
left join job_data jd
	on cd.id_job = jd.id_job
group by
    cd.date_diff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end,
    placement,
    is_paid_source,
    channel

union all

select
	${country_id} as country_id,     --TODO!!!!! Set relevant id_country
    cd.date_diff as datediff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end as cluster_cnt,
    placement,
    is_paid_source,
    channel,
    coalesce(jc.id_cluster, 0) as local_cluster_id,
    coalesce(jc.cluster, 'unknown') as local_cluster_name,
    sum(revenue_usd) as revenue_usd,
    sum(cnt_paid_clicks) as paid_clicks_cnt,
    sum(cnt_away) as away_clicks_cnt,
    sum(cnt_jdp) as jdp_clicks_cnt
from click_data cd
left join job_data jd
	on cd.id_job = jd.id_job
left join
	(
      	select distinct id_job, id_cluster, cluster
      	from an.job_cluster
      	where id_job in (select uid from click_data)
    ) jc
	on cd.uid = jc.id_job
group by
    cd.date_diff,
	case
    	when cnt_cluster < 2 then cnt_cluster
        else 2
    end,
    placement,
    is_paid_source,
    channel,
    coalesce(jc.id_cluster, 0),
    coalesce(jc.cluster, 'unknown')
