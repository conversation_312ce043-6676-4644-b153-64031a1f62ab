       
declare @date_from date = :to_sqlcode_date_or_datediff_start,
        @date_to date = :to_sqlcode_date_or_datediff_end,
        @from_email_click_flag int = 256,
        @from_external_click_flag int = 512;



select ac.id, ac.flags, au.flags as user_flags, au.company, ac.name, au.id as id_user, au.budget as user_budget,
        au.daily_budget as user_daily_budget,
ac.budget as cmp_budget, ac.daily_budget as cmp_daily_budget, acf.id as category
into #auc_campaigns
    from [auction].[campaign] ac with (nolock)
    inner join [auction].[site] ast with (nolock) on ac.id_site = ast.id
    inner join [auction].[user] au with (nolock) on au.id = ast.id_user
    left join (select distinct campaign_feature.id_campaign id
                from [auction].[campaign_feature] with (nolock)
                where campaign_feature.feature & 256 = 256 ) AS acf on acf.id = ac.id

declare
    @click_flags_from_jdp_mask int = 2,
    @click_flags_to_jdp_mask int = 32,
    @flags_jdp_mask int = 32,
    @flags_away_test_mask int = 2,
    @flags_click_test_mask int = 16,
    @flags_duplicate_click int = 4096,
    @flags_duplicate_away int = 512,
    @auction_flags_paid_traffic_not_needed int = 32,
    @date_diff_from int = datediff(day, 0, @date_from),
    @month_date_diff int = datediff(m, 0, @date_from),
    @date_diff_to int = datediff(day, 0, @date_to)

--NOT EXTERNAL STATISTICS
    select
        sa.date as date,
        sa.id_project as id_project,
        sa.id_job as id_job,
        ip.name as site,
        auc.company as contractor,
        auc.id as id_campaign,
        auc.name as campaign,
        sa.rel_bonus as rel_bonus,
        sa.click_price as click_price,
        ss.id as id_session,
        ss.ip as ip,
        ss.flags as session_flags,
        ic.name as currency,
        (select top 1 value_to_usd from dbo.info_currency_history where date <= sa.date and id_currency = sa.id_currency order by date desc) as value_to_usd,
        auc.id_user as id_user,
        CONVERT(bit, isnull(sa.flags, 0) & @flags_away_test_mask) as test_click,
        case when (ts.id is null or ts.is_paid = 0) then 1 else 0 end as is_organic,
        case when ts.is_paid = 1 and (select top 1 flags from auction.campaign_log where date <= sa.date and id_campaign = auc.id order by date desc) & @auction_flags_paid_traffic_not_needed = 32 then 1 else 0 end as is_paid_overflow,
        iif(sa.flags & @flags_duplicate_away != 0, 1, 0) as is_duplicated,
        auc.flags as flags,
        iif(sa.flags & @flags_duplicate_away = 0, 1, 0)  as away,
        case when ts.channel = 'Affiliate' and ts.is_paid = 1 and sa.flags & @flags_duplicate_away = 0 then 1 else 0 end as is_paid_affiliate,
        case when click_price = 0 or click_price IS NULL then 1 else 0 end as is_free_clicks
    into #temp
    from dbo.session_away sa with(nolock)
    inner join #auc_campaigns auc on sa.id_campaign = auc.id
    left join dbo.info_currency ic with (nolock) on ic.id = sa.id_currency
    inner join dbo.session ss with(nolock) on sa.date_diff = ss.date_diff and ss.id = sa.id_session
    inner join dbo.info_project ip with (nolock) on ip.id = sa.id_project
    left join dbo.u_traffic_source ts with(nolock) on ts.id = ss.id_current_traf_source
    where
        ss.date_diff between @date_diff_from - 1 and @date_diff_to
        and sa.date_diff = @date_diff_from
        and sa.date >= @date_from and sa.date < @date_to
        and (ss.flags is null or ss.flags & 1 <> 1)
        and auc.user_flags & 2 = 0
        and coalesce(lower(ip.name), '') not like 'j-vers.%'
    union all
    select
        sc.date as date,
        sc.id_project as id_project,
        sc.id_job as id_job,
        ip.name as site,
        auc.company as contractor,
        auc.id as id_campaign,
        auc.name as campaign,
        sc.rel_bonus as rel_bonus,
        sc.click_price as click_price,
        ss.id as id_session,
        ss.ip as ip,
        ss.flags as session_flags,
        ic.name as currency,
        (select top 1 value_to_usd from dbo.info_currency_history where date <= sc.date and id_currency = sc.id_currency order by date desc) as value_to_usd,
        auc.id_user as id_user,
        convert(bit, isnull(sc.flags, 0) & @flags_click_test_mask) as test_click,
        (case when (ts.id is null or ts.is_paid = 0) then 1 else 0 end) as is_organic,
        case when ts.is_paid = 1 and (select top 1 flags from auction.campaign_log where date <= sc.date and id_campaign = auc.id order by date desc) & @auction_flags_paid_traffic_not_needed = 32 then 1 else 0 end as is_paid_overflow,
        iif(sc.flags & @flags_duplicate_click != 0, 1, 0) as is_duplicated,
        auc.flags as flags,
        case when sc.flags & @flags_duplicate_click = 0 and sc.job_destination = 1 then 1 else 0 end away,
        case when ts.channel = 'Affiliate' and ts.is_paid = 1 and sc.flags & @flags_duplicate_click = 0 then 1 else 0 end as is_paid_affiliate,
        case when click_price = 0 or click_price IS NULL then 1 else 0 end as is_free_clicks
    from (select
            date,
            id_project,
            id_job,
            rel_bonus,
            click_price,
            id_currency,
            flags,
            id_campaign,
            id_session,
            date_diff,
            job_destination
        from dbo.session_click with(nolock)
        union all
        select
            date,
            id_project,
            id_job,
            rel_bonus,
            click_price,
            id_currency,
            flags,
            id_campaign,
            id_session,
            date_diff,
            job_destination
        from dbo.session_click_no_serp scns with(nolock)) sc
    inner join #auc_campaigns auc on sc.id_campaign = auc.id
    left join dbo.info_currency ic with (nolock) on ic.id = sc.id_currency
    inner join dbo.session ss with(nolock) on sc.date_diff = ss.date_diff and ss.id = sc.id_session
    inner join dbo.info_project ip with (nolock) on ip.id = sc.id_project
    left join dbo.u_traffic_source ts with(nolock) on ts.id = ss.id_current_traf_source
    where
        sc.date_diff between @date_diff_from and @date_diff_to
        and ss.date_diff between @date_diff_from - 1 and @date_diff_to
        and sc.date >= @date_from and sc.date < @date_to
        and (ss.flags is null or ss.flags & 1 <> 1)
        and auc.user_flags & 2 = 2
        and coalesce(lower(ip.name), '') not like 'j-vers.%'

    select
        format(CONVERT(date, max(t.date)), 'yyyy-MM-dd') as date,
        max(id_project) as id_project,
        max(site) as site,
        max(contractor) as contractor,
        max(id_campaign) as id_campaign,
        max(campaign) as campaign,
        sum(case when is_duplicated = 0 and test_click = 0 then 1 else 0 end) as click_count,
        click_price as click_price,
        max(currency) as currency,
        rel_bonus,
        count(distinct id_job) job_count,
        count(distinct id_session) session_count,
        count(distinct ip) ip_count,
        sum(case when is_duplicated = 0 and test_click = 0 then click_price * value_to_usd else 0 end) as total_value,
        sum(case when is_duplicated = 0 and test_click = 0 then click_price else 0 end) as total_value_origin_currency,
        max(t.id_user) as id_user,
        sum(case when is_duplicated = 0 then test_click else 0 end) as test_count,
        sum(case when is_duplicated = 0 and test_click = 0 then is_organic else 0 end) as organic_count,
        sum(case when is_duplicated = 0 and test_click = 0 then is_paid_overflow else 0 end) as paid_overflow_count,
        max(t.flags) as flags,
        sum(click_price * value_to_usd) * isnull((select top 1 discount from dbo.vw_info_project_discount d (nolock) where d.id_project = id_project and datediff(m, 0, d.date) <= @month_date_diff order by d.date desc), 0) as total_value_discount,
        sum(is_duplicated) as duplicated_count,
        sum(case when is_duplicated = 0 and test_click = 0 then away else 0 end) as away_count,
        max(cmp.user_budget) as user_budget,
        max(cmp.user_daily_budget) as user_daily_budget,
        max(case when cmp.flags & 256 = 256 then cmp.cmp_budget end) as cmp_budget,
        max(case when cmp.user_flags & 16384= 16384 then cmp.cmp_daily_budget end ) as cmp_daily_budget,
        sum(case when is_duplicated = 0 and test_click = 0 then is_paid_affiliate else 0 end) as is_paid_affiliate,
        sum(case when is_paid_affiliate = 1 and is_duplicated = 0 and test_click = 0 then click_price * value_to_usd end) as affiliate_revenue,
        sum(case when is_duplicated = 0 and test_click >0 then click_price * value_to_usd else 0 end) as test_revenue_usd,
        case when cmp.category IS NOT NULL then 1 else 0 end as is_category_segmentation,
        sum(case when is_duplicated = 0 and test_click = 0 then is_free_clicks end) as free_clicks,
  		sum(case when is_duplicated = 0 and test_click = 0 and coalesce(is_free_clicks,0) = 0 then 1 else 0 end) as paid_clicks
    from #temp t
    left join #auc_campaigns cmp
    on t.id_campaign = cmp.id
    group by id_project,
                id_campaign,
                click_price,
                rel_bonus,
                case when cmp.category IS NOT NULL then 1 else 0 end;
