declare
@dt_begin int = ${dt_begin},
@dt_end int = ${dt_end},
@country_id int = ${country_id};

/* Until 07.02.2023 - data only for salary pages.
   After 07.02.2023, we record data for other additional pages. 
   Added coalesce(...,0) condition for script.
 */

select @country_id                                        as country_id,
       s.date_diff                                        as session_datediff,
       case
           when s.session_create_page_type = 8 then 'salary'
           when s.session_create_page_type = 10 then 'company'
           when s.session_create_page_type = 11 then 'career-skills'
           when s.session_create_page_type = 12 then 'career-job-description'
           end                                            as page_name,
       'organic revenue'                                  as revenue_type,
       s.id_current_traf_source                           as traffic_source_id,
       sign(s.flags & 16)                                 as device_type_id,
       s.session_create_page_type,
       ss.search_source                                   as search_source_id,
       coalesce(count(distinct ss.id), 0)                 as search_cnt,
       coalesce(count(distinct sc.id), 0)                 as serp_click_cnt,
       coalesce(count(distinct sa.id), 0)                 as away_cnt,
       coalesce(sum(sa.click_price * ic.value_to_usd), 0) as revenue_usd
from dbo.session s
         join dbo.session_search ss
              on s.date_diff = ss.date_diff and
                 s.id = ss.id_session
         left join dbo.session_click sc
                   on ss.id = sc.id_search and
                      ss.date_diff = sc.date_diff
         left join dbo.info_currency ic
                   on ic.id = sc.id_currency
         left join dbo.session_away sa
                   on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
where s.date_diff between @dt_begin and @dt_end
  and s.flags & 1 = 0
  and s.flags & 64 = 0
  and s.session_create_page_type in
      (8 /*SalaryPage*/, 10 /*CompanyPage*/, 11 /*SkillsPage*/, 12 /*CareerJobDescriptionPage*/)
group by s.date_diff,
         case
             when s.session_create_page_type = 8 then 'salary'
             when s.session_create_page_type = 10 then 'company'
             when s.session_create_page_type = 11 then 'career-skills'
             when s.session_create_page_type = 12 then 'career-job-description'
             end,
         s.id_current_traf_source,
         sign(s.flags & 16),
         s.session_create_page_type,
         ss.search_source

union

select @country_id                                        as country_id,
       s.date_diff                                        as session_datediff,
       case
           when ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144) then 'salary'
           when ss.search_source in (146, 147, 148, 149, 150) then 'company'
           when ss.search_source in (151, 152, 153, 154) then 'career-skills'
           when ss.search_source in (155, 156, 157, 158) then 'career-job-description'
           end                                            as page_name,
       'main site revenue'                                as revenue_type,
       s.id_current_traf_source                           as traffic_source_id,
       sign(s.flags & 16)                                 as device_type_id,
       s.session_create_page_type,
       ss.search_source                                   as search_source_id,
       coalesce(count(distinct ss.id), 0)                 as search_cnt,
       coalesce(count(distinct sc.id), 0)                 as serp_click_cnt,
       coalesce(count(distinct sa.id), 0)                 as away_cnt,
       coalesce(sum(sa.click_price * ic.value_to_usd), 0) as revenue_usd
from dbo.session s
         join dbo.session_search ss
              on s.date_diff = ss.date_diff and
                 s.id = ss.id_session
         left join dbo.session_click sc
                   on ss.id = sc.id_search and
                      ss.date_diff = sc.date_diff
         left join dbo.info_currency ic
                   on ic.id = sc.id_currency
         left join dbo.session_away sa
                   on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
where ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144, /*salary*/
                           146, 147, 148, 149, 150, /*company*/
                           151, 152, 153, 154, /*career-skills*/
                           155, 156, 157, 158 /*career-job-description*/
    )
  and s.date_diff between @dt_begin and @dt_end
  and s.flags & 1 = 0
  and s.flags & 64 = 0
  and not s.session_create_page_type in
          (8 /*SalaryPage*/, 10 /*CompanyPage*/, 11 /*SkillsPage*/, 12 /*CareerJobDescriptionPage*/)
group by s.date_diff,
         case
             when s.session_create_page_type = 8 then 'salary'
             when s.session_create_page_type = 10 then 'company'
             when s.session_create_page_type = 11 then 'career-skills'
             when s.session_create_page_type = 12 then 'career-job-description'
             end,
         s.id_current_traf_source,
         sign(s.flags & 16),
         s.session_create_page_type,
         ss.search_source;
