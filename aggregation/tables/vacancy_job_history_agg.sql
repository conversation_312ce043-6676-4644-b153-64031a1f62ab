-- Country scope: FR, UK, DE

declare _first_day date := (SELECT (date_trunc('month', current_date - interval '1 month'))::date as first_day);
declare _last_day date := (SELECT (date_trunc('month', current_date::date) - interval '1 day')::date as last_day);

-- historical jobs count by year_month
create temp table j_by_date as
  SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
         count(distinct j.id)               as jobs_cnt,
         count(distinct j.id_similar_group) as unique_jobs_cnt
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between _first_day and _last_day
        and (j.date_expired is null or cast(j.date_expired as date) > _last_day)
Group by to_char(j.date_created, 'YYYYMM')
;

-- historical jobs count by region top 10
create temp table j_by_region as
SELECT to_char(j.date_created, 'YYYYMM')    as year_month,
       j.id_region                          as id_region,
       count(distinct j.id)                 as jobs_cnt
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between _first_day and _last_day
        and (j.date_expired is null or cast(j.date_expired as date) > _last_day)
Group by to_char(j.date_created, 'YYYYMM'), j.id_region
;

create temp table j_by_region_top as
SELECT *, row_number() over ( order by b.jobs_cnt desc ) AS TOPNo
FROM (      SELECT *
            FROM j_by_region j
            Order by j.jobs_cnt desc
            Limit 30 ) b
;

-- historical jobs count by vacancies top 10
create temp table j_TOP10 as
SELECT *, row_number() over ( order by b.jobs_cnt desc ) AS TOPNo
FROM (      Select *
            FROM (
              SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
                     j.title,
                     j.salary_val1,
                     j.salary_val2,
                     j.id_currency,
                     j.id_salary_rate,
                     count(distinct j.id)               as jobs_cnt
              FROM public.job j (nolock)
              WHERE cast(j.date_created as date) between _first_day and _last_day
                    and (j.date_expired is null or cast(j.date_expired as date) > _last_day)
              Group by to_char(j.date_created, 'YYYYMM'), j.title, j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
                ) a
            Order by a.jobs_cnt desc
            Limit 30 ) b
;

-- historical jobs count by date and year_month STUDENT_job
create temp table j_by_date_student as
  SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
         count(distinct j.id)               as jobs_cnt
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between _first_day and _last_day
            and  (lower(j.title) like '%étudiant%'
                or lower(j.title) like '%etudiant%'
                or lower(j.title) like '%student%'
                or lower(j.title) like '%studierenden%') 
Group by j.date_created::date,
         to_char(j.date_created, 'YYYYMM')
;

-- historical jobs count by region top 10 STUDENT_job
create temp table j_by_region_student as
SELECT to_char(j.date_created, 'YYYYMM')    as year_month,
       j.id_region                          as id_region,
       count(distinct j.id)                 as jobs_cnt
FROM public.job j (nolock)
WHERE cast(j.date_created as date) between _first_day and _last_day
            and  (lower(j.title) like '%étudiant%'
                or lower(j.title) like '%etudiant%'
                or lower(j.title) like '%student%'
                or lower(j.title) like '%studierenden%')
Group by to_char(j.date_created, 'YYYYMM'), j.id_region
;

create temp table j_by_region_top_student as
SELECT *, row_number() over ( order by b.jobs_cnt desc ) AS TOPNo
FROM (      SELECT *
            FROM j_by_region_student j
            Order by j.jobs_cnt desc
            Limit 30 ) b
;

-- historical jobs count by vacancies top 10 STUDENT_job
create temp table j_TOP10_student as
SELECT *, row_number() over ( order by b.jobs_cnt desc ) AS TOPNo
FROM (      Select *
            FROM (
              SELECT to_char(j.date_created, 'YYYYMM')  as year_month,
                     j.title,
                     j.salary_val1,
                     j.salary_val2,
                     j.id_currency,
                     j.id_salary_rate,
                     count(distinct j.id)               as jobs_cnt
              FROM public.job j (nolock)
              WHERE cast(j.date_created as date) between _first_day and _last_day
                        and  (lower(j.title) like '%étudiant%'
                                or lower(j.title) like '%etudiant%'
                                or lower(j.title) like '%student%'
                                or lower(j.title) like '%studierenden%')
              Group by to_char(j.date_created, 'YYYYMM'), j.title, j.salary_val1, j.salary_val2, j.id_currency, j.id_salary_rate
                ) a
            Order by a.jobs_cnt desc
            Limit 30 ) b
;

-- final union with all metrics
create temp table  final_union as
    Select
           j1.year_month,
           null::integer                 as id_region,
           null::text                    as title,
           null::double precision        as salary_val1,
           null::double precision        as salary_val2,
           null::smallint                as id_currency,
           null::smallint                as id_salary_rate,
           null::smallint                as TOPNo,
           'jobs_by_date'                as metric,
           'total'                       as metric_type,
           j1.jobs_cnt		      as value
    FROM j_by_date j1
    union all
    Select
           top.year_month,
           top.id_region,
           null::text             as title,
           null::double precision as salary_val1,
           null::double precision as salary_val2,
           null::smallint         as id_currency,
           null::smallint         as id_salary_rate,
           top.TOPNo,
           'jobs_by_region_top'   as metric,
           'total'                as metric_type,
           top.jobs_cnt           as value
    FROM j_by_region_top top
    union all
    Select
           j2.year_month,
           null::integer           as id_region,
           j2.title,
           j2.salary_val1,
           j2.salary_val2,
           j2.id_currency,
           j2.id_salary_rate,
           j2.TOPNo,
           'jobs_top10'            as metric,
           'total'                 as metric_type,
           j2.jobs_cnt             as value
    FROM j_TOP10 j2
    union all
	Select
           j3.year_month,
           null::integer                 as id_region,
           null::text                    as title,
           null::double precision        as salary_val1,
           null::double precision        as salary_val2,
           null::smallint                as id_currency,
           null::smallint                as id_salary_rate,
           null::smallint                as TOPNo,
           'jobs_by_date'                as metric,
           'student'                     as metric_type,
           j3.jobs_cnt		      as value
    FROM j_by_date_student j3
    union all
    Select
           top.year_month,
           top.id_region,
           null::text             as title,
           null::double precision as salary_val1,
           null::double precision as salary_val2,
           null::smallint         as id_currency,
           null::smallint         as id_salary_rate,
           top.TOPNo,
           'jobs_by_region_top'   as metric,
           'student'              as metric_type,
           top.jobs_cnt           as value
    FROM j_by_region_top_student top
    union all
    Select
           j2.year_month,
           null::integer           as id_region,
           j2.title,
           j2.salary_val1,
           j2.salary_val2,
           j2.id_currency,
           j2.id_salary_rate,
           j2.TOPNo,
           'jobs_top10'            as metric,
           'student'               as metric_type,
           j2.jobs_cnt             as value
    FROM j_TOP10_student j2
;

Select lower(substring(current_database(),13,2))::text      as country,
       u.year_month,
       u.id_region,
       u.title,
       u.salary_val1,
       u.salary_val2,
       u.id_currency,
       u.id_salary_rate,
       u.TOPNo,
       u.metric,
       u.metric_type,
       u.value
FROM final_union u;
