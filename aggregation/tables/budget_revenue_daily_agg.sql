    with date_range as
             (
                 Select make_date( cast(EXTRACT( year from  current_date - 1) as int), cast(EXTRACT(month from current_date - 1) as int), 1)   as start_date,
                        make_date( cast(EXTRACT( year from  current_date - 1) as int), cast(EXTRACT(month from current_date - 1) as int), cast(EXTRACT(day from current_date - 1) as int))as end_date
             ) ,

        click_data as (
             select acs.country_id as country,
                    acs.id_project,
                    acs.site,
                    acs.id_user,
                    acs.id_campaign,
                    max(acs.campaign)                                              as campaign,
                    sum(session_count)                                             as session_cnt,
                    COALESCE(sum(total_value) / NULLIF(sum(click_count),0),0)                          as cpc_usd,
                    sum(click_count)                                               as click_cnt,
                    sum(organic_count)                                             as organic_click_cnt,
                    sum(click_count) - sum(organic_count)                          as paid_click_cnt,
                    sum(total_value)                                               as revenue_usd,
                    sum(  total_value +
                    total_value / extract(day from current_date - 1) *
                    (make_date(
            cast(extract(year from current_date - 1 + INTERVAL '1 month' ) as int) ,cast( extract(month from current_date - 1 + INTERVAL '1 month' ) as int), 1)
    - cast( current_date - 1  as date) -1))    as potential_revenue_usd,
                    COALESCE(sum(total_value/NULLIF(click_count,0) * organic_count),0)                     as organic_revenue_usd,
                    COALESCE(sum(total_value/NULLIF(click_count,0) * (click_count - organic_count)),0)       as paid_revenue_usd,
                    sum(job_count)                                                 as job_cnt,
                    sum(paid_overflow_count)                                       as paid_overflow_cnt,
                    sum(test_count)                                                as test_click_cnt,
                    sum(test_revenue_usd)                        as test_click_revenue_usd,
                    COALESCE(sum(total_value/NULLIF(click_count*duplicated_count,0)),0)                  as dublicated_click_revenue_usd,
                    sum(is_paid_affiliate)                                         as is_paid_affiliate,
                    sum(affiliate_revenue)                                         as affiliate_revenue,
                    sum(free_clicks)                                               as free_clicks,
                    sum(paid_clicks)                                               as paid_clicks
             from aggregation.auction_click_statistic_analytics acs
             where cast(date  as date) BETWEEN (select start_date from date_range) and (select end_date from date_range)
             group by acs.country_id,
                      acs.id_project,
                      acs.site,
                      acs.id_user,
                      acs.id_campaign
         ),

        user_budget_log as (
                    SELECT * FROM (
                                    SELECT t1.date, t1.country_id, t1.id, t1.id_user, t1.budget, t1.daily_budget, t1.flags, au.currency,
                                           row_number() OVER (PARTITION BY country_id, id_user ORDER BY date desc) as row_number
                                    FROM auction.user_budget_log t1
                                    JOIN  imp.auction_user au
                                            on  au.country = t1.country_id
                                            and   au.id = t1.id_user
                                    where  DATE(date)  <=  (SELECT end_date FROM date_range)
                    ) t2
                         where row_number = 1),

        auction_campaign_log as (
                    select * from (
                            SELECT t1.*, ac.id_project, ac.is_price_per_job, ac.currency,
                                    row_number() OVER (PARTITION BY  t1.id_campaign, t1.country_id ORDER BY date desc) as row_number
                            FROM auction.campaign_log t1
                            LEFT JOIN imp.auction_campaign ac
                                                     on  ac.country_id = t1.country_id
                                                     and    ac.id = t1.id_campaign
                            where  DATE(date)  <=  (SELECT end_date FROM date_range)
                    ) t2
                        where row_number = 1),

        info_currency_history as (
                        SELECT * FROM (
                            SELECT ich.*,
                                   row_number() over (partition by ich.country, ich.id_currency order by date desc) as row_number
                            FROM dimension.info_currency_history ich
                            WHERE date(ich.date) <= (SELECT end_date FROM date_range)
                    ) t2
                        where row_number =1
                        ),

         budgets as (
             select distinct cd.country,
                    cd.id_user,
                    cd.id_project,
                    cd.id_campaign,
                    coalesce(ubl.budget * ic.value_to_usd, 0)  as user_budget_month_usd,
                    coalesce(ac.budget * ic1.value_to_usd, 0) as campaign_budget_month_usd,
                    coalesce(ubl.budget, 0)  as user_budget_month,
                    coalesce(ac.budget, 0) as campaign_budget_month,
                    case when (ac.budget = 0 and ac.click_price >0)
                    or (ac.budget = 0 and ac.campaign_status = 0 and ac.is_price_per_job = TRUE)  then 1 else 0 end as is_unlim_cmp,
                    case when ac.daily_budget > 0 and ac.flags & 256 = 256 and ac.daily_budget is not null then 1 else 0 end is_cmp_with_daily_budget,
                    case when ubl.daily_budget > 0 and ubl.flags & 16384 = 16384 and ubl.daily_budget is not null then 1 else 0 end is_user_with_daily_budget
             from click_data cd
                      left join user_budget_log ubl
                           on  ubl.country_id = cd.country
                            and   ubl.id_user = cd.id_user
                      left join info_currency_history ic
                         on  ubl.country_id = ic.country
                          and   ubl.currency = ic.id_currency
                      left join auction_campaign_log ac
                           on  ac.country_id = cd.country
                           and    ac.id_campaign = cd.id_campaign
                      left join info_currency_history ic1
                           on  ac.country_id = ic1.country
                           and    ac.currency = ic1.id_currency
         ),
        coefficient as
         (
                        SELECT campaign.country_id,
                               campaign.id_project,
                               avg(campaign.conversion_rate) AS external_coefficient
                        FROM auction_campaign_log campaign
                        WHERE campaign.conversion_rate IS NOT NULL
                          AND campaign.campaign_status = 0
                        GROUP BY  campaign.id_project,campaign.country_id
         )

    select cd.country as country_id,
           current_date - 1   as action_date,
           cd.id_user,
           countries.alpha_2 as country,
           cd.id_project,
           cd.site,
           max(b.is_unlim_cmp) as is_unlim_cmp,
           sum(session_cnt)                            as session_cnt,
           COALESCE(sum(cd.revenue_usd) / NULLIF(sum(click_cnt),0),0)        as cpc_usd,
           sum(click_cnt)                              as click_cnt,
           sum(organic_click_cnt)                      as organic_click_cnt,
           sum(paid_click_cnt)                         as paid_click_cnt,
           sum(revenue_usd)                            as revenue_usd,
           sum(organic_revenue_usd)                    as organic_revenue_usd,
           sum(paid_revenue_usd)                       as paid_revenue_usd,
           sum(potential_revenue_usd)                  as potential_revenue_usd,
           sum(job_cnt)                                as job_cnt,
           sum(paid_overflow_cnt)                      as paid_overflow_cnt,
           max(user_budget_month_usd)                  as user_budget_month_usd,
           sum(campaign_budget_month_usd)              as campaign_budget_month_usd,
           max(user_budget_month)                      as user_budget_month,
           sum(campaign_budget_month)                  as campaign_budget_month,
           case
               when
                   max(user_budget_month_usd) > 0
               then max(user_budget_month_usd) - sum(revenue_usd)
               when
                   max(user_budget_month_usd) = 0 and sum(campaign_budget_month_usd) > 0
               then sum(campaign_budget_month_usd) - sum(revenue_usd)
               else 0 end                                as revenue_budget_diff,
           case
               when
                   max(user_budget_month_usd) > 0
               then max(user_budget_month_usd) - sum(potential_revenue_usd)
               when
                   max(user_budget_month_usd) = 0 and sum(campaign_budget_month_usd) > 0
               then sum(campaign_budget_month_usd) - sum(potential_revenue_usd)
               else 0 end                                as potential_revenue_budget_diff,
           sum(test_click_cnt)                         as test_click_cnt,
           sum(test_click_revenue_usd)                 as test_click_revenue_usd,
           c.external_coefficient,
           sum(dublicated_click_revenue_usd)          as dublicated_click_revenue_usd,
           cd.id_campaign,
           cd.campaign,
           max(b.is_cmp_with_daily_budget) as is_cmp_with_daily_budget,
           max(b.is_user_with_daily_budget) as is_user_with_daily_budget,
           sum(is_paid_affiliate) as is_paid_affiliate,
           sum(affiliate_revenue) as affiliate_revenue,
           sum(free_clicks) as free_clicks,
           sum(paid_clicks) as paid_cicks
           from click_data cd
              left join budgets b
                        on b.country = cd.country and
                            b.id_project = cd.id_project and b.id_campaign = cd.id_campaign
                            and b.id_user = cd.id_user
              left join coefficient c
                        on  cd.country = c.country_id and
                            cd.id_project = c.id_project
               left join dimension.countries
                        on cd.country = countries.id

    group by
             cd.id_user,
             cd.country,
             cd.id_project,
             cd.site,
             c.external_coefficient,
             countries.alpha_2,
             cd.id_campaign,
             cd.campaign;
