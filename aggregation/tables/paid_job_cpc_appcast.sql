declare @country_id = 6;
declare @date = getdate();

Select @country_id			as id_country,
       cast(@date as date)	as date,
       campaign.id_project,
	   a.conversion_rate,
	   case when campaign.click_price = 0 and campaign.is_price_per_job = 1 
			then coalesce(job_region_billing_info.click_price, 0)* info_currency.value_to_usd 
			else campaign.click_price*info_currency.value_to_usd end														as cpа_for_paid_job_count_usd,
       sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end)    as paid_job_count
   
from dbo.job_region with (nolock)
         join auction.campaign with (nolock)
               on job_region.id_campaign = campaign.id
         join dbo.info_currency with (nolock)
               on info_currency.id = campaign.currency
         left join dbo.job_region_billing_info with (nolock)
               on job_region.uid = job_region_billing_info.uid_job
		 cross join (
					select top 1 conversion_rate -- ext stat coef
					from auction.campaign_log acl
					where acl.id_campaign = 52124
					order by date desc) a
where  (campaign.date_end is null or cast(campaign.date_end as date) > @date)
		and campaign.campaign_status = 0
		and campaign.id_project = 16902
		and campaign.id = 52124
group by campaign.id_project,
		 a.conversion_rate,
		 case when campaign.click_price = 0 and campaign.is_price_per_job = 1 then coalesce(job_region_billing_info.click_price, 0)* info_currency.value_to_usd else campaign.click_price* info_currency.value_to_usd end
       ;

