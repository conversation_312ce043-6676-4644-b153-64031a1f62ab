----Flag Table
create temporary table flag_table1 as (
select distinct
    cvba.flags,
    case
        when cvba.flags = 0 then ''
        when cvba.flags = 1 then 'CvSubmitted'
           when cvba.flags = 2 then 'CvCreated'
           when cvba.flags = 4 then 'ImageAttached'
           when cvba.flags = 64 then 'EmailInvalidFormatError'
           when cvba.flags = 128 then 'TelephoneInvalidFormatError'
           when cvba.flags = 256 then 'TelephoneIsEmptyError'
           when cvba.flags = 512 then 'FullNameIsEmptyError'
           when cvba.flags = 1024 then 'ImageInvalidFormatError'
           when cvba.flags = 2048 then 'ImageMaxSizeError'
           else 'Other flags' end as            flag
from public.cv_build_action cvba);

----Action Table
create temporary table action_table1 as (
select distinct
    cvba.type,
    case
           when cvba.type = 0 then 'SubmitCv'
           when cvba.type = 1 then 'SaveCv'
        when cvba.type = 2 then 'Abandon'
        when cvba.type = 3 then 'AttachImage'
        when cvba.type = 4 then 'NextButton'
        when cvba.type = 5 then 'PreviousStep'
        when cvba.type = 9 then 'CvDownloaded'
        when cvba.type = 10 then 'PageView'
        when cvba.type = 12 then 'PageView'
           else null end as            action
from public.cv_build_action cvba);
create temporary table source_table as (select distinct acv.source,
case
        when acv.source = 0 then 'Unknown'
        when acv.source = 1 then 'ApplyForm'
        when acv.source = 2 then 'ManageCvPage'
        when acv.source = 3 then 'MenuLink'
        when acv.source = 4 then 'OnBoarding'
        when acv.source = 5 then 'MenuHeader'
        when acv.source = 6 then 'MyAccount'
        when acv.source = 7 then 'MyResume'
        when acv.source = 8 then 'MyResumePreCreate'
        when acv.source = 9 then 'ApplyButton'
        when acv.source = 10 then 'Walrus'
        when acv.source = 11 then 'SimplifiedCv'
        when acv.source = 12 then 'CVReviewBanner'
           else null end as source_name
    from link_dbo.account_cv acv);


----
create temporary table flag_table as (
select distinct acv.flags,
case
        when acv.flags = 0 then 'none'
        when acv.flags = 1 then 'Mobile'
        when acv.flags = 3 then 'Mobile+CvBuilder'
        when acv.flags = 4 then 'NewCvBuilder'
        when acv.flags = 5 then 'Mobile+NewCvBuilder'
        else 'Other' end as flag_name
    from link_dbo.account_cv acv);


---- Main Table --- дозаписувати і накоплювати дані
select
       'action' type,
       cvba.date :: date,
       case
           when cvba.type = 0 then 'SubmitCv'
           when cvba.type = 1 then 'SaveCv'
        when cvba.type = 2 then 'Abandon'
        when cvba.type = 3 then 'AttachImage'
        when cvba.type = 4 then 'NextButton'
        when cvba.type = 5 then 'PreviousStep'
        when cvba.type = 9 then 'CvDownloaded'
        when cvba.type = 10 then 'PageView'
        when cvba.type = 12 then 'PageView'
           else null end as            action,
       case
        when cvba.flags = 0 then ''
        when cvba.flags = 1 then 'CvSubmitted'
           when cvba.flags = 2 then 'CvCreated'
           when cvba.flags = 4 then 'ImageAttached'
           when cvba.flags = 64 then 'EmailInvalidFormatError'
           when cvba.flags = 128 then 'TelephoneInvalidFormatError'
           when cvba.flags = 256 then 'TelephoneIsEmptyError'
           when cvba.flags = 512 then 'FullNameIsEmptyError'
           when cvba.flags = 1024 then 'ImageInvalidFormatError'
           when cvba.flags = 2048 then 'ImageMaxSizeError'
           else 'Other flags' end as            flag,
       count(cvba.id)                  cnt_action,
       count(distinct cvba.id_account) cnt_uniqe_account
from public.cv_build_action cvba
group by cvba.date :: date
--- умова по одному дню
--- UNION -- оновлювати
union all
select
  --  null date_diff,
    'account'
       acv.date_created :: date,
       acv.source,
       st.source_name,
       acv.flags,
       ft.flag_name,
       deleted,
       count(acv.id)                  cnt_action,
       count(distinct acv.id_account) cnt_uniqe_account
from link_dbo.account_cv acv
left join flag_table ft on acv.flags = ft.flags
left join source_table st on st.source = acv.source


group by acv.date_created :: date, acv.source,st.source_name, acv.flags,ft.flag_name
