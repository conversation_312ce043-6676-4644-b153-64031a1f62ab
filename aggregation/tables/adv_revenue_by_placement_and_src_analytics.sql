  declare @date_diff int = ${dt_begin},
          @country_id int = ${country_id},
          @from_email_click_flag int = 256,
          @from_external_click_flag int = 512,
          @flags_duplicate_click int = 4096,
          @flags_duplicate_away int = 512,
          @month_date_diff int = ${month_date_diff};


  select *
  into #temp_session_away
  from dbo.session_away
  where date_diff = @date_diff;

  select *
  into #temp_session_search
  from dbo.session_search
  where date_diff = @date_diff

  select *
  into #temp_session
  from dbo.session
  where date_diff = @date_diff;

  select *
  into #temp_session_click
  from dbo.session_click
  where date_diff = @date_diff;

  select *
  into #temp_session_click_no_serp
  from dbo.session_click_no_serp
  where date_diff = @date_diff;


    select *
  into #temp_session_jdp
  from dbo.session_jdp
  where date_diff = @date_diff;

    select *
  into #temp_session_external
  from dbo.session_external
  where date_diff = @date_diff;



  select @country_id as country_id,
    s2.date_diff as date_diff,
    s2.id_traf_source,
    s2.id_current_traf_source,
    s2.placement,
    sum(s2.click_price_usd) revenue_usd,
    sum(s2.revenue_usd_discount) revenue_usd_discount,
    s2.id_job_category
  from
  (
    select s1.date_diff,
      s1.id_traf_source,
      s1.id_current_traf_source,
      s1.click_price_usd,
      s1.id_session,
      case
        when id_project in (17055,17045)
          then 'price per post'
        when add_placement = 1
          then 'salary page'
        when add_placement = 2
          then 'category page'  
        when add_placement = 3
          then 'company page'  
        when add_placement = 4
          then 'skill page' 
        when add_placement = 5
          then 'job description page'    
        when s1.flags & 64 = 64 or s1.flags & 128 = 128
          then 'mobile app'
        when info_project.hide_in_search = 1
          then 'ad exchange'
        when coalesce(s1.letter_type, email_sent.letter_type) is not null
          then concat('letter type ',coalesce(s1.letter_type, email_sent.letter_type))
        when s1.id_recommend is not null
          then 'recommendations'
        when s1.id_alertview is not null
          then 'other letter types'
        when s1.id_search is not null
          then 'search'
        when s1.id_external is not null
          then 'external'
        else 'other'
      end placement,
      s1.click_price_usd * isnull((select top 1 discount from dbo.vw_info_project_discount d (nolock) where d.id_project = s1 .id_project and datediff(m, 0, d.date) <= @month_date_diff order by d.date desc), 0) revenue_usd_discount,
      s1.id_job_category
    from
    (
      select sa.date_diff,
        s.id_traf_source,
        s.id_current_traf_source,
        s.id id_session,
        sa.click_price * ic.value_to_usd click_price_usd,
        isnull(sa.letter_type, sj.letter_type) letter_type,
        isnull(sc.id_recommend, scj.id_recommend) id_recommend,
        isnull(sc.id_alertview, scj.id_alertview) id_alertview,
        isnull(sc.id_search, scj.id_search) id_search,
        ext.id id_external,
        s.flags,
        sa.id_project,
        case when sa.flags & 2048 = 2048  or coalesce(ss.search_source,ssj.search_source) in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
             when sa.flags & 4096 = 4096  or coalesce(ss.search_source,ssj.search_source) in (145) then 2--  category page
             when coalesce(ss.search_source,ssj.search_source) in (146, 147, 148, 149, 150) then 3-- company page
             when coalesce(ss.search_source,ssj.search_source) in (151, 152, 153, 154) then 4-- skill page
             when coalesce(ss.search_source,ssj.search_source) in (155, 156, 157, 158) then 5-- JobDescription page
        end as add_placement,
        coalesce(jh.id_category, j.id_category)                as id_job_category 
      from #temp_session_away sa (nolock)
      inner join #temp_session s (nolock) on sa.date_diff = s.date_diff
                                       and sa.id_session = s.id
      inner join dbo.info_currency ic (nolock) on ic.id = sa.id_currency
      left join auction.campaign ac (nolock) on ac.id = sa.id_campaign
      left join auction.site ast (nolock) on ac.id_site = ast.id
      left join auction.[user] au (nolock) on au.id = ast.id_user
      left join dbo.info_project ip with(nolock) on ip.id = sa.id_project
      -- serp -> away
      left join #temp_session_click sc (nolock) on sc.date_diff = sa.date_diff
                                             and sc.id = sa.id_click
      -- serp -> jdp -> away
      left join #temp_session_jdp sj (nolock) on sj.date_diff = sa.date_diff
                                           and sj.id = sa.id_jdp
      left join #temp_session_click scj (nolock) on scj.date_diff = sj.date_diff
                                              and scj.id = sj.id_click
      left join #temp_session_external ext (nolock) on ext.date_diff = sa.date_diff
                                                 and ext.id_away = sa.id
      left join #temp_session_search ss (nolock) on ss.date_diff = sc.date_diff
                                                 and ss.id = sc.id_search
      left join #temp_session_search ssj (nolock) on ssj.date_diff = scj.date_diff
                                                 and ssj.id = scj.id_search
      left join dbo.job j (nolock) on sa.id_job = j.id
      left join dbo.job_history jh (nolock) on sa.uid_job = jh.uid  
                                           
      where
        sa.date_diff = @date_diff
        and isnull(s.flags, 0) & 1 = 0
        and
        (
          sa.id_campaign = 0
          or au.flags & 2 = 0
        )
        and isnull(sa.flags, 0) & 2 = 0
        and sa.flags & @flags_duplicate_away = 0
        and coalesce(lower(ip.name), '') not like 'j-vers.%'

      union all

      select sc.date_diff,
        s.id_traf_source,
        s.id_current_traf_source,
        s.id id_session,
        sc.click_price * ic.value_to_usd click_price_usd,
        isnull(sa.letter_type, sj.letter_type) letter_type,
        sc.id_recommend,
        sc.id_alertview,
        sc.id_search,
        ext.id id_external,
        s.flags,
        sc.id_project,
        case when sa.flags & 2048 = 2048 or sj.source = 9 or ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143,144) then 1-- salary page
             when sa.flags & 4096 = 4096  or ss.search_source in (145) then 2--  category page
             when ss.search_source in (146, 147, 148, 149, 150) then 3-- company page
             when ss.search_source in (151, 152, 153, 154) then 4-- skill page
             when ss.search_source in (155, 156, 157, 158) then 5-- JobDescription page
        end as add_placement ,
        coalesce(jh.id_category, j.id_category)                as id_job_category
      from #temp_session_click sc (nolock)
      inner join #temp_session s (nolock) on sc.date_diff = s.date_diff
                                       and sc.id_session = s.id
      inner join dbo.info_currency ic (nolock) on ic.id = sc.id_currency
      left join auction.campaign ac (nolock) on ac.id = sc.id_campaign
      left join auction.site ast (nolock) on ac.id_site = ast.id
      left join auction.[user] au (nolock) on au.id = ast.id_user
      left join dbo.info_project ip with(nolock) on ip.id = sc.id_project
      left join #temp_session_away sa (nolock) on sc.date_diff = sa.date_diff
                                            and sc.id = sa.id_click
      left join #temp_session_jdp sj (nolock) on sj.date_diff = sa.date_diff
                                           and sj.id = sa.id_jdp
      left join #temp_session_external ext (nolock) on ext.date_diff = sc.date_diff
                                                 and
                                                 (
                                                   ext.id_away = sa.id
                                                   or ext.id_jdp = sj.id
                                                 )
      left join #temp_session_search ss (nolock) on ss.date_diff = sc.date_diff
                                                 and ss.id = sc.id_search  
      left join dbo.job j (nolock) on sc.id_job = j.id
      left join dbo.job_history jh (nolock) on sc.uid_job = jh.uid  
                                                                                                                             
      where
        sc.date_diff = @date_diff
        and isnull(s.flags, 0) & 1 = 0
        and
        (
         -- sc.id_campaign = 0 or
          au.flags & 2 = 2
        )
        and isnull(sc.flags, 0) & 16 = 0
        and sc.flags & @flags_duplicate_click = 0
        and coalesce(lower(ip.name), '') not like 'j-vers.%'

	  union all

      select scns.date_diff,
        s.id_traf_source,
        s.id_current_traf_source,
        s.id id_session,
        scns.click_price * ic.value_to_usd click_price_usd,
        scns.letter_type,
        scns.id_recommend,
        null id_alertview,
        null id_search,
        se.id id_external,
        s.flags,
        scns.id_project,
        null as add_placement,
        coalesce(jh.id_category, j.id_category) as id_job_category 
      from #temp_session_click_no_serp scns (nolock)
      inner join #temp_session s (nolock) on scns.date_diff = s.date_diff and scns.id_session = s.id
      inner join dbo.info_currency ic (nolock) on ic.id = scns.id_currency
      inner join auction.campaign ac (nolock) on ac.id = scns.id_campaign
      inner join auction.site ast (nolock) on ac.id_site = ast.id
      inner join auction.[user] au (nolock) on au.id = ast.id_user
      left join dbo.info_project ip with(nolock) on ip.id = scns.id_project
      left join #temp_session_jdp sj (nolock) on sj.id_click_no_serp = scns.id and sj.date_diff = scns.date_diff
      left join #temp_session_away sa (nolock) on sa.id_click_no_serp = scns.id and sa.date_diff = scns.date_diff
      left join #temp_session_external se (nolock) on se.id_jdp = sj.id or se.id_away = sa.id
      left join dbo.job j(nolock) on scns.id_job = j.id
      left join dbo.job_history jh (nolock) on scns.uid_job = jh.uid 
      where
         scns.date_diff = @date_diff
        and isnull(s.flags, 0) & 1 = 0
        and au.flags & 2 = 2
        and isnull(scns.flags, 0) & 16 = 0
        and scns.flags & @flags_duplicate_click = 0
        and coalesce(lower(ip.name), '') not like 'j-vers.%'
    ) s1
      left join dbo.info_project with(nolock)
      on s1.id_project  = info_project.id
      left join
        (  select  id_alertview,
               min(id_message)  as id_message
        from dbo.session_alertview_message  with (nolock)
        where date_diff = @date_diff
        group by id_alertview
        ) sam
      on sam.id_alertview = s1.id_alertview
      left join dbo.email_sent with(nolock)
      on email_sent.id_message = sam.id_message
  ) s2
  group by s2.date_diff,
    s2.id_traf_source,
    s2.placement,
    s2.id_current_traf_source,
    s2.id_job_category;



	drop table #temp_session_away;
	drop table #temp_session;
	drop table #temp_session_click;
	drop table #temp_session_click_no_serp;
  drop table #temp_session_jdp;
  drop table #temp_session_external;
