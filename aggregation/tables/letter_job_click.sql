create temporary table temp_account_verified as
    (select a.id                                        as account_id,
            (ac.verify_date::date - '1900-01-01'::date) as verify_datediff
     from link_dbo.account a
     join link_dbo.account_contact ac
          on a.id = ac.id_account
     where ac.verify_date::date - '1900-01-01'::date between @date_diff-7 and @date_diff) 
;

                   create temp table temp_email_visit as
                    select *
                    from link_dbo.email_visit;

                    create index temp_email_visit_pk
                        on temp_email_visit (id_account, letter_type, date_diff);

                    create temp table temp_email_alert as
                    select *
                    from link_dbo.email_alert;

                    create index temp_email_alert_pk
                        on temp_email_alert (id);

create temporary table temp_job_click_from_alertview as
select ea.id_account,
       av.verify_datediff,
       sa.date_diff,
       ea.search,
       ea.salary,
       ea.txt_region,
       ea.id_region,
       ea.radius_km,
       ev.letter_type,
       count(distinct case when sc.job_destination = 1 then sc.id end)     as away_cnt,
       count(distinct case when not sc.job_destination = 1 then sc.id end) as jdp_cnt
from temp_email_visit ev
join temp_account_verified av
     on av.account_id = ev.id_account and
        ev.date_diff between av.verify_datediff and av.verify_datediff + 7
join link_dbo.session_alertview_message sam
     on ev.date_diff = sam.date_diff and
        ev.id_message = sam.id_message
join link_dbo.session_alertview sa
     on sa.date_diff = sam.date_diff and
        sa.id = sam.id_alertview
join temp_email_alert ea
     on sa.sub_id_alert = ea.id
left join link_dbo.session_click sc
          on sc.date_diff = sa.date_diff and
             sc.id_alertview = sa.id
where ev.date_diff = @date_diff
group by ea.id_account,
         av.verify_datediff,
         ea.search,
         ea.salary,
         ea.id_region,
         ea.txt_region,
         ea.radius_km,
         sa.date_diff,
         ev.letter_type;

create temporary table temp_lt8_jdp as
select id_account,
       date_diff,
       count(distinct id) as jdp_cnt
from session_jdp sj
where letter_type = 8
group by id_account,
         date_diff
;
drop table if exists temp_job_click_direct_from_lt8;
create temporary table temp_job_click_direct_from_lt8 as
with lt8_away as
         (select ev.id_account,
                 av.verify_datediff,
                 ev.date_diff,
                 ea.search,
                 ea.salary,
                 ea.txt_region,
                 ea.id_region,
                 ea.radius_km,
                 ev.letter_type,
                 count(distinct sam.id_away) as away_cnt
          from temp_email_visit ev
          join temp_account_verified av
               on av.account_id = ev.id_account and
                  ev.date_diff between av.verify_datediff and av.verify_datediff + 7
          join temp_email_alert ea
               on ev.id_account = ea.id_account and
                  ev.id_alert = ea.id
          left join link_dbo.session_away_message sam
                    on sam.date_diff = ev.date_diff and
                       sam.id_message = ev.id_message
          where ev.letter_type = 8
            and ev.date_diff = @date_diff
          group by ev.id_account,
                   av.verify_datediff,
                   ea.search,
                   ea.salary,
                   ea.txt_region,
                   ea.id_region,
                   ea.radius_km,
                   ev.date_diff,
                   ev.letter_type)
select lt8_away.*,
       coalesce(temp_lt8_jdp.jdp_cnt, 0) as jdp_cnt
from lt8_away
left join temp_lt8_jdp
          on lt8_away.date_diff = temp_lt8_jdp.date_diff and
             lt8_away.id_account = temp_lt8_jdp.id_account
;



select @country_id         as country_id,
       id_account           as account_id,
       verify_datediff,
       date_diff            as job_click_datediff,
       search               as keyword,
       salary,
       txt_region           as region_name,
       id_region            as region_id,
       radius_km,
       letter_type,
       1 /*from alertview*/ as click_source_id,
       away_cnt,
       jdp_cnt
from temp_job_click_from_alertview

union all
select @country_id         as country_id,
       id_account        as account_id,
       verify_datediff,
       date_diff         as job_click_datediff,
       search            as keyword,
       salary,
       txt_region        as region_name,
       id_region         as region_id,
       radius_km,
       letter_type,
       2 /*from letter*/ as click_source_id,
       away_cnt,
       jdp_cnt
from temp_job_click_direct_from_lt8
;
