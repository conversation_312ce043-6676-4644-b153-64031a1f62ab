declare  @dt_begin int = ${dt_begin},
		 @dt_end int = ${dt_end},
		 @country_id int = ${country_id};

SELECT @country_id										   as country_id,
       dateadd(day, SJ.date_diff, '1900-01-01')            as session_date,
       SJ.job_id_project                                   as id_project,
       info_project.name                                   as project_name,
       isnull(campaign.name, campaign_2.name)              as campaign_name,
       isnull(sc.id_campaign, scns.id_campaign)			   as id_campaign,
	   session.id_current_traf_source,
	   session.session_create_page_type,
       u_traffic_source.name                               as traffic_name,
       u_traffic_source.is_paid                            as traffic_is_paid,
       u_traffic_source.channel,
	   isnull(SC.uid_job, scns.uid_job)					   as uid_job,
	   isnull(jr.id_job, jr2.id_job) 					   as id_job,
	   isnull(j.title, j2.title)						   as job_title,
	   isnull(jr.id_region, jr2.id_region)				   as id_region,
	   isnull(ir.name, ir2.name)						   as region_name,
	   isnull(ir.is_city, ir2.is_city)     				   as is_city,
       session.ip_cc,
	   case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128  then 2
           else 0 end                                      as is_mobile,
	   case when session.flags & 2 = 2 then 1
			else 0 end					                   as is_returned,
       isnull(info_currency.name, ic.name)				   as currency_name,
       isnull(sc.click_price, scns.click_price) * isnull(info_currency.value_to_usd,ic.value_to_usd)        as cpc_usd,
       isnull(sc.click_price, scns.click_price)																as cpc_origin,
       isnull(conversion_apply.conversion_rate,0)															as conversion_to_apply,
	   count(distinct SJ.id)                               as jdp_cnt,
       count(distinct SA.id)                               as apply_cnt

from dbo.session_jdp SJ with (nolock)
         left join dbo.session_jdp_action SJA with (nolock)
                   on SJ.date_diff = SJA.date_diff
                       and SJ.id = SJA.id_jdp
         left join dbo.session_apply SA with (nolock)
                   on SA.date_diff = SJA.date_diff
                       and SA.id_src_jdp_action = SJA.id
         join dbo.session session with (nolock)
				   on SJ.date_diff = session.date_diff
					   and SJ.id_session = session.id
         left join dbo.session_click SC with (nolock)
                   on SJ.date_diff = SC.date_diff
                       and SJ.id_click = SC.id
		 left join dbo.session_click_no_serp scns (nolock)
				   on sj.date_diff = scns.date_diff
					   and sj.id_click_no_serp = scns.id
         left join dbo.u_traffic_source with (nolock)
                   on session.id_current_traf_source = u_traffic_source.id
         left join dbo.info_project with (nolock)
                   on SJ.job_id_project = info_project.id
         left join auction.campaign with (nolock)
                   on SC.id_campaign = campaign.id
		 left join auction.campaign campaign_2 with (nolock)
                   on scns.id_campaign = campaign_2.id
         left join dbo.info_currency with (nolock)
                   on SC.id_currency = info_currency.id
		 left join dbo.info_currency ic with (nolock)
                   on scns.id_currency = ic.id
		 left join dbo.job_region jr with (nolock)
				   on jr.uid = SC.uid_job
		 left join dbo.info_region ir with (nolock)
				   on jr.id_region = ir.id
		 left join dbo.job j with (nolock)
				   on jr.id_job = j.id
		 left join dbo.job_region jr2 with (nolock)
				   on jr2.uid = scns.uid_job
		 left join dbo.info_region ir2 with (nolock)
				   on jr2.id_region = ir2.id
		 left join dbo.job j2 with (nolock)
				   on jr2.id_job = j2.id
		 cross apply (select top 1 acl.conversion_rate
					from auction.campaign_log acl
					where date <= SJ.date and acl.id_campaign = isnull(sc.id_campaign, scns.id_campaign)
					order by date desc) conversion_apply

where  SJ.date_diff between @dt_begin and @dt_end
		and session.flags & 1 != 1				/* remove bot sessions */
		  and sj.flags & 4 = 4					/* easy apply form is available */
			and SJ.job_id_project = 16902		/* appcast only */
				and (u_traffic_source.channel = 'Paid Search' or u_traffic_source.channel = 'Paid Innovation Search')


group by dateadd(day, SJ.date_diff, '1900-01-01'),
         SJ.job_id_project,
         info_project.name,
         isnull(campaign.name, campaign_2.name),
         isnull(sc.id_campaign,scns.id_campaign),
		 session.id_current_traf_source,
		 session.session_create_page_type,
         u_traffic_source.name,
         u_traffic_source.is_paid,
         u_traffic_source.channel,
	     isnull(SC.uid_job, scns.uid_job),
	     isnull(jr.id_job, jr2.id_job),
	     isnull(j.title, j2.title),
	     isnull(jr.id_region, jr2.id_region),
	     isnull(ir.name, ir2.name),
	     isnull(ir.is_city, ir2.is_city),
		 session.ip_cc,
         case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128  then 2
           else 0 end,
		 case when session.flags & 2 = 2 then 1 else 0 end,
		 isnull(info_currency.name, ic.name),
		 isnull(sc.click_price, scns.click_price) * isnull(info_currency.value_to_usd,ic.value_to_usd)  ,
         isnull(sc.click_price, scns.click_price),
		 isnull(conversion_apply.conversion_rate,0)
		 ;
