declare @current_datediff int = datediff(day, 0, dbo.ex_getdate() - 1),
        @country_id int = (select id from openquery([dwh], 'select * from dimension.countries') where alpha_2 = substring(db_name(), 5, 2)),
        @country_code varchar(2) = lower(substring(db_name(), 5, 2));


select id_project,
       min(cac.date_diff) as conversion_start_datediff
into #conversion_away_start
from auction.conversion_away_connection cac with (nolock)
         join dbo.session_away sa with (nolock)
              on cac.date_diff = sa.date_diff
                  and cac.id_session_away = sa.id
group by id_project


select distinct
       @country_id              as country_id,
       sa.date_diff             as job_view_datediff,
       s.id_current_traf_source,
       case
            when s.ip_cc = @country_code or s.ip_cc = 'gb' and @country_code = 'uk'
            then 1
            else 0
            end                 as is_local,
       sa.uid_job,
       sa.id_project,
       1 /*jdp on client site*/ as view_type,
       case
           when sa.date_diff >= cas.conversion_start_datediff then 2 /*away with conversions*/
           else 1 /*away without conversions*/
           end                  as job_type,
       sa.id_session,
       null                     as id_jdp_with_apply_click,
       null                     as id_apply,
       null                     as id_apply_with_questionnaire,
       null                     as id_new_cv,
       null                     as id_jdp_with_questionnaire
into #view_job
from session_away sa with (nolock)
join session s with (NOLOCK)
  on s.date_diff = sa.date_diff and
     s.id = sa.id_session
left join #conversion_away_start cas with (nolock)
       on cas.id_project = sa.id_project
where s.flags&1 = 0 /*not bots*/
  and sa.id_jdp is null
  and sa.date_diff = @current_datediff

union all


select distinct
       @country_id                                   as country_id,
       sj.date_diff                                  as job_view_datediff,
       s.id_current_traf_source,
       case
            when s.ip_cc = @country_code or s.ip_cc = 'gb' and @country_code = 'uk'
            then 1
            else 0
            end                                      as is_local,
       sj.uid_job,
       sj.job_id_project                             as id_project,
       2 /*jdp on Jooble*/                           as view_type,
       case
           when sj.flags & 4 = 4 and sj.flags & 524288 = 524288 then 12/*new apply*/
           when sj.flags & 4 = 4 and sj.flags & 524288 = 0 then 11/*easy apply default*/
           when sj.date_diff >= cas.conversion_start_datediff then 2 /*away with conversions*/
           else 1 /*away without conversions*/
           end                                       as job_type,
       sj.id_session                                 as id_session,
       sja.id_jdp                                    as id_jdp_with_apply_click,
       sa.id                                         as id_apply,
       case when sa.flags & 128 = 128 then sa.id end as id_apply_with_questionnaire,
       ac.id                                         as id_new_cv,
       sqa.id_jdp                                    as id_jdp_with_questionnaire
from session_jdp sj with (nolock)
join session s with (NOLOCK)
     on s.date_diff = sj.date_diff and
        s.id = sj.id_session
left join #conversion_away_start cas with (nolock)
          on cas.id_project = sj.job_id_project
left join session_jdp_action sja with (nolock)
          on sja.id_jdp = sj.id and
             sja.flags & 2 = 2 /*easy apply*/ and
             sja.type = 1 /*apply click*/
left join session_apply sa with (nolock)
          on sa.id_src_jdp_action = sja.id
left join account_cv ac with (nolock)
          on sa.id_cv = ac.id and
             sa.date_diff = datediff(day, 0, ac.date_created)
left join session_questionnaire_action sqa with (nolock)
          on sqa.date_diff = sj.date_diff and
             sqa.id_jdp = sj.id
where s.flags & 1 = 0 /*not bots*/
  and sj.date_diff = @current_datediff
;

select country_id,
       job_view_datediff,
       uid_job,
       id_project,
       view_type,
       job_type,
       id_current_traf_source,
       is_local,
       count(distinct id_session) as job_view_cnt,
       count(distinct id_jdp_with_apply_click)     as apply_click_cnt,
       count(distinct id_jdp_with_questionnaire)   as questionnaire_start_cnt,
       count(distinct id_apply)                    as apply_cnt,
       count(distinct id_apply_with_questionnaire) as apply_with_questionnaire_cnt
into #view_job_agg
from #view_job
group by country_id,
         job_view_datediff,
         uid_job,
         id_project,
         view_type,
         job_type,
         id_current_traf_source,
         is_local

select country_id,
       job_view_datediff,
       id_project,
       view_type,
       job_type,
       id_current_traf_source,
       is_local,
       sum(job_view_cnt)                 as job_view_cnt,
       sum(apply_cnt)                    as apply_cnt,
       sum(apply_with_questionnaire_cnt) as apply_with_questionnaire_cnt,
       sum(apply_click_cnt)              as apply_click_cnt,
       sum(questionnaire_start_cnt)      as questionnaire_start_cnt
into #view_and_apply
from #view_job_agg
group by country_id,
         job_view_datediff,
         id_project,
         view_type,
         job_type,
         id_current_traf_source,
         is_local;


select country_id,
       job_view_datediff,
       id_project,
       view_type,
       job_type,
       id_current_traf_source,
       is_local,
       count(distinct id_new_cv) as new_cv_cnt
into #cv
from #view_job
group by country_id,
         job_view_datediff,
         id_project,
         view_type,
         job_type,
         id_current_traf_source,
         is_local;

select vaa.country_id,
       vaa.job_view_datediff,
       vaa.id_project,
       vaa.view_type,
       vaa.job_type,
       vaa.id_current_traf_source,
       vaa.is_local,
       vaa.job_view_cnt,
       vaa.apply_cnt,
       vaa.apply_with_questionnaire_cnt,
       vaa.apply_click_cnt,
       vaa.questionnaire_start_cnt,
       c.new_cv_cnt
from #view_and_apply vaa
left join #cv c
          on vaa.country_id = c.country_id and
             vaa.job_view_datediff = c.job_view_datediff and
             vaa.id_project = c.id_project and
             vaa.job_type = c.job_type and
             vaa.view_type = c.view_type and
             vaa.id_current_traf_source = c.id_current_traf_source and
             vaa.is_local = c.is_local
;
