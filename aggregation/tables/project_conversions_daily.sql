declare @start_datediff int = :to_sqlcode_date_int,
        @dt_end int = :to_sqlcode_date_int;


SELECT 
       'aways'                                            as metric,
       dateadd(day, session_away.date_diff, '1900-01-01') as session_date,
       session_away.id_project                              as project_id,
       info_project.name                                  as project_name,
       campaign.name                                      as campaign_name,
       case
           when session_away.letter_type is not null then concat('Letter Type ', letter_type)
           when session_away.id_click is not null then 'Click'
           when session_away.id_jdp is not null then 'Jdp'
           when session_away.id_click_no_serp is not null then 'No serp'
           else 'Other' end                               as away_type,
       id_campaign as campaign_id,
       case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
           else 0 end                                     as is_mobile,
       u_traffic_source.name                              as traffic_name,
       u_traffic_source.is_paid                           as traffic_is_paid,
       u_traffic_source.channel,
       session.ip_cc,
       info_currency.name,
       0                                                  as away_revenue,
       0                                                  as away_revenue_origin_currency,
       count(session_away.id)                             as aways,
       count(conversion_away_connection.id_session_away)  as conversions,
       session.id_current_traf_source,
       session.session_create_page_type,
       case when session.flags & 2 = 2 then 1 else 0 end  as is_returned,
       coalesce(jh.id_category, j.id_category)            as job_category_id,
       sum(conversion_away_connection.all_conversion)     as all_conversion
from dbo.session_away session_away with (nolock)
         left join dbo.info_project with (nolock)
                   on session_away.id_project = info_project.id
         left join dbo.session session with (nolock)
                   on session_away.id_session = session.id
                       and session_away.date_diff = session.date_diff
         left join dbo.u_traffic_source with (nolock)
                   on session.id_traf_source = u_traffic_source.id
         left join
     (select  id_session_away, 
              count(id_conversion) as all_conversion
      from auction.conversion_away_connection with (nolock)
      group by id_session_away
      ) conversion_away_connection
     on conversion_away_connection.id_session_away = session_away.id
         left join auction.campaign with (nolock)
                   on session_away.id_campaign = campaign.id
         left join dbo.info_currency with (nolock)
                   on session_away.id_currency = info_currency.id
         left join dbo.job j (nolock)
                   on session_away.id_job = j.id
         left join dbo.job_history jh (nolock)
                   on session_away.uid_job = jh.uid

where session_away.date_diff between @start_datediff and @dt_end
  and coalesce(lower(info_project.name), '') not like 'j-vers.%'
  and session.flags & 1 != 1
group by dateadd(day, session_away.date_diff, '1900-01-01'),
         info_project.name,
         campaign.name,
         case
             when session_away.letter_type is not null then concat('Letter Type ', letter_type)
             when session_away.id_click is not null then 'Click'
             when session_away.id_jdp is not null then 'Jdp'
             when session_away.id_click_no_serp is not null then 'No serp'
             else 'Other' end,
         session_away.id_project,
         u_traffic_source.name,
         u_traffic_source.is_paid,
         case
             when session.flags & 16 = 16 then 1
             when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
             else 0 end,
         u_traffic_source.channel,
         session.ip_cc,
         info_currency.name,
         id_campaign,
         session.id_current_traf_source,
         session.session_create_page_type,
         case when session.flags & 2 = 2 then 1 else 0 end,
         coalesce(jh.id_category, j.id_category)

UNION ALL

select 
       case when is_apply = 0 then 'aways' else 'applies' end as metric,
       dateadd(day, date_diff, '1900-01-01')                  as session_date,
       id_project as project_id,
       project_name,
       campaign_name,
       away_type,
       id_campaign as campaign_id,
       is_mobile,
       traffic_name,
       traffic_is_paid,
       channel,
       ip_cc,
       name,
       sum(away_revenue)                                      as away_revenue,
       sum(away_revenue_origin_currency)                      as away_revenue_origin_currency,
       null                                                   as aways,
       null                                                   as conversions,
       id_current_traf_source,
       session_create_page_type,
       is_returned,
       id_job_category as job_category_id,
       null                                                   as all_conversion  
from (select sa.date_diff,
             sa.id_project,
             info_project.name                             as project_name,
             campaign.name                                 as campaign_name,
             case

                 when sa.letter_type is not null then concat('Letter Type ', sa.letter_type)
                 when sa.id_click is not null then 'Click'
                 when sa.id_jdp is not null then 'Jdp'
                 when sa.id_click_no_serp is not null then 'No serp'
                 else 'Other' end                          as away_type,
             sa.id_campaign,
             case
                 when s.flags & 16 = 16 then 1
                 when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                 else 0 end                                as is_mobile,
             u_traffic_source.name                         as traffic_name,
             u_traffic_source.is_paid                      as traffic_is_paid,
             u_traffic_source.channel,
             s.ip_cc,
             ic.name,
             coalesce(sa.click_price, 0) * ic.value_to_usd as away_revenue,
             coalesce(sa.click_price, 0)                   as away_revenue_origin_currency,
             s.id_current_traf_source,
             s.session_create_page_type,
             case when s.flags & 2 = 2 then 1 else 0 end   as is_returned,
             0                                             as is_apply,
             coalesce(jh.id_category, j.id_category)       as id_job_category

      from dbo.session_away sa (nolock)
               inner join dbo.session s (nolock) on sa.date_diff = s.date_diff
          and sa.id_session = s.id
               inner join dbo.info_currency ic (nolock) on ic.id = sa.id_currency
               left join auction.campaign ac (nolock) on ac.id = sa.id_campaign
               left join auction.site ast (nolock) on ac.id_site = ast.id
               left join auction.[user] au (nolock) on au.id = ast.id_user
          -- serp -> away
               left join dbo.session_click sc (nolock) on sc.date_diff = sa.date_diff
          and sc.id = sa.id_click
          -- serp -> jdp -> away
               left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff
          and sj.id = sa.id_jdp
               left join dbo.session_click scj (nolock) on scj.date_diff = sj.date_diff
          and scj.id = sj.id_click
               left join dbo.info_project with (nolock)
                         on sa.id_project = info_project.id
               left join auction.campaign with (nolock)
                         on sa.id_campaign = campaign.id
               left join dbo.u_traffic_source with (nolock)
                         on s.id_traf_source = u_traffic_source.id
               left join dbo.job j (nolock)
                         on sa.id_job = j.id
               left join dbo.job_history jh (nolock)
                         on sa.uid_job = jh.uid
      where sa.date_diff between @start_datediff and @dt_end
        and isnull(s.flags, 0) & 1 = 0
        and (
                  sa.id_campaign = 0
              or au.flags & 2 = 0
          )
        and isnull(sa.flags, 0) & 2 = 0
        and sa.flags & 512 = 0
        and coalesce(lower(info_project.name), '') not like 'j-vers.%'


      union all

      select sc.date_diff,
             sc.id_project,
             info_project.name                                  as project_name,
             campaign.name                                      as campaign_name,
             case
                 when coalesce(sa.letter_type, sj.letter_type) is not null
                     then concat('Letter Type ', coalesce(sa.letter_type, sj.letter_type))
                 when sa.id_click is not null then 'Click'
                 when coalesce(sj.id, sa.id_jdp) is not null then 'Jdp'
                 when coalesce(sa.id_click_no_serp, sj.id_click_no_serp) is not null then 'No serp'
                 else 'Other' end                               as away_type,
             sc.id_campaign,
             case
                 when s.flags & 16 = 16 then 1
                 when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                 else 0 end                                     as is_mobile,
             u_traffic_source.name                              as traffic_name,
             u_traffic_source.is_paid                           as traffic_is_paid,
             u_traffic_source.channel,
             s.ip_cc,
             ic.name,
             coalesce(sc.click_price, 0) * ic.value_to_usd      as away_revenue,
             coalesce(sc.click_price, 0)                        as away_revenue_origin_currency,
             s.id_current_traf_source,
             s.session_create_page_type,
             case when s.flags & 2 = 2 then 1 else 0 end        as is_returned,
             case when sc.job_destination = 3 then 1 else 0 end as is_apply,
             coalesce(jh.id_category, j.id_category)            as id_job_category

      from dbo.session_click sc (nolock)
               inner join dbo.session s (nolock) on sc.date_diff = s.date_diff
          and sc.id_session = s.id
               inner join dbo.info_currency ic (nolock) on ic.id = sc.id_currency
               left join auction.campaign ac (nolock) on ac.id = sc.id_campaign
               left join auction.site ast (nolock) on ac.id_site = ast.id
               left join auction.[user] au (nolock) on au.id = ast.id_user
               left join dbo.session_away sa (nolock) on sc.date_diff = sa.date_diff
          and sc.id = sa.id_click
               left join dbo.session_jdp sj (nolock) on sj.date_diff = sa.date_diff
          and sj.id = sa.id_jdp
               left join dbo.info_project with (nolock)
                         on sc.id_project = info_project.id
               left join auction.campaign with (nolock)
                         on sc.id_campaign = campaign.id
               left join dbo.u_traffic_source with (nolock)
                         on s.id_traf_source = u_traffic_source.id
               left join dbo.job j (nolock)
                         on sc.id_job = j.id
               left join dbo.job_history jh (nolock)
                         on sc.uid_job = jh.uid

      where sc.date_diff between @start_datediff and @dt_end
        and isnull(s.flags, 0) & 1 = 0
        and (
                  sc.id_campaign = 0
              or au.flags & 2 = 2
          )
        and isnull(sc.flags, 0) & 16 = 0
        and sc.flags & 4096 = 0
        and coalesce(lower(info_project.name), '') not like 'j-vers.%'


      union all

      select scns.date_diff,
             scns.id_project,
             info_project.name                                    as project_name,
             campaign.name                                        as campaign_name,
             case
                 when scns.letter_type is not null then concat('Letter Type ', scns.letter_type)
                 else 'No serp' end                               as away_type,
             scns.id_campaign,
             case
                 when s.flags & 16 = 16 then 1
                 when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                 else 0 end                                       as is_mobile,
             u_traffic_source.name                                as traffic_name,
             u_traffic_source.is_paid                             as traffic_is_paid,
             u_traffic_source.channel,
             s.ip_cc,
             ic.name,
             coalesce(scns.click_price, 0) * ic.value_to_usd      as away_revenue,
             coalesce(scns.click_price, 0)                        as away_revenue_origin_currency,
             s.id_current_traf_source,
             s.session_create_page_type,
             case when s.flags & 2 = 2 then 1 else 0 end          as is_returned,
             case when scns.job_destination = 3 then 1 else 0 end as is_apply,
             coalesce(jh.id_category, j.id_category)              as id_job_category
      from dbo.session_click_no_serp scns (nolock)
               inner join dbo.session s (nolock) on scns.date_diff = s.date_diff
          and scns.id_session = s.id
               inner join dbo.info_currency ic (nolock) on ic.id = scns.id_currency
               inner join auction.campaign ac (nolock) on ac.id = scns.id_campaign
               inner join auction.site ast (nolock) on ac.id_site = ast.id
               inner join auction.[user] au (nolock) on au.id = ast.id_user
               left join dbo.info_project with (nolock)
                         on scns.id_project = info_project.id
               left join auction.campaign with (nolock)
                         on scns.id_campaign = campaign.id
               left join dbo.u_traffic_source with (nolock)
                         on s.id_traf_source = u_traffic_source.id
               left join dbo.session_jdp sj (nolock) on sj.id_click_no_serp = scns.id and sj.date_diff = scns.date_diff
               left join dbo.session_away sa (nolock) on sa.id_click_no_serp = scns.id and sa.date_diff = scns.date_diff
               left join dbo.job j (nolock) on scns.id_job = j.id
               left join dbo.job_history (nolock) jh on scns.uid_job = jh.uid
      where scns.date_diff between @start_datediff and @dt_end
        and isnull(s.flags, 0) & 1 = 0
        and au.flags & 2 = 2
        and isnull(scns.flags, 0) & 16 = 0
        and scns.flags & 4096 = 0
        and coalesce(lower(info_project.name), '') not like 'j-vers.%') as Revenue
group by dateadd(day, date_diff, '1900-01-01'),
         id_project,
         project_name,
         campaign_name,
         away_type,
         id_campaign,
         is_mobile,
         traffic_name,
         traffic_is_paid,
         channel,
         ip_cc,
         name,
         id_current_traf_source,
         session_create_page_type,
         is_returned,
         is_apply,
         id_job_category

union all

SELECT 
       'applies'                                         as metric,
       dateadd(day, SJ.date_diff, '1900-01-01')          as session_date,
       SJ.job_id_project                                 as project_id,
       info_project.name                                 as project_name,
       campaign.name                                     as campaign_name,
       case
           when SJ.letter_type is not null then concat('Letter Type ', letter_type)
           when SJ.id_click is not null then 'Click'
           --when SJ.id_jdp is not null then 'Jdp'
           when SJ.id_click_no_serp is not null then 'No serp'
           else 'Other' end                              as jdp_type,
       sc.id_campaign as campaign,
       case
           when session.flags & 16 = 16 then 1
           when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
           else 0 end                                    as is_mobile,
       u_traffic_source.name                             as traffic_name,
       u_traffic_source.is_paid                          as traffic_is_paid,
       u_traffic_source.channel,
       session.ip_cc,
       info_currency.name,
       0                                                 as revenue,
       0                                                 as away_revenue_origin_currency,
       count(distinct SJ.id)                             as value,
       count(distinct SA.id)                             as conversions,
       session.id_current_traf_source,
       session.session_create_page_type,
       case when session.flags & 2 = 2 then 1 else 0 end as is_returned,
       coalesce(jh.id_category, j.id_category)           as job_category_id,
       count(distinct SA.id)                             as all_conversions
from dbo.session_jdp SJ with (nolock)
         left join dbo.session_jdp_action SJA with (nolock)
                   on SJ.date_diff = SJA.date_diff
                       and SJ.id = SJA.id_jdp
         left join dbo.session_apply SA with (nolock)
                   on SA.date_diff = SJA.date_diff
                       and SA.id_src_jdp_action = SJA.id
         join dbo.session session with (nolock)
              on SJ.date_diff = session.date_diff
                  and SJ.id_session = session.id
         left join dbo.session_click SC with (nolock)
                   on SJ.date_diff = SC.date_diff
                       and SJ.id_click = SC.id
         left join dbo.u_traffic_source with (nolock)
                   on session.id_traf_source = u_traffic_source.id
         left join dbo.info_project with (nolock)
                   on SJ.job_id_project = info_project.id
         left join auction.campaign with (nolock)
                   on SC.id_campaign = campaign.id
         left join dbo.info_currency with (nolock)
                   on SC.id_currency = info_currency.id
         left join dbo.job_region jr (nolock)
                   on sj.uid_job = jr.uid
         left join dbo.job j (nolock)
                   on jr.id_job = j.id
         left join dbo.job_history jh (nolock)
                   on sj.uid_job = jh.uid

where SJ.date_diff between @start_datediff and @dt_end
  and session.flags & 1 != 1
  and sj.flags & 4 = 4
  and coalesce(lower(info_project.name), '') not like 'j-vers.%'

group by dateadd(day, SJ.date_diff, '1900-01-01'),
         SJ.job_id_project,
         info_project.name,
         campaign.name,
         case
             when SJ.letter_type is not null then concat('Letter Type ', letter_type)
             when SJ.id_click is not null then 'Click'
             when SJ.id_click_no_serp is not null then 'No serp'
             else 'Other' end,
         u_traffic_source.name,
         u_traffic_source.is_paid,
         u_traffic_source.channel,
         session.ip_cc,
         info_currency.name,
         sc.id_campaign,
         case
             when session.flags & 16 = 16 then 1
             when session.flags & 64 = 64 or session.flags & 128 = 128 then 2
             else 0 end,
         session.id_current_traf_source,
         session.session_create_page_type,
         case when session.flags & 2 = 2 then 1 else 0 end,
         coalesce(jh.id_category, j.id_category)
         ;
