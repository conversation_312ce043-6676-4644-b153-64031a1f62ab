        create temp table if not exists tmp_active_test as
        SELECT distinct eat.id_test
        FROM link_dbo.email_account_test eat
                 join an.email_account_test_settings eats on eat.id_test = eats.id_test
        where (eat.date_diff >= (current_date::date - 7) - '1900-01-01' and
               coalesce(cast(eats.start_date as date), '2021-01-01') < '2021-08-10')
           or (coalesce(cast(eats.start_date as date), '2021-01-01') > '2021-08-10' and
               (eat.date_diff is null or eat.date_diff >= _input_date_diff))
        ;



        create temp table if not exists tmp_session_account as
        Select id_session,
               max(id_account) as id_account
        from link_dbo.session_account
        where date_diff = _input_date_diff
        group by id_session
        ;


        create temp table if not exists tmp_user_tests as
        select EAT.id_account,
               array_to_string(array_agg(concat((EAT.id_test), ':', EAT.id_group)), ',') as account_tests
        from link_dbo.email_account_test EAT
                 inner join an.email_account_test_settings EATS on EAT.id_test = EATS.id_test
        where (EAT.id_test in (select id_test from tmp_active_test) or EATS.is_active = 1)
            and EAT.date_diff <= _input_date_diff
        group by EAT.id_account
        ;



        create temp table if not exists tmp_users as
        select ut.id_account,
               ut.account_tests,
               case
                   when (_input_date_diff - (CAST(ACC.date_add AS DATE) - DATE '1900-01-01')::INTEGER) <= 7 then 'new'
                   when (_input_date_diff - (CAST(ACC.date_add AS DATE) - DATE '1900-01-01')::INTEGER) between 8 and 29
                       then '8-29 days'
                   else '30+ days' end        as account_age,
               case
                   when (_input_date_diff - AI.last_visit) <= 7 then 'active last 7days'
                   when (_input_date_diff - AI.last_visit) between 8 and 29 then 'active last 8-29days'
                   else 'active 30+ days' end as account_active
        from tmp_user_tests ut
                 inner join link_dbo.account ACC on ACC.id = ut.id_account
                 inner join link_dbo.account_info AI on AI.id_account = ut.id_account
        group by ut.id_account, ut.account_tests,
                 case
                     when (_input_date_diff - (CAST(ACC.date_add AS DATE) - DATE '1900-01-01')::INTEGER) <= 7 then 'new'
                     when (_input_date_diff - (CAST(ACC.date_add AS DATE) - DATE '1900-01-01')::INTEGER) between 8 and 29
                         then '8-29 days'
                     else '30+ days' end,
                 case
                     when (_input_date_diff - AI.last_visit) <= 7 then 'active last 7days'
                     when (_input_date_diff - AI.last_visit) between 8 and 29 then 'active last 8-29days'
                     else 'active 30+ days' end
        ;

        create index if not exists idx_tmp_users_01 on tmp_users(id_account, account_tests);
        analyze tmp_users;



        create temp table if not exists tmp_accounts as
        select date_diff,
               account_tests,
               account_age,
               account_active,
               count(distinct users.id_account) as value
        from tmp_users users
                 inner join link_dbo.email_account_test EAT on users.id_account = EAT.id_account
        where date_diff = _input_date_diff
        group by date_diff, account_tests,
                 account_age, account_active
        ;


        -- Unsubscribed accounts
        create temp table if not exists tmp_unsub as
        select (CAST(AI.unsub_date AS DATE) - DATE '1900-01-01')::INTEGER as date_diff,
               users.account_tests,
               account_age,
               account_active,
               count(AI.id_account)                                       as value
        from link_dbo.account_info AI
                 inner join tmp_users users on users.id_account = AI.id_account
        where (CAST(AI.unsub_date AS DATE) - DATE '1900-01-01')::INTEGER = _input_date_diff
        group by (CAST(AI.unsub_date AS DATE) - DATE '1900-01-01')::INTEGER, account_tests,
                 account_age, account_active
        ;


        create temp table if not exists tmp_sent as
        select ES.date_diff,
               users.account_tests,
               account_age,
               account_active,
               ES.letter_type,
               count(distinct ES.id_message) as value
        from an.email_sent ES
                 inner join tmp_users users on users.id_account = ES.id_account
        where ES.date_diff = _input_date_diff
        group by ES.date_diff, account_tests, ES.letter_type,
                 account_age, account_active
        ;


        create temp table if not exists tmp_opened as
        select EO.date_diff            as date_diff,
               users.account_tests,
               EO.letter_type,
               count(distinct EO.id_message) as letter_open,
               account_age,
               account_active
       from link_dbo.email_open EO
                 inner join tmp_users users on users.id_account = EO.id_account
        where eo.date_diff = _input_date_diff
        group by EO.date_diff,
                 account_tests,
                 EO.letter_type,
                 account_age,
                 account_active
        ;


        create temp table if not exists tmp_visited
        as
        select EV.date_diff                           as date_diff,
               users.account_tests,
               EV.letter_type,
               case when EV.flags & 1 = 1 then 1 else 0 end as mobile,
               account_age,
               account_active,
               count(distinct EV.id_message)                as letter_click
        from link_dbo.email_visit EV
                 inner join tmp_users users on users.id_account = EV.id_account
        where EV.date_diff = _input_date_diff
        group by EV.date_diff, account_tests, EV.letter_type,
                 case when EV.flags & 1 = 1 then 1 else 0 end,
                 account_age, account_active
        ;


        create temp table if not exists tmp_alertview as
        select SA.date_diff,
               users.account_tests,
               case when SA.service_flags & 1 = 1 then 1 else 0 end as mobile,
               account_age,
               account_active,
               email_sent.letter_type,
               count(distinct SA.id)                                as alertview_cnt,
               count(distinct SC.id)                                as click_cnt
        from public.session_alertview SA
                 join link_dbo.email_alert EA on SA.sub_id_alert = EA.id
                 join tmp_users users on users.id_account = EA.id_account
                 left join public.session_click SC on SC.date_diff = SA.date_diff
                                                          and SC.id_alertview = SA.id
                 join public.session s on SA.date_diff = s.date_diff
                                            and SA.id_session = s.id
                 left join public.session_alertview_message SAM on SA.date_diff = SAM.date_diff
                                                                       and SA.id = SAM.id_alertview
                 left join an.email_sent email_sent on SAM.id_message::text = email_sent.id_message::text
        where SA.date_diff = _input_date_diff
          and coalesce(s.flags, 0) & 1 = 0
        group by SA.date_diff, account_tests,
                 case when SA.service_flags & 1 = 1 then 1 else 0 end,
                 account_age, account_active, email_sent.letter_type
        ;


        create temp table if not exists tmp_jdps as
        select SJ.date_diff,
               users.account_tests,
               coalesce(SJ.letter_type, email_sent.letter_type) as letter_type,
               case when SJ.flags & 1 = 1 then 1 else 0 end     as mobile,
               account_age,
               account_active,
               count(distinct SJ.id)                            as jdp_cnt
        from public.session_jdp SJ
                 join public.session_click SC on SJ.date_diff = Sc.date_diff
                          and SJ.id_click = SC.id
                 inner join tmp_users users on users.id_account = SJ.id_account
                 join public.session s on SJ.date_diff = s.date_diff and
                         SJ.id_session = s.id
                 left join public.session_alertview_message SAM on SC.date_diff = SAM.date_diff
                               and SC.id_alertview = SAM.id_alertview
                 left join an.email_sent email_sent
                           on SAM.id_message::text = email_sent.id_message::text
        where SJ.date_diff = _input_date_diff
          and coalesce(s.flags, 0) & 1 = 0
          and SC.flags & 4096 = 0
          and (SC.id_alertview is not null or SJ.letter_type is not null)
        group by SJ.date_diff, account_tests, coalesce(SJ.letter_type, email_sent.letter_type),
                 case when SJ.flags & 1 = 1 then 1 else 0 end,
                 account_age, account_active
        ;



        --+=============
        create temp table if not exists tmp_info_currency as
        select * from link_dbo.info_currency_history
        where date>now() - INTERVAL '130 DAYS' ;
        create index if not exists idx_tmp_info_currency_00 on tmp_info_currency(value_to_usd);


        create temp table if not exists tmp_conv_start as
        select sa.id_project,
               min(con.date_diff) as date_diff
        from public.session_away sa
                 join link_auction.conversion_away_connection con on con.date_diff = sa.date_diff
            and con.id_session_away = sa.id
        group by sa.id_project;


        create temp table if not exists tmp_sam as
        select id_alertview,
               min(id_message::text)::text as id_message
        from public.session_alertview_message
        where date_diff = _input_date_diff
        group by id_alertview;


        --===================================================

        create temp table if not exists tmp_aways as
        select Away.date_diff,
               coalesce(users0.account_tests, users1.account_tests, users2.account_tests)        as account_tests,
               coalesce(Away.letter_type, SJ.letter_type, email_sent.letter_type)                as letter_type,
               case when Away.flags & 1 = 1 then 1 else 0 end                                    as mobile,
               coalesce(users0.account_age, users1.account_age, users2.account_age)              as account_age,
               coalesce(users0.account_active, users1.account_active, users2.account_active)     as account_active,
               count(distinct case
                                  when Away.id_jdp is not null then Away.id end)                 as jdp_aways,
               count(distinct case
                                  when coalesce(Away.letter_type, SJ.letter_type) is not null and
                                       coalesce(SC.id_alertview, SCJ.id_alertview) is null
                                      then Away.id end)                                          as lt_aways,
               count(distinct case
                                  when coalesce(SC.id_alertview, SCJ.id_alertview) is not null
                                      then Away.id end)                                          as serp_aways,
               count(distinct conv.id_session_away)                                              as conversions,
               count(distinct case when Away.date_diff >= conv_start.date_diff then Away.id end) as conversion_aways,
               count(distinct Away.id)                                                           as total_aways,
               1                                                                                 as valid_mark
        from public.session_away Away
                 left join public.session_jdp SJ
                           on Away.date_diff = SJ.date_diff
                               and Away.id_jdp = SJ.id
                 left join public.session_click SC
                           on Away.date_diff = SC.date_diff
                               and Away.id_click = SC.id
                 left join public.session_click as SCJ
                           on SJ.date_diff = SCJ.date_diff
                               and SJ.id_click = SCJ.id
                 left join tmp_info_currency ic on ic.id_currency  = Away.id_currency
                                                            and ic.date::date = Away.date::date
                 left join
                     (select distinct date_diff, id_session_away
                      from link_auction.conversion_away_connection) conv on conv.date_diff = Away.date_diff
                                                                                and conv.id_session_away = Away.id
                 left join tmp_conv_start conv_start on Away.id_project = conv_start.id_project
                 left join public.session_alertview sal on Away.date_diff = sal.date_diff
                                                            and coalesce(sc.id_alertview, scj.id_alertview) = sal.id
                 left join link_dbo.email_alert ea on sal.sub_id_alert = ea.id
                 left join tmp_session_account sa on sa.id_session = Away.id_session
                 left join tmp_users users0 on users0.id_account = Away.id_account
                 left join tmp_users users1 on users1.id_account = ea.id_account
                 left join tmp_users users2 on users2.id_account = sa.id_account
                 join public.session s on Away.date_diff = s.date_diff and Away.id_session = s.id
                 left join tmp_sam sam on coalesce(SC.id_alertview, SCJ.id_alertview) = SAM.id_alertview
                 left join an.email_sent email_sent on SAM.id_message::text = email_sent.id_message::text
        where Away.date_diff = _input_date_diff
          and coalesce(s.flags, 0) & 1 = 0
          and Away.flags & 512 = 0
        group by Away.date_diff,
                 coalesce(users0.account_tests, users1.account_tests, users2.account_tests),
                 coalesce(users0.account_age, users1.account_age, users2.account_age),
                 coalesce(users0.account_active, users1.account_active, users2.account_active),
                 case
                     when Away.flags & 1 = 1 then 1
                     else 0 end,
                 coalesce(Away.letter_type, SJ.letter_type, email_sent.letter_type);
        --===================================================


        --+========================================

        create temp table if not exists tmp_Away as
            select s.date_diff,
                     s.id                                                             as id_session,
                     sa.click_price * coalesce(ic.value_to_usd, 0)                    click_price_usd,
                     coalesce(sa.letter_type, sj.letter_type, email_sent.letter_type) as letter_type,
                     coalesce(sc.id_alertview, scj.id_alertview)                      as id_alertview,
                     sa.id_jdp,
                     coalesce(sa.id_account, sj.id_account) as                        id_account,
                     s.flags,
                     sa.id_project
              from public.session_away sa
                       inner join public.session s on sa.date_diff = s.date_diff
                                                       and sa.id_session = s.id
                       inner join tmp_info_currency ic on ic.id_currency  = sa.id_currency
                                                            and ic.date::date = sa.date::date
                  -- serp -> away
                       left join public.session_click sc on sc.date_diff = sa.date_diff
                                                             and sc.id = sa.id_click
                  -- serp -> jdp -> away
                       left join public.session_jdp sj on sj.date_diff = sa.date_diff
                                                           and sj.id = sa.id_jdp
                       left join public.session_click scj on scj.date_diff = sj.date_diff
                                                              and scj.id = sj.id_click
                       left join tmp_sam sam on coalesce(SC.id_alertview, SCJ.id_alertview) = SAM.id_alertview
                       left join an.email_sent email_sent on SAM.id_message::text = email_sent.id_message::text
              where sa.date_diff = _input_date_diff
                and coalesce(s.flags, 0) & 1 = 0
                and coalesce(sa.flags, 0) & 2 = 0
                and sa.flags & 512 = 0
        ;


        create temp table if not exists tmp_revenue as
        Select Away.date_diff,
               account_tests,
               letter_type,
               case when Away.flags & 16 = 16 then 1 else 0 end as mobile,
               account_age,
               account_active,
               sum(case
                       when Away.id_jdp is not null then click_price_usd
                       else 0 end)                              as jdp_revenue,
               sum(case
                       when Away.letter_type is not null and Away.id_alertview is null then click_price_usd
                       else 0 end)                              as lt_revenue,
               sum(case
                       when Away.id_alertview is not null then click_price_usd
                       else 0 end)                              as serp_revenue,
               sum(case
                       when Away.date_diff >= conv_start.date_diff then click_price_usd
                       else 0 end)                              as conversion_revenue,
               sum(click_price_usd)                             as total_revenue,
               1                                                as valid_mark
        from tmp_Away Away
                 left join tmp_session_account sa on Away.id_session = sa.id_session
                 left join public.session_alertview sal on sal.date_diff = _input_date_diff
                                                           and Away.id_alertview = sal.id
                 left join link_dbo.email_alert ea on sal.sub_id_alert = ea.id
                 join tmp_users users on users.id_account = coalesce(coalesce(Away.id_account, sa.id_account), ea.id_account)
                 left join tmp_conv_start conv_start on Away.id_project = conv_start.id_project
        group by Away.date_diff,
                 account_tests,
                 letter_type,
                 case when Away.flags & 16 = 16 then 1 else 0 end,
                 account_age,
                 account_active;
        --+========================================


        --+========================================
        create temp table if not exists tmp_applies as
        select SA.date_diff,
               account_tests,
               coalesce(SJ.letter_type, email_sent.letter_type) as letter_type,
               case
                   when SJ.flags & 1 = 1 then 1
                   else 0 end                                   as mobile,
               account_age,
               account_active,
               count(distinct SA.id)                            as applies_cnt
        from public.session_jdp SJ
                 inner join public.session_jdp_action SJA
                            on SJ.date_diff = SJA.date_diff
                                and SJ.id = SJA.id_jdp
                 inner join public.session_apply SA
                            on SA.date_diff = SJA.date_diff
                                and SA.id_src_jdp_action = SJA.id
                 inner join tmp_users users on users.id_account = SA.id_account
                 join public.session s on SJ.date_diff = s.date_diff and SJ.id_session = s.id
                 left join public.session_click scj on scj.date_diff = sj.date_diff
            and scj.id = sj.id_click
                 left join public.session_alertview_message SAM on scj.date_diff = SAM.date_diff
                                                                       and scj.id_alertview = SAM.id_alertview
                 left join an.email_sent email_sent on SAM.id_message::text = email_sent.id_message::text
        where SA.date_diff = _input_date_diff
          and coalesce(s.flags, 0) & 1 = 0
          and (SJ.letter_type is not null or scj.id_alertview is not null)
        group by SA.date_diff, account_tests,
                 account_age, account_active,
                 case
                     when SJ.flags & 1 = 1 then 1
                     else 0 end,
                 coalesce(SJ.letter_type, email_sent.letter_type)
        ;
        --+========================================


        --+========================================
        create temp table if not exists tmp_final as
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               null::int                           as letter_type,
               null::int                           as mobile,
               value,
               'Test Accounts'                     as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_accounts
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               null::int                           as letter_type,
               null::int                           as mobile,
               value,
               'Unsubscribed Accounts'             as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_unsub
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               null::int                           as mobile,
               value,
               'Letters Sent'                      as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_sent
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               null::int                           as mobile,
               letter_open,
               'Letters Opened'                    as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_opened
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               --send_interval,
               mobile,
               letter_click,
               'Letters Clicked'                   as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_visited
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type                         as letter_type,
               mobile,
               alertview_cnt,
               'Alertviews'                        as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_alertview
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type                         as letter_type,
               mobile,
               click_cnt,
               'Clicks'                            as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_alertview
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               jdp_cnt,
               'JDPs'                              as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_jdps
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               jdp_aways,
               'JDP Aways'                         as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_aways
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               conversions,
               'Conversions'                       as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_aways
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               --send_interval,
               mobile,
               jdp_revenue,
               'JDP Revenue'                       as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_revenue
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               --send_interval,
               mobile,
               serp_revenue,
               'SERP Revenue'                      as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_revenue
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               --send_interval,
               mobile,
               conversion_revenue,
               'Conversion Revenue'                as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_revenue
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               serp_aways,
               'SERP Aways'                        as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_aways
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               total_aways,
               'Total Aways'                       as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_aways
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               conversion_aways,
               'Conversion Aways'                  as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_aways
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               lt_aways,
               'Letter Aways'                      as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_aways
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               lt_revenue,
               'Letter Revenue'                    as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_revenue
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               total_revenue,
               'Total Revenue'                     as metric,
               account_age,
               account_active,
               valid_mark
        from tmp_revenue
        union all
        SELECT date_diff + DATE '1900-01-01'::DATE as date,
               account_tests,
               letter_type,
               mobile,
               applies_cnt,
               'Applies'                           as metric,
               account_age,
               account_active,
               1                                   as valid_mark
        from tmp_applies;
        --+========================================


        delete from an.rpl_email_abtest_agg
        where load_date_diff = _input_date_diff;


        insert into an.rpl_email_abtest_agg(country_id, action_date, account_test_num, letter_type, is_mobile,
                                            metric_cnt, metric_name, account_age, account_active, is_valid, load_date_diff)
        Select _country_id   as country_id,
               date::date    as action_date,
               account_tests as account_test_num,
               letter_type,
               mobile        as is_mobile,
               sum(value)    as metric_cnt,
               metric        as metric_name,
               account_age,
               account_active,
               valid_mark    as is_valid,
               _input_date_diff    as load_date_diff
        from tmp_final
        where value != 0
          and account_tests is not null
        group by date::date,
                 account_tests,
                 letter_type,
                 mobile,
                 metric,
                 account_age,
                 account_active,
                 valid_mark;
        --

        drop table if exists tmp_aways;
        drop table if exists tmp_accounts;
        drop table if exists tmp_active_test;
        drop table if exists tmp_applies;
        drop table if exists tmp_alertview;
        drop table if exists tmp_Final;
        drop table if exists tmp_revenue;
        drop table if exists tmp_user_tests;
        drop table if exists tmp_users;
        drop table if exists tmp_unsub;
        drop table if exists tmp_sent;
        drop table if exists tmp_opened;
        drop table if exists tmp_visited;
        drop table if exists tmp_session_account;
        drop table if exists tmp_jdps;
        drop table if exists tmp_account;
        drop table if exists tmp_account_info;
        drop table if exists tmp_Away;
        drop table if exists tmp_info_currency;
        drop table if exists tmp_conversion_away_connection;
        drop table if exists tmp_conv_start;
        drop table if exists tmp_sam;


