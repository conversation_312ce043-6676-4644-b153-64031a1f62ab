declare @dt_begin int = ${dt_begin},
		@dt_end int = ${dt_end},
		@country_id int = ${country_id}


select @country_id                           as country_id,
       s.date_diff                           as session_datediff,
       s.id_current_traf_source              as traffic_source_id,
       sign(s.flags & 16)                    as device_type_id,
       s.session_create_page_type,
       ss.search_source                      as search_source_id,
       count(distinct ss.id)                 as search_cnt,
       count(distinct sc.id)                 as serp_click_cnt,
       count(distinct sa.id)                 as away_cnt,
       sum(sa.click_price * ic.value_to_usd) as revenue_usd
from dbo.session s
         join dbo.session_search ss
              on s.date_diff = ss.date_diff and
                 s.id = ss.id_session
         left join dbo.session_click sc
                   on ss.id = sc.id_search and
                      ss.date_diff = sc.date_diff
         left join dbo.info_currency ic
                   on ic.id = sc.id_currency
         left join dbo.session_away sa
                   on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
where s.date_diff between @dt_begin and @dt_end
  and s.flags & 1 = 0
  and s.flags & 64 = 0
  and s.session_create_page_type = 8
group by ss.search_source,
         s.session_create_page_type,
         s.date_diff,
         s.id_current_traf_source,
         sign(s.flags & 16)

union

select @country_id                           as country_id,
       s.date_diff                           as session_datediff,
       s.id_current_traf_source              as traffic_source_id,
       sign(s.flags & 16)                    as device_type_id,
       s.session_create_page_type,
       ss.search_source                      as search_source_id,
       count(distinct ss.id)                 as search_cnt,
       count(distinct sc.id)                 as serp_click_cnt,
       count(distinct sa.id)                 as away_cnt,
       sum(sa.click_price * ic.value_to_usd) as revenue_usd
from dbo.session s
         join dbo.session_search ss
              on s.date_diff = ss.date_diff and
                 s.id = ss.id_session
         left join dbo.session_click sc
                   on ss.id = sc.id_search and
                      ss.date_diff = sc.date_diff
         left join dbo.info_currency ic
                   on ic.id = sc.id_currency
         left join dbo.session_away sa
                   on sc.date_diff = sa.date_diff
                       and sc.id = sa.id_click
where ss.search_source in (118, 119, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144)
  and s.date_diff between @dt_begin and @dt_end
  and s.flags & 1 = 0
  and s.flags & 64 = 0
  and not s.session_create_page_type = 8
group by ss.search_source,
         s.session_create_page_type,
         s.date_diff,
         s.id_current_traf_source,
         sign(s.flags & 16)
;
