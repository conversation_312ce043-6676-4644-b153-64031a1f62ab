declare @date_diff int = datediff(day, 0, getdate() - 1);


select st.groups                               as groups,
       s.id                                    as id_session,
       s.date_diff                             as date_diff,
       iif(s.flags & 2 = 2, 1, 0)              as is_returned,
       iif(s.flags & 16 = 16, 1, 0)            as is_mobile,
       coalesce(s.session_create_page_type, 0) as session_create_page_type,
       s.id_traf_source                        as id_traffic_source,
       s.id_current_traf_source                as id_current_traffic_source,
       uts.channel                             as channel_1,
       uts1.channel                            as channel_2,
       case
           when (s.date_diff - s.first_visit_date_diff) < 8 then 7
           when (s.date_diff - s.first_visit_date_diff) < 15 then 14
           when (s.date_diff - s.first_visit_date_diff) <= 30 then 30
           when (s.date_diff - s.first_visit_date_diff) <= 90 then 90
           when (s.date_diff - s.first_visit_date_diff) > 90 then 100
           end                                 as user_dtd_lifetime
into #sessions
from dbo.session s with (nolock)
         left join dbo.session_test_agg st with (nolock) on st.date_diff = s.date_diff and s.id = st.id_session
         left join dbo.u_traffic_source uts with (nolock) on s.id_traf_source = uts.id
         left join dbo.u_traffic_source uts1 with (nolock) on s.id_current_traf_source = uts1.id
where s.flags & 1 = 0 and s.flags & 4 = 0
  and s.date_diff = @date_diff;

with _session_action as (
    select sessions.groups,
           sessions.id_session,
           sessions.date_diff,
           sessions.is_returned,
           sessions.is_mobile,
           sessions.session_create_page_type,
           sessions.channel_1,
           sessions.channel_2,
           sessions.user_dtd_lifetime,
           sact.type as action_type,
           sact.id   as id_session_action

    from #sessions sessions with (nolock)
             left join dbo.session_action sact with (nolock)
                       on sact.date_diff = sessions.date_diff and sessions.id_session = sact.id_session
    where sessions.date_diff = @date_diff
),

     _alertview_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                saa.action as action_type,
                saa.id     as id_sess_alertview_action,
                sa.id      as id_sess_alertview

         from dbo.session_alertview sa with (nolock)
                  join #sessions sessions with (nolock) on sa.date_diff = sessions.date_diff and
                                                           sa.id_session = sessions.id_session
                  left join (
             select saa.id,
                    saa.action,
                    saa.date_diff,
                    (select top 1 id
                     from dbo.session_alertview sa with (nolock)
                     where sa.sub_id_alert = saa.id_alertview
                       and saa.date_diff = sa.date_diff
                       and saa.date_time > sa.date
                     order by date) as id_alertview
             from dbo.session_alertview_action saa with (nolock)
             where saa.date_diff = @date_diff
         ) saa
                            on saa.date_diff = sa.date_diff and sa.id = saa.id_alertview
         where sa.date_diff = @date_diff
     ),

     _apply_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                sapa.type as action_type,
                sapa.id   as id_sess_apply_action,
                sj.id     as id_sess_jdp
         from dbo.session_jdp sj with (nolock)
                  join #sessions sessions with (nolock)
                       on sessions.date_diff = sj.date_diff and sessions.id_session = sj.id_session
                  left join dbo.session_apply_action sapa with (nolock)
                            on sj.date_diff = sapa.date_diff and sj.id = sapa.id_jdp
         where sj.date_diff = @date_diff
           and sj.flags & 4 = 4
     ),

/*
		_auth_action as (
	select     sessions.groups,
               sessions.id_session,
               sessions.date_diff,
               sessions.is_local,
               sessions.session_create_page_type,
               sessions.channel_1,
               sessions.channel_2,
               sessions.user_dtd_lifetime,
			   saua.type                                 as action_type,
               saua.id                                   as id_sess_auth_action,
			   sauth.id                                  as id_sess_auth

	from dbo.session_auth sauth with (nolock)
	join #sessions sessions with (nolock) on sessions.date_diff = sauth.date_diff and sessions.id_session = sauth.id_session
	left join dbo.session_auth_action saua with (nolock) on sauth.date_diff = saua.date_diff and sauth.id = saua.id_auth
		where date_diff = @date_diff   */

     _cdp_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                sca.type as action_type,
                sca.id   as id_sess_cdp_action,
                sc.id    as id_sess_cdp

         from dbo.session_cdp sc with (nolock)
                  join #sessions sessions with (nolock)
                       on sessions.date_diff = sc.date_diff and sessions.id_session = sc.id_session
                  left join dbo.session_cdp_action sca with (nolock)
                            on sc.date_diff = sca.date_diff and sc.id = sca.id_cdp
         where sc.date_diff = @date_diff
     ),

     _feature_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                sfa.type as action_type,
                sfa.id   as id_sess_feature_action,
                sf.id    as id_sess_feature

         from #sessions sessions with (nolock)
                  left join dbo.session_feature sf with (nolock)
                            on sessions.date_diff = sf.date_diff and sessions.id_session = sf.id_session
                  left join dbo.session_feature_action sfa with (nolock)
                            on sf.date_diff = sfa.date_diff and sf.id = sfa.id_session_feature
         where sessions.date_diff = @date_diff
     ),

     _filter_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                sfla.filter_type as entity_type,
                sfla.action      as action_type,
                sfla.id          as id_sess_filter_action,
                ss.id            as id_sess_filter

         from dbo.session_search ss with (nolock)
                  join #sessions sessions with (nolock)
                       on sessions.date_diff = ss.date_diff and sessions.id_session = ss.id_session
                  left join dbo.session_filter_action sfla with (nolock)
                            on ss.date_diff = sfla.date_diff and ss.id = sfla.id_search_prev
         where ss.date_diff = @date_diff
     ),

     _jdp_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                sja.type as action_type,
                sja.id   as id_sess_jdp_action,
                sj.id    as id_sess_jdp

         from dbo.session_jdp sj with (nolock)
                  join #sessions sessions with (nolock)
                       on sessions.date_diff = sj.date_diff and sessions.id_session = sj.id_session
                  left join dbo.session_jdp_action sja with (nolock)
                            on sj.date_diff = sja.date_diff and sj.id = sja.id_jdp
         where sj.date_diff = @date_diff
     ),

--      _letter_action as (
--          select sessions.groups,
--                 sessions.id_session,
--                 sessions.date_diff,
--                 sessions.is_returned,
--                 sessions.is_mobile,
--                 sessions.session_create_page_type,
--                 sessions.channel_1,
--                 sessions.channel_2,
--                 sessions.user_dtd_lifetime,
--                 sla.type       as action_type,
--                 sla.id         as id_sess_letter_action,
--                 sla.id_message as id_sess_letter
--
--          from #sessions sessions with (nolock)
--                   join dbo.session_letter_action sla with (nolock)
--                        on sessions.date_diff = sla.date_diff and sessions.id_session = sla.id_session
--          where sessions.date_diff = @date_diff),

     _profile_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                spa.type as action_type,
                spa.id   as id_sess_profile_action,
                sp.id    as id_sess_profile

         from dbo.session_profile sp with (nolock)
                  join #sessions sessions with (nolock)
                       on sessions.date_diff = sp.date_diff and sessions.id_session = sp.id_session
                  left join dbo.session_profile_action spa with (nolock)
                            on sp.date_diff = spa.date_diff and sp.id = spa.id_session_profile
         where sp.date_diff = @date_diff
     ),

/*		_questionnaire_action as (
    Select     sessions.groups,
               sessions.id_session,
               sessions.date_diff,
               sessions.is_local,
               sessions.session_create_page_type,
               sessions.channel_1,
               sessions.channel_2,
               sessions.user_dtd_lifetime,
			   sqa.question_type                         as action_type,
               		  sqa.id                                    as id_sess_questionnaire_action,
	         		  sqa.id_questionnaire                      as id_sess_questionnaire

	From #sessions sessions with (nolock)
	left join dbo.session_questionnaire_action sqa with (nolock) on sessions.date_diff = sqa.date_diff and sessions.id_session = sqa.id_session
       	 	where sessions.date_diff = @date_diff), */

     _notification_center_action as (
         select sessions.groups,
                sessions.id_session,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                snca.notification_type as entity_type,
                snca.action_type       as action_type,
                snca.id                as id_sess_notification_center_action

         from #sessions sessions with (nolock)
                  left join dbo.session_notification_center_action snca with (nolock)
                            on sessions.date_diff = snca.date_diff and sessions.id_session = snca.id_session
         where sessions.date_diff = @date_diff
     ),


     action_union as (
         select sessions.groups,
                sessions.date_diff,
                sessions.is_returned,
                sessions.is_mobile,
                sessions.session_create_page_type,
                sessions.channel_1,
                sessions.channel_2,
                sessions.user_dtd_lifetime,
                cast(null as int)                   as entity_type,
                cast(-1 as int)                     as action_type,
                'session'                           as metric,
                cast(0 as bigint)                   as event_cnt,
                cast(0 as bigint)                   as page_cnt,
                count(distinct sessions.id_session) as session_cnt
         from #sessions sessions
         group by sessions.groups,
                  sessions.date_diff,
                  sessions.is_returned,
                  sessions.is_mobile,
                  sessions.session_create_page_type,
                  sessions.channel_1,
                  sessions.channel_2,
                  sessions.user_dtd_lifetime
         union
         select sa.groups,
                sa.date_diff,
                sa.is_returned,
                sa.is_mobile,
                sa.session_create_page_type,
                sa.channel_1,
                sa.channel_2,
                sa.user_dtd_lifetime,
                cast(null as int)                    as entity_type,
                sa.action_type                       as action_type,
                'session_action'                     as metric,
                count(distinct sa.id_session_action) as event_cnt,
                cast(0 as bigint)                    as page_cnt,
                count(distinct sa.id_session)        as session_cnt
         from _session_action sa
         group by sa.groups,
                  sa.date_diff,
                  sa.is_returned,
                  sa.is_mobile,
                  sa.session_create_page_type,
                  sa.channel_1,
                  sa.channel_2,
                  sa.user_dtd_lifetime,
                  sa.action_type
         union
         select sa.groups,
                sa.date_diff,
                sa.is_returned,
                sa.is_mobile,
                sa.session_create_page_type,
                sa.channel_1,
                sa.channel_2,
                sa.user_dtd_lifetime,
                cast(null as int)             as entity_type,
                cast(-1 as int)               as action_type,
                'session_action'              as metric,
                cast(0 as bigint)             as event_cnt,
                cast(0 as bigint)             as page_cnt,
                count(distinct sa.id_session) as session_cnt
         from _session_action sa
         group by sa.groups,
                  sa.date_diff,
                  sa.is_returned,
                  sa.is_mobile,
                  sa.session_create_page_type,
                  sa.channel_1,
                  sa.channel_2,
                  sa.user_dtd_lifetime
         union
         select aa.groups,
                aa.date_diff,
                aa.is_returned,
                aa.is_mobile,
                aa.session_create_page_type,
                aa.channel_1,
                aa.channel_2,
                aa.user_dtd_lifetime,
                cast(null as int)                           as entity_type,
                aa.action_type                              as action_type,
                'alertview_action'                          as metric,
                count(distinct aa.id_sess_alertview_action) as event_cnt,
                count(distinct aa.id_sess_alertview)        as page_cnt,
                count(distinct aa.id_session)               as session_cnt
         from _alertview_action aa
         group by aa.groups,
                  aa.date_diff,
                  aa.is_returned,
                  aa.is_mobile,
                  aa.session_create_page_type,
                  aa.channel_1,
                  aa.channel_2,
                  aa.user_dtd_lifetime,
                  aa.action_type
         union
         select aa.groups,
                aa.date_diff,
                aa.is_returned,
                aa.is_mobile,
                aa.session_create_page_type,
                aa.channel_1,
                aa.channel_2,
                aa.user_dtd_lifetime,
                cast(null as int)             as entity_type,
                cast(-1 as int)               as action_type,
                'alertview_action'            as metric,
                cast(0 as bigint)             as event_cnt,
                cast(0 as bigint)             as page_cnt,
                count(distinct aa.id_session) as session_cnt
         from _alertview_action aa
         group by aa.groups,
                  aa.date_diff,
                  aa.is_returned,
                  aa.is_mobile,
                  aa.session_create_page_type,
                  aa.channel_1,
                  aa.channel_2,
                  aa.user_dtd_lifetime
         union
         select apa.groups,
                apa.date_diff,
                apa.is_returned,
                apa.is_mobile,
                apa.session_create_page_type,
                apa.channel_1,
                apa.channel_2,
                apa.user_dtd_lifetime,
                cast(null as int)                        as entity_type,
                apa.action_type                          as action_type,
                'apply_action'                           as metric,
                count(distinct apa.id_sess_apply_action) as event_cnt,
                count(distinct apa.id_sess_jdp)          as page_cnt,
                count(distinct apa.id_session)           as session_cnt
         from _apply_action apa
         group by apa.groups,
                  apa.date_diff,
                  apa.is_returned,
                  apa.is_mobile,
                  apa.session_create_page_type,
                  apa.channel_1,
                  apa.channel_2,
                  apa.user_dtd_lifetime,
                  apa.action_type
         union
         select apa.groups,
                apa.date_diff,
                apa.is_returned,
                apa.is_mobile,
                apa.session_create_page_type,
                apa.channel_1,
                apa.channel_2,
                apa.user_dtd_lifetime,
                cast(null as int)              as entity_type,
                cast(-1 as int)                as action_type,
                'apply_action'                 as metric,
                cast(0 as bigint)              as event_cnt,
                cast(0 as bigint)              as page_cnt,
                count(distinct apa.id_session) as session_cnt
         from _apply_action apa
         group by apa.groups,
                  apa.date_diff,
                  apa.is_returned,
                  apa.is_mobile,
                  apa.session_create_page_type,
                  apa.channel_1,
                  apa.channel_2,
                  apa.user_dtd_lifetime
         union
         select cdpa.groups,
                cdpa.date_diff,
                cdpa.is_returned,
                cdpa.is_mobile,
                cdpa.session_create_page_type,
                cdpa.channel_1,
                cdpa.channel_2,
                cdpa.user_dtd_lifetime,
                cast(null as int)                       as entity_type,
                cdpa.action_type                        as action_type,
                'cdp_action'                            as metric,
                count(distinct cdpa.id_sess_cdp_action) as event_cnt,
                count(distinct cdpa.id_sess_cdp)        as page_cnt,
                count(distinct cdpa.id_session)         as session_cnt
         from _cdp_action cdpa
         group by cdpa.groups,
                  cdpa.date_diff,
                  cdpa.is_returned,
                  cdpa.is_mobile,
                  cdpa.session_create_page_type,
                  cdpa.channel_1,
                  cdpa.channel_2,
                  cdpa.user_dtd_lifetime,
                  cdpa.action_type
         union
         select cdpa.groups,
                cdpa.date_diff,
                cdpa.is_returned,
                cdpa.is_mobile,
                cdpa.session_create_page_type,
                cdpa.channel_1,
                cdpa.channel_2,
                cdpa.user_dtd_lifetime,
                cast(null as int)               as entity_type,
                cast(-1 as int)                 as action_type,
                'cdp_action'                    as metric,
                cast(0 as bigint)               as event_cnt,
                cast(0 as bigint)               as page_cnt,
                count(distinct cdpa.id_session) as session_cnt
         from _cdp_action cdpa
         group by cdpa.groups,
                  cdpa.date_diff,
                  cdpa.is_returned,
                  cdpa.is_mobile,
                  cdpa.session_create_page_type,
                  cdpa.channel_1,
                  cdpa.channel_2,
                  cdpa.user_dtd_lifetime
         union
         select fea.groups,
                fea.date_diff,
                fea.is_returned,
                fea.is_mobile,
                fea.session_create_page_type,
                fea.channel_1,
                fea.channel_2,
                fea.user_dtd_lifetime,
                cast(null as int)                          as entity_type,
                fea.action_type                            as action_type,
                'feature_action'                           as metric,
                count(distinct fea.id_sess_feature_action) as event_cnt,
                count(distinct fea.id_sess_feature)        as page_cnt,
                count(distinct fea.id_session)             as session_cnt
         from _feature_action fea
         group by fea.groups,
                  fea.date_diff,
                  fea.is_returned,
                  fea.is_mobile,
                  fea.session_create_page_type,
                  fea.channel_1,
                  fea.channel_2,
                  fea.user_dtd_lifetime,
                  fea.action_type
         union
         select fea.groups,
                fea.date_diff,
                fea.is_returned,
                fea.is_mobile,
                fea.session_create_page_type,
                fea.channel_1,
                fea.channel_2,
                fea.user_dtd_lifetime,
                cast(null as int)              as entity_type,
                cast(-1 as int)                as action_type,
                'feature_action'               as metric,
                cast(0 as bigint)              as event_cnt,
                cast(0 as bigint)              as page_cnt,
                count(distinct fea.id_session) as session_cnt
         from _feature_action fea
         group by fea.groups,
                  fea.date_diff,
                  fea.is_returned,
                  fea.is_mobile,
                  fea.session_create_page_type,
                  fea.channel_1,
                  fea.channel_2,
                  fea.user_dtd_lifetime,
                  fea.action_type
         union
         select fla.groups,
                fla.date_diff,
                fla.is_returned,
                fla.is_mobile,
                fla.session_create_page_type,
                fla.channel_1,
                fla.channel_2,
                fla.user_dtd_lifetime,
                fla.entity_type,
                fla.action_type                           as action_type,
                'filter_action'                           as metric,
                count(distinct fla.id_sess_filter_action) as event_cnt,
                count(distinct fla.id_sess_filter)        as page_cnt,
                count(distinct fla.id_session)            as session_cnt
         from _filter_action fla
         group by fla.groups,
                  fla.date_diff,
                  fla.is_returned,
                  fla.is_mobile,
                  fla.session_create_page_type,
                  fla.channel_1,
                  fla.channel_2,
                  fla.user_dtd_lifetime,
                  fla.entity_type,
                  fla.action_type
         union
         select fla.groups,
                fla.date_diff,
                fla.is_returned,
                fla.is_mobile,
                fla.session_create_page_type,
                fla.channel_1,
                fla.channel_2,
                fla.user_dtd_lifetime,
                fla.entity_type,
                cast(-1 as int)                as action_type,
                'filter_action'                as metric,
                cast(0 as bigint)              as event_cnt,
                cast(0 as bigint)              as page_cnt,
                count(distinct fla.id_session) as session_cnt
         from _filter_action fla
         group by fla.groups,
                  fla.date_diff,
                  fla.is_returned,
                  fla.is_mobile,
                  fla.session_create_page_type,
                  fla.channel_1,
                  fla.channel_2,
                  fla.user_dtd_lifetime,
                  fla.entity_type
         union
         select jdpa.groups,
                jdpa.date_diff,
                jdpa.is_returned,
                jdpa.is_mobile,
                jdpa.session_create_page_type,
                jdpa.channel_1,
                jdpa.channel_2,
                jdpa.user_dtd_lifetime,
                cast(null as int)                       as entity_type,
                jdpa.action_type                        as action_type,
                'jdp_action'                            as metric,
                count(distinct jdpa.id_sess_jdp_action) as event_cnt,
                count(distinct jdpa.id_sess_jdp)        as page_cnt,
                count(distinct jdpa.id_session)         as session_cnt
         from _jdp_action jdpa
         group by jdpa.groups,
                  jdpa.date_diff,
                  jdpa.is_returned,
                  jdpa.is_mobile,
                  jdpa.session_create_page_type,
                  jdpa.channel_1,
                  jdpa.channel_2,
                  jdpa.user_dtd_lifetime,
                  jdpa.action_type
         union
         select jdpa.groups,
                jdpa.date_diff,
                jdpa.is_returned,
                jdpa.is_mobile,
                jdpa.session_create_page_type,
                jdpa.channel_1,
                jdpa.channel_2,
                jdpa.user_dtd_lifetime,
                cast(null as int)               as entity_type,
                cast(-1 as int)                 as action_type,
                'jdp_action'                    as metric,
                cast(0 as bigint)               as event_cnt,
                cast(0 as bigint)               as page_cnt,
                count(distinct jdpa.id_session) as session_cnt
         from _jdp_action jdpa
         group by jdpa.groups,
                  jdpa.date_diff,
                  jdpa.is_returned,
                  jdpa.is_mobile,
                  jdpa.session_create_page_type,
                  jdpa.channel_1,
                  jdpa.channel_2,
                  jdpa.user_dtd_lifetime
         union
         --          select la.groups,
--                 la.date_diff,
--                 la.is_local,
--                 la.session_create_page_type,
--                 la.channel_1,
--                 la.channel_2,
--                 la.user_dtd_lifetime,
--                 la.action_type                           as action_type,
--                 'letter_action'                          as metric,
--                 count(distinct la.id_sess_letter_action) as event_cnt,
--                 count(distinct la.id_sess_letter)        as page_cnt,
--                 count(distinct la.id_session)            as session_cnt
--          from _letter_action la
--          group by la.groups,
--                   la.date_diff,
--                   la.is_local,
--                   la.session_create_page_type,
--                   la.channel_1,
--                   la.channel_2,
--                   la.user_dtd_lifetime,
--                   la.action_type
--          union
         select pra.groups,
                pra.date_diff,
                pra.is_returned,
                pra.is_mobile,
                pra.session_create_page_type,
                pra.channel_1,
                pra.channel_2,
                pra.user_dtd_lifetime,
                cast(null as int)                          as entity_type,
                pra.action_type                            as action_type,
                'profile_action'                           as metric,
                count(distinct pra.id_sess_profile_action) as event_cnt,
                count(distinct pra.id_sess_profile)        as page_cnt,
                count(distinct pra.id_session)             as session_cnt
         from _profile_action pra
         group by pra.groups,
                  pra.date_diff,
                  pra.is_returned,
                  pra.is_mobile,
                  pra.session_create_page_type,
                  pra.channel_1,
                  pra.channel_2,
                  pra.user_dtd_lifetime,
                  pra.action_type
         union
         select pra.groups,
                pra.date_diff,
                pra.is_returned,
                pra.is_mobile,
                pra.session_create_page_type,
                pra.channel_1,
                pra.channel_2,
                pra.user_dtd_lifetime,
                cast(null as int)              as entity_type,
                cast(-1 as int)                as action_type,
                'profile_action'               as metric,
                cast(0 as bigint)              as event_cnt,
                cast(0 as bigint)              as page_cnt,
                count(distinct pra.id_session) as session_cnt
         from _profile_action pra
         group by pra.groups,
                  pra.date_diff,
                  pra.is_returned,
                  pra.is_mobile,
                  pra.session_create_page_type,
                  pra.channel_1,
                  pra.channel_2,
                  pra.user_dtd_lifetime
         union
         select nca.groups,
                nca.date_diff,
                nca.is_returned,
                nca.is_mobile,
                nca.session_create_page_type,
                nca.channel_1,
                nca.channel_2,
                nca.user_dtd_lifetime,
                nca.entity_type,
                nca.action_type                                        as action_type,
                'notification_center_action'                           as metric,
                count(distinct nca.id_sess_notification_center_action) as event_cnt,
                cast(0 as bigint)                                      as page_cnt,
                count(distinct nca.id_session)                         as session_cnt
         from _notification_center_action nca
         group by nca.groups,
                  nca.date_diff,
                  nca.is_returned,
                  nca.is_mobile,
                  nca.session_create_page_type,
                  nca.channel_1,
                  nca.channel_2,
                  nca.user_dtd_lifetime,
                  nca.entity_type,
                  nca.action_type
         union
         select nca.groups,
                nca.date_diff,
                nca.is_returned,
                nca.is_mobile,
                nca.session_create_page_type,
                nca.channel_1,
                nca.channel_2,
                nca.user_dtd_lifetime,
                nca.entity_type,
                cast(-1 as int)                as action_type,
                'notification_center_action'   as metric,
                cast(0 as bigint)              as event_cnt,
                cast(0 as bigint)              as page_cnt,
                count(distinct nca.id_session) as session_cnt
         from _notification_center_action nca
         group by nca.groups,
                  nca.date_diff,
                  nca.is_returned,
                  nca.is_mobile,
                  nca.session_create_page_type,
                  nca.channel_1,
                  nca.channel_2,
                  nca.user_dtd_lifetime,
                  nca.entity_type
     )

select au.groups,
       au.date_diff,
       au.is_returned,
       au.is_mobile,
       au.session_create_page_type,
       au.channel_1,
       au.channel_2,
       au.user_dtd_lifetime,
       au.entity_type,
       au.action_type,
       au.metric,
       au.event_cnt,
       au.page_cnt,
       au.session_cnt

from action_union au

drop table #sessions;
