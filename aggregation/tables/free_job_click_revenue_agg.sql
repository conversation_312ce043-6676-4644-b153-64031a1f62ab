-- DWH

delete from aggregation.free_job_click_revenue_agg
where is_complete_data = 0;

-- for each country it could be different value
$date_diff = (select max(session_datediff)
              from free_job_click_revenue_agg
              where is_complete_data = 1);

-- Replica
drop table if exists temp_session_jdp_potential_auth;
create temp table temp_session_jdp_potential_auth as
select distinct
       sj.date_diff as session_datediff,
       sj.id_session as session_id,
       sj.id as jdp_id,
       sj.uid_job,
       sj.date as jdp_date,
       max(sj.date) over (partition by sj.id_session, sj.id_account) as last_date_before_registration,
       case when sj.id_ref_action is null then 0 else 1 end as is_from_swipe,
       id_click as click_id,
       69 as source_id,
       sj.id_account as account_id
from session_jdp sj
join session_jdp_action sja
       on sj.date_diff = sja.date_diff and
          sj.id = sja.id_jdp and
          sja.type = 1 /*respond*/
where sj.flags & 4 = 0 and
      sj.date_diff > $date_diff
      --and sj.id_account is null
union all

-- new apply source
select distinct
       sj.date_diff as session_datediff,
       sj.id_session as session_id,
       sj.id as jdp_id,
       sj.uid_job,
       sj.date as jdp_date,
       max(sj.date) over (partition by sj.id_session, sj.id_account) as last_date_before_registration,
       case when sj.id_ref_action is null then 0 else 1 end as is_from_swipe,
       id_click as click_id,
       45 as source_id,
       sj.id_account as account_id
from session_jdp sj
join session_jdp_action sja
       on sj.date_diff = sja.date_diff and
          sj.id = sja.id_jdp and
          sja.type = 1 /*respond*/
where sj.flags & 4 = 4 and
      sj.date_diff  > $date_diff
      --and sj.id_account is null
;

drop table if exists temp_job_account;
create temp table temp_job_account as
with    cte_session_account as (
select eai.date_diff as session_datediff,
       eai.id_session as session_id,
       max(eai.id_account) as account_id,
       a.source as source_id
from link_dbo.email_account_interactions eai
join link_dbo.account a
  on a.id = eai.id_account
where eai.interaction_type = 0
 and source in (45,69)
 and eai.date_diff  > $date_diff
group by eai.date_diff,
       eai.id_session,
       a.source
)


select sjpa.session_datediff,
       sjpa.session_id,
       sjpa.source_id,
       sjpa.uid_job,
       sa.account_id,
       is_from_swipe,
       click_id,
       jdp_id
from temp_session_jdp_potential_auth sjpa
join cte_session_account sa
  on sa.session_datediff = sjpa.session_datediff and
     sa.session_id = sjpa.session_id and
     sa.source_id = sjpa.source_id
where jdp_date = last_date_before_registration
 and sjpa.account_id is null
;

drop table if exists session_0$_click;
create temp table session_0$_click as
select sc.date_diff as session_datediff,
       sc.id_session as session_id,
       sc.uid_job,
       sc.id as click_id,
       sj.id as jdp_id,
       case when sj.id_account is null then 0 else 1 end as has_account,
       case when sj.id_ref_action is null then 0 else 1 end as is_from_swipe,
       case when sj.flags & 4 = 4 then 45 else 69 end as source_id,
       case
           when sc.id_recommend is not null
               then 2 /*recommendation*/
           when sc.id_alertview is not null
               then 3 /*alertview*/
           when sc.id_search is not null
               then 4 /*search*/
           when s.session_create_page_type = 4 then 7 /*directly to jdp*/
           else 0 /*other*/
           end as placement_id,
       sign(s.flags&16) as is_mobile

from session_click sc
join session_jdp sj
      on sj.date_diff = sc.date_diff and
         sj.id_click = sc.id
join session s
 on s.date_diff = sc.date_diff and
    s.id = sc.id_session
where sc.date_diff  > $date_diff
  and sc.click_price = 0
  --and sc.id_search is not null
;



drop table if exists uid_job_revenue;
create temporary table uid_job_revenue as
select ja.session_id,
       ja.session_datediff,
       ja.click_id,
       ja.jdp_id,
       ja.is_from_swipe,
       ja.uid_job,
       ja.account_id,
       ja.source_id,
       max((ac.verify_date::date - '1900-01-01'::date)) as verify_datediff,
       max(case when (ac.verify_date::date - '1900-01-01'::date) <= ja.session_datediff+21 then 1 else 0 end) as is_verified,
       sum(ar.email_revenue) as verified_email_revenue,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) and (ac.verify_date::date - '1900-01-01'::date)+3 then ar.email_revenue end) as verified_email_revenue_0_3,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+4 and (ac.verify_date::date - '1900-01-01'::date)+7 then ar.email_revenue end) as verified_email_revenue_4_7,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+8 and (ac.verify_date::date - '1900-01-01'::date)+14 then ar.email_revenue end) as verified_email_revenue_8_14,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+15 and (ac.verify_date::date - '1900-01-01'::date)+21 then ar.email_revenue end) as verified_email_revenue_15_21,

       sum(case when ar.date_diff >= (ac.verify_date::date - '1900-01-01'::date)+1 then ar.total_revenue end) as verified_total_revenue,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+1 and (ac.verify_date::date - '1900-01-01'::date)+3 then ar.total_revenue end) as verified_total_revenue_0_3,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+4 and (ac.verify_date::date - '1900-01-01'::date)+7 then ar.total_revenue end) as verified_total_revenue_4_7,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+8 and (ac.verify_date::date - '1900-01-01'::date)+14 then ar.total_revenue end) as verified_total_revenue_8_14,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+15 and (ac.verify_date::date - '1900-01-01'::date)+21 then ar.total_revenue end) as verified_total_revenue_15_21,

       sum(case when ar.date_diff >= (ac.verify_date::date - '1900-01-01'::date)+1 then ar.away_clicks_premium end) as verified_away_clicks_premium,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+1 and (ac.verify_date::date - '1900-01-01'::date)+3 then ar.away_clicks_premium end) as verified_away_clicks_premium_0_3,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+4 and (ac.verify_date::date - '1900-01-01'::date)+7 then ar.away_clicks_premium end) as verified_away_clicks_premium_4_7,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+8 and (ac.verify_date::date - '1900-01-01'::date)+14 then ar.away_clicks_premium end) as verified_away_clicks_premium_8_14,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+15 and (ac.verify_date::date - '1900-01-01'::date)+21 then ar.away_clicks_premium end) as verified_away_clicks_premium_15_21,

       sum(case when ar.date_diff >= (ac.verify_date::date - '1900-01-01'::date)+1 then ar.away_clicks_free end) as verified_clicks_free,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+1 and (ac.verify_date::date - '1900-01-01'::date)+3 then ar.away_clicks_free end) as verified_away_clicks_free_0_3,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+4 and (ac.verify_date::date - '1900-01-01'::date)+7 then ar.away_clicks_free end) as verified_away_clicks_free_4_7,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+8 and (ac.verify_date::date - '1900-01-01'::date)+14 then ar.away_clicks_free end) as verified_away_clicks_free_8_14,
       sum(case when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date)+15 and (ac.verify_date::date - '1900-01-01'::date)+21 then ar.away_clicks_free end) as verified_away_clicks_free_15_21

from temp_job_account ja
left join link_dbo.account_contact ac
      on ja.account_id = ac.id_account
left join link_dbo.account_revenue ar
      on ar.id_account = ac.id_account and
         ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) and (ac.verify_date::date - '1900-01-01'::date)+21
group by ja.session_id,
       ja.session_datediff,
       ja.click_id,
       ja.is_from_swipe,
       ja.uid_job,
       ja.account_id,
       ja.source_id,
       ja.jdp_id;

-- кількість верифікованих, які відкриили хоча б 1 лист
drop table if exists temp_account_email_open;
create temp table temp_account_email_open as
select id_account,
       count(distinct case when letter_type = 1 then eo.id_message end) as letter_1_open_cnt_0_14,
       count(distinct case when letter_type = 1 and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff +3 then eo.id_message end) as letter_1_open_cnt_0_3,
       count(distinct case when letter_type = 1 and eo.date_diff between ujr.verify_datediff+4 and ujr.verify_datediff +7 then eo.id_message end) as letter_1_open_cnt_4_7,
       count(distinct case when letter_type = 1 and eo.date_diff between ujr.verify_datediff+8 and ujr.verify_datediff +14 then eo.id_message end) as letter_1_open_cnt_8_14,

       count(distinct case when letter_type = 8 then eo.id_message end) as letter_8_open_cnt_0_14,
       count(distinct case when letter_type = 8 and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff +3 then eo.id_message end) as letter_8_open_cnt_0_3,
       count(distinct case when letter_type = 8 and eo.date_diff between ujr.verify_datediff+4 and ujr.verify_datediff +7 then eo.id_message end) as letter_8_open_cnt_4_7,
       count(distinct case when letter_type = 8 and eo.date_diff between ujr.verify_datediff+8 and ujr.verify_datediff +14 then eo.id_message end) as letter_8_open_cnt_8_14,

       count(distinct case when letter_type = 71 then eo.id_message end) as letter_71_open_cnt_0_14,
       count(distinct case when letter_type = 71 and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff +3 then eo.id_message end) as letter_71_open_cnt_0_3,
       count(distinct case when letter_type = 71 and eo.date_diff between ujr.verify_datediff+4 and ujr.verify_datediff +7 then eo.id_message end) as letter_71_open_cnt_4_7,
       count(distinct case when letter_type = 71 and eo.date_diff between ujr.verify_datediff+8 and ujr.verify_datediff +14 then eo.id_message end) as letter_71_open_cnt_8_14
from uid_job_revenue ujr
left join link_dbo.email_open eo
 on eo.id_account = ujr.account_id
and eo.letter_type in (1,71,8)
and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff +14
where ujr.is_verified = 1
group by id_account
;

-- кількість верифікованих, які зробили хоча б 1 клік з імейлу
drop table if exists temp_account_email_visit;
create temp table temp_account_email_visit as
select id_account,
       count(distinct case when letter_type = 1 then ev.id_message end) as letter_1_interact_cnt_0_14,
       count(distinct case when letter_type = 1 and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff +3 then ev.id_message end) as letter_1_interact_cnt_0_3,
       count(distinct case when letter_type = 1 and ev.date_diff between ujr.verify_datediff+4 and ujr.verify_datediff +7 then ev.id_message end) as letter_1_interact_cnt_4_7,
       count(distinct case when letter_type = 1 and ev.date_diff between ujr.verify_datediff+8 and ujr.verify_datediff +14 then ev.id_message end) as letter_1_interact_cnt_8_14,

       count(distinct case when letter_type = 8 then ev.id_message end) as letter_8_interact_cnt_0_14,
       count(distinct case when letter_type = 8 and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff +3 then ev.id_message end) as letter_8_interact_cnt_0_3,
       count(distinct case when letter_type = 8 and ev.date_diff between ujr.verify_datediff+4 and ujr.verify_datediff +7 then ev.id_message end) as letter_8_interact_cnt_4_7,
       count(distinct case when letter_type = 8 and ev.date_diff between ujr.verify_datediff+8 and ujr.verify_datediff +14 then ev.id_message end) as letter_8_interact_cnt_8_14,

       count(distinct case when letter_type = 71 then ev.id_message end) as letter_71_interact_cnt_0_14,
       count(distinct case when letter_type = 71 and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff +3 then ev.id_message end) as letter_71_interact_cnt_0_3,
       count(distinct case when letter_type = 71 and ev.date_diff between ujr.verify_datediff+4 and ujr.verify_datediff +7 then ev.id_message end) as letter_71_interact_cnt_4_7,
       count(distinct case when letter_type = 71 and ev.date_diff between ujr.verify_datediff+8 and ujr.verify_datediff +14 then ev.id_message end) as letter_71_interact_cnt_8_14
from uid_job_revenue ujr
left join link_dbo.email_visit ev
 on ev.id_account = ujr.account_id
and ev.letter_type in (1,71,8)
and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff +14
where ujr.is_verified = 1
group by id_account;


drop table if exists temp_session_0$_click_revenue;
create temporary table temp_session_0$_click_revenue as
select $country_id as country_id,
       sc.session_datediff,
       sc.session_id,
       sc.click_id,
       sc.jdp_id,
       sc.has_account,
       sc.is_from_swipe,
       sc.uid_job,
       ujr.account_id,
       sc.source_id,
       sc.placement_id,
       sc.is_mobile,
       sjpa.jdp_id as jdp_id_with_respond,
       ujr.is_verified,

       ujr.verified_email_revenue,
       ujr.verified_email_revenue_0_3,
       ujr.verified_email_revenue_4_7,
       ujr.verified_email_revenue_8_14,
       ujr.verified_email_revenue_15_21,

       ujr.verified_total_revenue,
       ujr.verified_total_revenue_0_3,
       ujr.verified_total_revenue_4_7,
       ujr.verified_total_revenue_8_14,
       ujr.verified_total_revenue_15_21,

       ujr.verified_away_clicks_premium,
       ujr.verified_away_clicks_premium_0_3,
       ujr.verified_away_clicks_premium_4_7,
       ujr.verified_away_clicks_premium_8_14,
       ujr.verified_away_clicks_premium_15_21,

       ujr.verified_clicks_free,
       ujr.verified_away_clicks_free_0_3,
       ujr.verified_away_clicks_free_4_7,
       ujr.verified_away_clicks_free_8_14,
       ujr.verified_away_clicks_free_15_21,

       letter_1_open_cnt_0_14,
       letter_1_open_cnt_0_3,
       letter_1_open_cnt_4_7,
       letter_1_open_cnt_8_14,

       letter_8_open_cnt_0_14,
       letter_8_open_cnt_0_3,
       letter_8_open_cnt_4_7,
       letter_8_open_cnt_8_14,

       letter_71_open_cnt_0_14,
       letter_71_open_cnt_0_3,
       letter_71_open_cnt_4_7,
       letter_71_open_cnt_8_14,

       letter_1_interact_cnt_0_14,
       letter_1_interact_cnt_0_3,
       letter_1_interact_cnt_4_7,
       letter_1_interact_cnt_8_14,

       letter_8_interact_cnt_0_14,
       letter_8_interact_cnt_0_3,
       letter_8_interact_cnt_4_7,
       letter_8_interact_cnt_8_14,

       letter_71_interact_cnt_0_14,
       letter_71_interact_cnt_0_3,
       letter_71_interact_cnt_4_7,
       letter_71_interact_cnt_8_14,

       now() as update_datetime
from session_0$_click sc
left join temp_session_jdp_potential_auth sjpa
      on sjpa.session_datediff = sc.session_datediff and
         sjpa.jdp_id = sc.jdp_id
left join uid_job_revenue ujr
        on sc.session_datediff = ujr.session_datediff and
           sc.jdp_id = ujr.jdp_id
left join temp_account_email_open aeo
       on aeo.id_account = ujr.account_id
left join temp_account_email_visit aev
      on aev.id_account = ujr.account_id
;      

select country_id                                                   as country_id,
       update_datetime,
       session_datediff,
       case when session_datediff <= fn_get_date_diff(update_datetime)-22 then 1 else 0 end::smallint as is_complete_data,
       is_mobile,
       placement_id,
       has_account,
       source_id,
       is_from_swipe,
       count(distinct click_id)                                      as free_job_click_cnt,
       count(distinct jdp_id)                                        as jdp_cnt,
       count(distinct jdp_id_with_respond)                           as jdp_with_respond_cnt,
       count(distinct case when jdp_id_with_respond is not null then session_id end)   as session_with_at_least_1_respond_cnt,

       count(distinct account_id)                                    as new_account_cnt,
       count(distinct case when is_verified = 1 then account_id end) as new_verified_account_cnt,
       count(distinct case
                          when is_verified = 1 and
                               coalesce(letter_1_open_cnt_0_3, 0) + coalesce(letter_8_open_cnt_0_3, 0) +
                               coalesce(letter_71_open_cnt_0_3, 0) + coalesce(letter_1_open_cnt_4_7, 0) +
                               coalesce(letter_8_open_cnt_4_7, 0) + coalesce(letter_71_open_cnt_4_7, 0) >= 1
                              then account_id end)                   as account_with_at_least_1_open_letter_cnt_0_7,
       count(distinct case
                          when is_verified = 1 and
                               coalesce(letter_1_open_cnt_0_3, 0) + coalesce(letter_8_open_cnt_0_3, 0) +
                               coalesce(letter_71_open_cnt_0_3, 0) + coalesce(letter_1_open_cnt_4_7, 0) +
                               coalesce(letter_8_open_cnt_4_7, 0) + coalesce(letter_71_open_cnt_4_7, 0) >= 2
                              then account_id end)                   as account_with_at_least_2_open_letter_cnt_0_7,

       count(distinct case
                          when is_verified = 1 and
                               coalesce(letter_1_interact_cnt_0_3, 0) + coalesce(letter_8_interact_cnt_0_3, 0) +
                               coalesce(letter_71_interact_cnt_0_3, 0) + coalesce(letter_1_interact_cnt_4_7, 0) +
                               coalesce(letter_8_interact_cnt_4_7, 0) + coalesce(letter_71_interact_cnt_4_7, 0) >= 1
                              then account_id end)                   as account_with_at_least_1_interact_letter_cnt_0_7,
       count(distinct case
                          when is_verified = 1 and
                               coalesce(letter_1_interact_cnt_0_3, 0) + coalesce(letter_8_interact_cnt_0_3, 0) +
                               coalesce(letter_71_interact_cnt_0_3, 0) + coalesce(letter_1_interact_cnt_4_7, 0) +
                               coalesce(letter_8_interact_cnt_4_7, 0) + coalesce(letter_71_interact_cnt_4_7, 0) >= 2
                              then account_id end)                   as account_with_at_least_2_interact_letter_cnt_0_7,

       sum(verified_email_revenue)                                   as verified_email_revenue,
       sum(verified_email_revenue_0_3)                               as verified_email_revenue_0_3,
       sum(verified_email_revenue_4_7)                               as verified_email_revenue_4_7,
       sum(verified_email_revenue_8_14)                              as verified_email_revenue_8_14,
       sum(verified_email_revenue_15_21)                             as verified_email_revenue_15_21,
       sum(verified_total_revenue)                                   as verified_total_revenue,
       sum(verified_total_revenue_0_3)                               as verified_total_revenue_0_3,
       sum(verified_total_revenue_4_7)                               as verified_total_revenue_4_7,
       sum(verified_total_revenue_8_14)                              as verified_total_revenue_8_14,
       sum(verified_total_revenue_15_21)                             as verified_total_revenue_15_21,
       sum(verified_away_clicks_premium)                             as verified_away_clicks_premium,
       sum(verified_away_clicks_premium_0_3)                         as verified_away_clicks_premium_0_3,
       sum(verified_away_clicks_premium_4_7)                         as verified_away_clicks_premium_4_7,
       sum(verified_away_clicks_premium_8_14)                        as verified_away_clicks_premium_8_14,
       sum(verified_away_clicks_premium_15_21)                       as verified_away_clicks_premium_15_21,
       sum(verified_clicks_free)                                     as verified_clicks_free,
       sum(verified_away_clicks_free_0_3)                            as verified_away_clicks_free_0_3,
       sum(verified_away_clicks_free_4_7)                            as verified_away_clicks_free_4_7,
       sum(verified_away_clicks_free_8_14)                           as verified_away_clicks_free_8_14,
       sum(verified_away_clicks_free_15_21)                          as verified_away_clicks_free_15_21,
       sum(letter_1_open_cnt_0_14)                                   as letter_1_open_cnt_0_14,
       sum(letter_1_open_cnt_0_3)                                    as letter_1_open_cnt_0_3,
       sum(letter_1_open_cnt_4_7)                                    as letter_1_open_cnt_4_7,
       sum(letter_1_open_cnt_8_14)                                   as letter_1_open_cnt_8_14,
       sum(letter_8_open_cnt_0_14)                                   as letter_8_open_cnt_0_14,
       sum(letter_8_open_cnt_0_3)                                    as letter_8_open_cnt_0_3,
       sum(letter_8_open_cnt_4_7)                                    as letter_8_open_cnt_4_7,
       sum(letter_8_open_cnt_8_14)                                   as letter_8_open_cnt_8_14,
       sum(letter_71_open_cnt_0_14)                                  as letter_71_open_cnt_0_14,
       sum(letter_71_open_cnt_0_3)                                   as letter_71_open_cnt_0_3,
       sum(letter_71_open_cnt_4_7)                                   as letter_71_open_cnt_4_7,
       sum(letter_71_open_cnt_8_14)                                  as letter_71_open_cnt_8_14,
       sum(letter_1_interact_cnt_0_14)                               as letter_1_interact_cnt_0_14,
       sum(letter_1_interact_cnt_0_3)                                as letter_1_interact_cnt_0_3,
       sum(letter_1_interact_cnt_4_7)                                as letter_1_interact_cnt_4_7,
       sum(letter_1_interact_cnt_8_14)                               as letter_1_interact_cnt_8_14,
       sum(letter_8_interact_cnt_0_14)                               as letter_8_interact_cnt_0_14,
       sum(letter_8_interact_cnt_0_3)                                as letter_8_interact_cnt_0_3,
       sum(letter_8_interact_cnt_4_7)                                as letter_8_interact_cnt_4_7,
       sum(letter_8_interact_cnt_8_14)                               as letter_8_interact_cnt_8_14,
       sum(letter_71_interact_cnt_0_14)                              as letter_71_interact_cnt_0_14,
       sum(letter_71_interact_cnt_0_3)                               as letter_71_interact_cnt_0_3,
       sum(letter_71_interact_cnt_4_7)                               as letter_71_interact_cnt_4_7,
       sum(letter_71_interact_cnt_8_14)                              as letter_71_interact_cnt_8_14,

       count(distinct case
                          when is_verified = 1 and
                               coalesce(verified_email_revenue_0_3, 0) + coalesce(verified_email_revenue_4_7, 0) > 0
                              then account_id end)                   as account_with_verified_email_revenue_cnt_0_7


from temp_session_0$_click_revenue s
group by country_id,
         update_datetime,
         session_datediff,
         is_mobile,
         placement_id,
         has_account,
         source_id,
         is_from_swipe
;
