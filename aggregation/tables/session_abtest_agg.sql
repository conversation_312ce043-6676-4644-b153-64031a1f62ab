            create temp table temp_account as
            select *
            from link_dbo.account;
            analyse temp_account;

            create temp table temp_account_contact as
            select *
            from link_dbo.account_contact;
            analyse temp_account_contact;

            create temp table temp_account_revenue as
            select *
            from an.account_revenue;
            analyse temp_account_revenue;

            create temp table temp_info_project as
            select *
            from link_dbo.info_project;
            create index idx_temp_info_project on temp_info_project(id);
            analyse temp_info_project;


            create temp table temp_campaign as
            select *
            from link_auction.campaign;
            create index idx_temp_campaign on temp_campaign(id);
            analyse temp_campaign;

            create temp table temp_info_currency as
            select *
            from link_dbo.info_currency;
            create index idx_temp_info_currency on temp_info_currency(id);
            analyse temp_info_currency;

            create temp table temp_site as
            select *
            from link_auction.site;
            create index idx_temp_site01 on temp_site(id);
            create index idx_temp_site02 on temp_site(id_user);
            analyse temp_site;

            create temp table temp_user as
            select *
            from link_auction."user";
            create index idx_temp_user01 on temp_user(id);
            create index idx_temp_user02 on temp_user(flags);
            analyse temp_user;

            ---------------
           create temp table session_external_tmp as
           select *
           from public.session_external
           where date_diff = _date_diff;
           create index idx_tmp_session_external1 on session_external_tmp(date_diff);
           create index idx_tmp_session_external2 on session_external_tmp(id_away);
           create index idx_tmp_session_external3 on session_external_tmp(id_jdp);
           analyse session_external_tmp;

           create temp table session_click_tmp_0 as
           select *
           from public.session_click sc
           where date_diff = _date_diff;
           create index idx_tmp_session_click01 on session_click_tmp_0(date_diff);
           create index idx_tmp_session_click02 on session_click_tmp_0(id_session);
           create index idx_tmp_session_click03 on session_click_tmp_0(id);
           create index idx_tmp_session_click04 on session_click_tmp_0(id_currency);
           create index idx_tmp_session_click05 on session_click_tmp_0(id_campaign);
           analyse session_click_tmp_0;

           create temp table session_away_tmp_0 as
           select *
           from public.session_away sa
           where date_diff = _date_diff;
           create index idx_tmp_session_away00 on session_away_tmp_0(id_campaign);
           create index idx_tmp_session_away01 on session_away_tmp_0(date_diff);
           create index idx_tmp_session_away02 on session_away_tmp_0(id);
           create index idx_tmp_session_away03 on session_away_tmp_0(flags);
           analyse session_away_tmp_0;

           create temp table session_click_no_serp_tmp_0 as
           select *
           from public.session_click_no_serp scns
           where date_diff = _date_diff;
           create index idx_tmp_session_click_no_serp_tmp_01 on session_click_no_serp_tmp_0(date_diff);
           create index idx_tmp_session_click_no_serp_tmp_02 on session_click_no_serp_tmp_0(id_session);
           create index idx_tmp_session_click_no_serp_tmp_03 on session_click_no_serp_tmp_0(id);
           create index idx_tmp_session_click_no_serp_tmp_04 on session_click_no_serp_tmp_0(id_currency);
           create index idx_tmp_session_click_no_serp_tmp_05 on session_click_no_serp_tmp_0(id_campaign);
           analyse session_click_no_serp_tmp_0;


           create temp table session_jdp_tmp as
           select *
           from public.session_jdp sj
           where date_diff = _date_diff;
           create index idx_session_jdp_tmp on session_jdp_tmp(id);
           analyse session_jdp_tmp;
   
           create temp table tmp_distinct_conversion_away_connection as
           select distinct id_session_away
           from link_auction.conversion_away_connection;

            ---------------




        create temp table session_test_agg_tmp as
              select id_session,
              session_test.date_diff,
              array_to_string(array_agg(id_test),' ') as id_tests,
              array_to_string(array_agg(concat((id_test),'-',session_test."group",'_',(iteration))), ' ') as groups
       from public.session_test
              where date_diff = _date_diff
                     and iteration >= 0
       group by id_session,
                session_test.date_diff;

       analyse session_test_agg_tmp;



        create temp table test_sessions as
        select st.groups                               as groups,
               s.id                                    as id_session,
               s.date_diff                             as date_diff,
               case
                   when s.flags & 2 = 2 then 1
                   else 0
                   end                                 as is_returned,
               case
                   when s.flags & 64 = 64 then 2 /*mobile app*/
                   when s.flags & 16 = 16 then 1 /*mobile web*/
                   else 0 /*desktop web*/
                   end                                 as device_type_id,
               case
                   when s.ip_cc = _country_code
                       or s.ip_cc = 'gb' and _country_code = 'uk'
                       then 1
                   else 0 end                          as is_local,
               coalesce(s.session_create_page_type, 0) as session_create_page_type,
               s.id_traf_source                        as id_traffic_source,
               s.id_current_traf_source                as id_current_traffic_source,
               s.cookie_label                          as cookie_label,
               s.start_date
        from public.session s
                 left join session_test_agg_tmp st on st.date_diff = s.date_diff
            and s.id = st.id_session
        where s.flags & 1 = 0
          and s.date_diff = _date_diff
          and s.user_agent_hash64 != 5981440342270357754;


        create index idx_test_sessions on test_sessions(date_diff, id_session);
        analyse test_sessions;


            -- START #1
           create temp table gather_revenue_tmp as
           select s.groups,
              s.date_diff,
              s.is_returned,
              s.device_type_id,
              s.is_local,
              s.session_create_page_type,
              s.id_traffic_source,
              s.id_session,
              sa.click_price * ic.value_to_usd::numeric          click_price_usd,
              coalesce(sa.letter_type, sj.letter_type)    letter_type,
              coalesce(sc.id_recommend, scj.id_recommend) id_recommend,
              coalesce(sc.id_alertview, scj.id_alertview) id_alertview,
              coalesce(sc.id_search, scj.id_search)       id_search,
              ext.id                                    id_external,
              cs.conversion_start
             from session_away_tmp_0 sa
                     inner join test_sessions s   on sa.date_diff = s.date_diff and sa.id_session = s.id_session
                     inner join temp_info_currency ic  on ic.id = sa.id_currency
                     left join temp_campaign ac on ac.id = sa.id_campaign
                     left join temp_site ast   on ac.id_site = ast.id
                     left join temp_user au   on au.id = ast.id_user
                     left join temp_info_project ip on ip.id = sa.id_project
                     left join session_click_tmp_0 sc   on sc.date_diff = sa.date_diff and sc.id = sa.id_click
                     left join session_jdp_tmp sj   on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
                     left join session_click_tmp_0 scj   on scj.date_diff = sj.date_diff and scj.id = sj.id_click
                     left join session_external_tmp ext   on ext.date_diff = sa.date_diff and ext.id_away = sa.id
                     left join (
                               select  id_project,
                                       min(cac.date_diff) as conversion_start
                               from link_auction.conversion_away_connection cac
                                     join public.session_away sa
                                on cac.date_diff  = sa.date_diff
                                and cac.id_session_away = sa.id
                                group by id_project
                               ) cs on sa.id_project = cs.id_project
               where sa.date_diff = _date_diff
                      and (sa.id_campaign = 0 or au.flags & 2 = 0)
                      and coalesce(sa.flags, 0) & 2 = 0
                      and sa.flags & 512 = 0
                      and coalesce(lower(ip.name), '') not like 'j-vers.%';


                analyze gather_revenue_tmp;

                analyse test_sessions;
                analyse temp_info_currency;
                analyse temp_campaign;
                analyse temp_site;
                analyse temp_user;
                analyse session_away_tmp_0;
                analyse session_jdp_tmp;
                analyse session_external_tmp;
--                 analyse tmp_cs;


               -- STEP #2
               insert into gather_revenue_tmp
               select s.groups,
                      s.date_diff,
                      s.is_returned,
                      s.device_type_id,
                      s.is_local,
                      s.session_create_page_type,
                      s.id_traffic_source,
                      s.id_session,
                      sc.click_price * ic.value_to_usd::numeric       click_price_usd,
                      coalesce(sa.letter_type, sj.letter_type) letter_type,
                      sc.id_recommend,
                      sc.id_alertview,
                      sc.id_search,
                      ext.id                                 id_external,
                      cs.conversion_start
               from session_click_tmp_0 sc
                             inner join test_sessions s   on sc.date_diff = s.date_diff and sc.id_session = s.id_session
                             inner join temp_info_currency ic   on ic.id = sc.id_currency
                             left join temp_campaign ac   on ac.id = sc.id_campaign
                             left join temp_site ast   on ac.id_site = ast.id
                             left join temp_user au   on au.id = ast.id_user
                             left join temp_info_project ip on ip.id = sc.id_project
                             left join session_away_tmp_0 sa   on sc.date_diff = sa.date_diff and sc.id = sa.id_click
                             left join session_jdp_tmp sj   on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
                             left join session_external_tmp ext
                                    on ext.date_diff = sc.date_diff and (ext.id_away = sa.id or ext.id_jdp = sj.id)
                             left join (
                                       select  id_project,
                                               min(cac.date_diff) as conversion_start
                                       from link_auction.conversion_away_connection cac
                                             join public.session_away sa
                                        on cac.date_diff  = sa.date_diff
                                        and cac.id_session_away = sa.id
                                        group by id_project
                                       ) cs on sa.id_project = cs.id_project
               where sc.date_diff = _date_diff
                      and (sc.id_campaign = 0 or au.flags & 2 = 2)
                      and coalesce(sc.flags, 0) & 16 = 0
                      and sc.flags & 4096 = 0
                      and coalesce(lower(ip.name), '') not like 'j-vers.%';



               -- STEP #3
               insert into gather_revenue_tmp
               select s.groups,
                      s.date_diff,
                      s.is_returned,
                      s.device_type_id,
                      s.is_local,
                      s.session_create_page_type,
                      s.id_traffic_source,
                      s.id_session,
                      scns.click_price * ic.value_to_usd::numeric click_price_usd,
                      scns.letter_type,
                      scns.id_recommend,
                      null   as                          id_alertview,
                      null   as                          id_search,
                      ext.id as                          id_external,
                      cs.conversion_start
               from session_click_no_serp_tmp_0 scns
                             inner join test_sessions s   on scns.date_diff = s.date_diff and scns.id_session = s.id_session
                             inner join temp_info_currency ic   on ic.id = scns.id_currency
                             left join temp_campaign ac   on ac.id = scns.id_campaign
                             left join temp_site ast   on ac.id_site = ast.id
                             left join temp_user au   on au.id = ast.id_user
                             left join temp_info_project ip on ip.id = scns.id_project
                             left join session_away_tmp_0 sa   on scns.date_diff = sa.date_diff and scns.id = sa.id_click_no_serp
                             left join session_jdp_tmp sj   on scns.date_diff = sj.date_diff and scns.id = sj.id_click_no_serp
                             left join session_external_tmp ext
                                    on ext.date_diff = scns.date_diff and (ext.id_away = sa.id or ext.id_jdp = sj.id)
                             left join (
                                       select  id_project,
                                               min(cac.date_diff) as conversion_start
                                       from link_auction.conversion_away_connection cac
                                             join public.session_away sa
                                        on cac.date_diff  = sa.date_diff
                                        and cac.id_session_away = sa.id
                                        group by id_project
                                       ) cs on scns.id_project = cs.id_project
               where scns.date_diff = _date_diff
                      and au.flags & 2 = 2
                      and coalesce(scns.flags, 0) & 16 = 0
                      and scns.flags & 4096 = 0
                      and coalesce(lower(ip.name), '') not like 'j-vers.%';



       create temp table revenue_raw as
       select s1.click_price_usd,
              s1.id_session,
              s1.groups,
              s1.date_diff,
              s1.is_returned,
              s1.device_type_id,
              s1.is_local,
              s1.session_create_page_type,
              s1.id_traffic_source,
              case
              when s1.letter_type = 8 then 1
              when s1.id_recommend is not null then 2
              when s1.id_alertview is not null then 3
              when s1.id_search is not null then 4
              when s1.id_external is not null then 5
              else 6
              end                                                                             placement,
              case when s1.date_diff >= s1.conversion_start then s1.click_price_usd else 0 end as conv_click_price_usd
       from gather_revenue_tmp s1;

       analyse revenue_raw;


        create temp table revenue_session as
        select ts.id_session,
               sum(rr.click_price_usd)      as revenue_usd,
               sum(rr.conv_click_price_usd) as conv_click_price_usd
        from test_sessions ts
                 left join revenue_raw rr on rr.date_diff = ts.date_diff
                                        and rr.id_session = ts.id_session
        group by ts.id_session;

        analyse revenue_session;


        create temp table revenue_session_ranked as
        select id_session,
               revenue_usd,
               conv_click_price_usd,
               row_number() over (order by revenue_usd desc) rank,
               count(id_session) over () as                  cnt
        from revenue_session
        where revenue_usd > 0;

        analyse revenue_session_ranked;


        create temp table session_info as
        select t.id_session,
               groups,
               t.date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               cookie_label,
               start_date,
               case
                   when rank <= round(cnt * 0.05, 0) and r.revenue_usd > 0 then 1
                   else 0
                   end as is_anomalistic,
               coalesce(revenue_usd,0) as revenue_usd,
               coalesce(conv_click_price_usd,0) as conv_click_price_usd
        from test_sessions t
        left join revenue_session_ranked r on r.id_session = t.id_session;

        analyse session_info;

        analyse temp_account;
        analyse temp_account_contact;


        create temp table session_acc_info as
        select s.id_session,
               s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.cookie_label,
               s.is_anomalistic,
               sac.id_account,
               new_acc.id                                                                                                     as new_account_id,
               row_number()
                       over (partition by new_acc.id order by s.start_date - new_acc.date_add)                                as new_account_rank,
               account_contact.verify_date,
               new_alert.id                                                                                                   as new_alert_id,
               new_alert.id_account                                                                                           as new_alert_account_id,
               row_number()
                       over (partition by new_alert.id_account, new_alert.id order by new_alert.date_add - s.start_date)      as new_alert_rank
        from session_info s
        left join public.session_account sac  on sac.date_diff = s.date_diff and sac.id_session = s.id_session
        left join temp_account new_acc  on s.date_diff = (select new_acc.date_add::date - '1900-01-01')
                                                   and new_acc.id = sac.id_account
                                                   and s.start_date <= new_acc.date_add
        left join temp_account_contact account_contact  on account_contact.id_account = new_acc.id
        left join link_dbo.email_alert new_alert  on (select new_alert.date_add::date - '1900-01-01') = s.date_diff
                                                         and sac.id_account = new_alert.id_account
                                                         and s.start_date <= new_alert.date_add;

       analyse session_acc_info;


        create temp table session_acc_agg as
        select groups,
               session_acc_info.date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               is_anomalistic,
               count(distinct id_session)                                                                  as session_cnt,
               count(distinct cookie_label)                                                                as user_cnt,
               count(distinct id_session)               as session_account_cnt,
               count(distinct iif(new_account_rank = 1, new_account_id, null))                             as new_account_cnt,
               count(distinct iif(new_account_rank = 1 and verify_date is not null, new_account_id,
                                  null))                                                                   as new_verified_account_cnt,
               count(distinct iif(new_alert_rank = 1, new_alert_id, null))                                 as new_alert_cnt,
               count(distinct iif(new_alert_rank = 1, new_alert_account_id, null))                         as new_alert_account_cnt,
               count(distinct
                     iif(new_alert_rank = 1 and new_alert_id is not null, id_session, null))               as new_alert_session_cnt,
               sum(iif(new_account_rank = 1, account_revenue.total_revenue, null))                as new_account_revenue
        from session_acc_info
        left join temp_account_revenue account_revenue on session_acc_info.new_account_id = account_revenue.id_account
                                                  and account_revenue.date_diff = _date_diff
        group by groups,
                 session_acc_info.date_diff,
                 is_returned,
                 device_type_id,
                 is_local,
                 session_create_page_type,
                 id_traffic_source,
                 id_current_traffic_source,
                 is_anomalistic;

       analyse session_acc_agg;


        create temp table session_search_tmp as
        select date_diff,
               id,
               id_session
        from public.session_search ss
        where ss.date_diff = _date_diff;

        analyse session_search_tmp;


        create temp table session_alertview_tmp as
        select date_diff,
               id,
               id_session
        from public.session_alertview
        where date_diff = _date_diff;

        analyse session_alertview_tmp;


        create temp table session_filter_action_tmp as
        select date_diff,
               id,
               id_search_prev,
               action,
               id_search
        from public.session_filter_action sfa
        where date_diff = _date_diff;

        analyse session_filter_action_tmp;


        create temp table session_action_tmp as
        select date_diff,
               id,
               id_session,
               type
        from public.session_action sact
        where date_diff = _date_diff;

        analyse session_action_tmp;




        create temp table top_actions_agg as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct ss.id)                                           as search_cnt,
               count(distinct sav.id)                                          as alertview_cnt,
               count(distinct iif(sfa.action is not null, sfa.id, null))       as session_filter_action_cnt,
               count(distinct ss.id_session)                                   as search_session_cnt,
               count(distinct sav.id_session)                                  as alertview_session_cnt,
               count(distinct iif(sfa.action is not null, s.id_session, null)) as session_filter_action_session_cnt
        from session_info s
        left join session_search_tmp ss  on ss.date_diff = s.date_diff and ss.id_session = s.id_session
        left join session_alertview_tmp sav  on sav.date_diff = s.date_diff and sav.id_session = s.id_session
        left join session_filter_action_tmp sfa  on sfa.date_diff = ss.date_diff and sfa.id_search_prev = ss.id
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic;


        create temp table session_action_agg as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               cast(floor(sact.type / 100.0) as varchar) as action_type,
               count(distinct sact.id)                   as session_action_cnt,
               count(distinct s.id_session)              as session_action_session_cnt
        from session_info s
        join session_action_tmp sact  on sact.date_diff = s.date_diff
                                         and sact.id_session = s.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic,
                 floor(sact.type / 100.0)

        union

        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               cast(-1 as varchar)              as action_type,
               count(distinct sact.id)      as session_action_cnt,
               count(distinct s.id_session) as session_action_session_cnt
        from session_info s
        join session_action_tmp sact  on sact.date_diff = s.date_diff
                                         and sact.id_session = s.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic;



        create temp table filter_search_agg as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sfa.id_search) as session_filter_search_cnt,
               count(distinct s.id_session)  as session_filter_search_session_cnt
        from session_filter_action_tmp sfa
        join session_search_tmp ss_prev  on ss_prev.date_diff = sfa.date_diff
                                                   and ss_prev.id = sfa.id_search_prev
        join session_info s_prev  on s_prev.date_diff = ss_prev.date_diff
                                         and s_prev.id_session = ss_prev.id_session
        join session_search_tmp ss_curr  on ss_curr.date_diff = sfa.date_diff
                                                   and ss_curr.id = sfa.id_search
        join session_info s  on s.date_diff = ss_curr.date_diff
                                    and s.id_session = ss_curr.id_session
                                    and s_prev.id_session = s.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic;



        -- AWAYS BY PLACEMENT
        create temp table jdp_metrics_agg as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sj.id)                                       as jdp_cnt,
               count(distinct case when sj.flags & 4 = 4 then sj.id end )  as apply_jdp_cnt,
               count(distinct sj.id_session)                               as jdp_session_cnt,
               count(distinct sja.id)                                      as session_jdp_action_cnt,
               count(distinct s.id_session)                                as session_jdp_action_session_cnt,
               count(distinct sap.id)                                      as apply_cnt,
               count(distinct iif(sap.id is not null, s.id_session, null)) as apply_session_cnt
        from public.session_jdp sj
        join session_info s  on s.date_diff = sj.date_diff and s.id_session = sj.id_session
        left join public.session_jdp_action sja  on sj.date_diff = sja.date_diff and sj.id = sja.id_jdp
        left join public.session_apply sap  on sja.date_diff = sap.date_diff and sja.id = sap.id_src_jdp_action
        where sj.date_diff = _date_diff
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic;


        -- AWAY
        create temp table away_agg as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sa.id)                                                 as away_cnt,
               count(distinct sa.id_session)                                         as away_session_cnt,
               count(distinct sj.id)                                                 as jdp_away_cnt,
               count(distinct sj.id_session)                                         as jdp_away_session_cnt,
               count(distinct away_con.id_session_away)                              as conversion_cnt,
               count(distinct
                     iif(away_con.id_session_away is not null, sa.id_session, null)) as conversion_session_cnt,
               case
                   when coalesce(sa.letter_type, sj.letter_type) = 8
                       then 1
                   when coalesce(sc.id_recommend, scj.id_recommend) is not null
                       then 2
                   when coalesce(sc.id_alertview, scj.id_alertview) is not null
                       then 3
                   when coalesce(sc.id_search, scj.id_search) is not null
                       then 4
                   when ext.id is not null
                       then 5
                   else 6
                   end                                                               as placement,
                count(distinct case when sa.date_diff> cs.conversion_start then sa.id end )  as conversion_away_cnt
        from public.session_away sa
        join session_info s  on s.date_diff = sa.date_diff and s.id_session = sa.id_session
        left join public.session_click sc   on sc.date_diff = sa.date_diff
                                                          and sc.id = sa.id_click
        left join public.session_jdp sj   on sj.date_diff = sa.date_diff
                                                        and sj.id = sa.id_jdp
        left join public.session_click scj   on scj.date_diff = sj.date_diff
                                                           and scj.id = sj.id_click
        left join public.session_external ext   on ext.date_diff = sa.date_diff
                                                              and ext.id_away = sa.id
        left join tmp_distinct_conversion_away_connection away_con on away_con.id_session_away = sa.id
        left join temp_info_project ip on ip.id = sa.id_project
        left join (
                   select  id_project,
                           min(cac.date_diff) as conversion_start
                   from link_auction.conversion_away_connection cac
                         join public.session_away sa
                    on cac.date_diff  = sa.date_diff
                    and cac.id_session_away = sa.id
                    group by id_project
                   ) cs  on sa.id_project = cs.id_project
        where sa.date_diff = _date_diff
        and coalesce(sa.flags, 0) & 2 = 0    -- exclude test campaign aways
        and coalesce(sa.flags, 0) & 512 = 0  -- exclude duplicates
        and coalesce(lower(ip.name), '') not like 'j-vers.%'
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic,
                 case
                     when coalesce(sa.letter_type, sj.letter_type) = 8
                         then 1
                     when coalesce(sc.id_recommend, scj.id_recommend) is not null
                         then 2
                     when coalesce(sc.id_alertview, scj.id_alertview) is not null
                         then 3
                     when coalesce(sc.id_search, scj.id_search) is not null
                         then 4
                     when ext.id is not null
                         then 5
                     else 6
                     end

        union

        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sa.id)                                                 as away_cnt,
               count(distinct sa.id_session)                                         as away_session_cnt,
               count(distinct iif(sj.id is not null, sa.id, null))                   as jdp_away_cnt,
               count(distinct iif(sj.id is not null, sa.id_session, null))           as jdp_away_session_cnt,
               count(distinct away_con.id_session_away)                              as conversion_cnt,
               count(distinct
                     iif(away_con.id_session_away is not null, sa.id_session, null)) as conversion_session_cnt,
               7                                                                     as placement,
               count(distinct case when sa.date_diff> cs.conversion_start then sa.id end )  as conversion_away_cnt
        from public.session_away sa
        join session_info s  on s.date_diff = sa.date_diff and s.id_session = sa.id_session
        left join public.session_jdp sj   on sj.date_diff = sa.date_diff and sj.id = sa.id_jdp
        left join temp_info_project ip on ip.id = sa.id_project
        left join tmp_distinct_conversion_away_connection away_con on away_con.id_session_away = sa.id
        left join (
                   select  id_project,
                           min(cac.date_diff) as conversion_start
                   from link_auction.conversion_away_connection cac
                         join public.session_away sa
                    on cac.date_diff  = sa.date_diff
                    and cac.id_session_away = sa.id
                    group by id_project
                   ) cs on sa.id_project = cs.id_project
        where sa.date_diff = _date_diff
              and coalesce(sa.flags, 0) & 2 = 0    -- exclude test campaign aways
              and coalesce(sa.flags, 0) & 512 = 0  -- exclude duplicates
              and coalesce(lower(ip.name), '') not like 'j-vers.%'

        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic;



        --intention-to-contact table
        create temp table revenue as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               r.placement,
               s.is_anomalistic,
               sum(r.click_price_usd)                                         as revenue_usd,
               count(distinct iif(r.click_price_usd > 0, r.id_session, null)) as session_with_revenue_count,
               sum(r.conv_click_price_usd) as conv_revenue_usd
        from revenue_raw r
        join session_info s on s.id_session = r.id_session
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 r.placement,
                 s.is_anomalistic

        union

        select groups,
               date_diff,
               is_returned,
               device_type_id,
               is_local,
               session_create_page_type,
               id_traffic_source,
               id_current_traffic_source,
               7                                                      as placement,
               is_anomalistic,
               sum(revenue_usd)                                       as revenue_usd,
               count(distinct iif(revenue_usd > 0, id_session, null)) as session_with_revenue_count,
               sum(conv_click_price_usd) as conv_revenue_usd
        from session_info
        group by groups,
                 date_diff,
                 is_returned,
                 device_type_id,
                 is_local,
                 session_create_page_type,
                 id_traffic_source,
                 id_current_traffic_source,
                 is_anomalistic;


        create temp table intention_to_contact as
        select s.groups,
               s.date_diff,
               s.is_returned,
               s.device_type_id,
               s.is_local,
               s.session_create_page_type,
               s.id_traffic_source,
               s.id_current_traffic_source,
               s.is_anomalistic,
               count(distinct sc.id) *
               (
                SELECT m_itc_jdp_away_projects_for_ab.itc_per_view
                FROM dblink('dbname=postgres host=********* user= password= options=-csearch_path=',
                        format('SELECT itc_per_view
                         FROM company.m_itc_jdp_away_projects_for_ab as m_all_away
                         where lower(m_all_away.country) = %s', quote_literal(_country_code))) AS m_itc_jdp_away_projects_for_ab(itc_per_view numeric)
               )                                                                                          as intention_to_contact_from_serp_cnt,
               count(distinct case
                                  when sja.type in (1/*respond*/, 13 /*click_to_call*/, 19/*show_phone_number*/, 21/*view_employer_phone*/,
                                                    34/*view_multiply_employer_phone*/ )
                                      then sjd.id end)                                                    as intention_to_contact_from_jdp_cnt
        from session_info s
                 left join session_click sc
                           on sc.job_destination = 1 /*away*/ and
                              sc.id_session = s.id_session
                               and sc.date_diff = s.date_diff
                 left join session_jdp sjd
                           on sjd.date_diff = s.date_diff
                               and sjd.id_session = s.id_session
                 left join session_jdp_action sja
                           on sjd.date_diff = sja.date_diff
                               and sjd.id = sja.id_jdp
                               and sja.type in (1/*respond*/,
                                                13 /*click_to_call*/,
                                                19/*show_phone_number*/,
                                                21/*view_employer_phone*/,
                                                34/*view_multiply_employer_phone*/
                                   )
        where s.date_diff = _date_diff
        group by s.groups,
                 s.date_diff,
                 s.is_returned,
                 s.device_type_id,
                 s.is_local,
                 s.session_create_page_type,
                 s.id_traffic_source,
                 s.id_current_traffic_source,
                 s.is_anomalistic;







        -------- truncate
        delete from an.rpl_session_abtest_agg
        where action_datediff = _date_diff;
        -------- truncate



        insert into an.rpl_session_abtest_agg
        with final as (
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null          as attribute_name,
                   null::int          as attribute_value,
                   'session_cnt' as metric,
                   session_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                  as attribute_name,
                   null::int                  as attribute_value,
                   'session_account_cnt' as metric,
                   session_account_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null              as attribute_name,
                   null::int              as attribute_value,
                   'new_account_cnt' as metric,
                   new_account_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null              as attribute_name,
                   null::int              as attribute_value,
                   'new_account_revenue' as metric,
                   new_account_revenue::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                       as attribute_name,
                   null::int                       as attribute_value,
                   'new_verified_account_cnt' as metric,
                   new_verified_account_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null            as attribute_name,
                   null::int            as attribute_value,
                   'new_alert_cnt' as metric,
                   new_alert_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                    as attribute_name,
                   null::int                    as attribute_value,
                   'new_alert_session_cnt' as metric,
                   new_alert_session_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                    as attribute_name,
                   null::int                    as attribute_value,
                   'new_alert_account_cnt' as metric,
                   new_alert_account_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null       as attribute_name,
                   null::int       as attribute_value,
                   'user_cnt' as metric,
                   user_cnt::numeric   as value
            from session_acc_agg session_acc_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null         as attribute_name,
                   null::int         as attribute_value,
                   'search_cnt' as metric,
                   search_cnt::numeric   as value
            from top_actions_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                 as attribute_name,
                   null::int                 as attribute_value,
                   'search_session_cnt' as metric,
                   search_session_cnt::numeric   as value
            from top_actions_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null            as attribute_name,
                   null::int            as attribute_value,
                   'alertview_cnt' as metric,
                   alertview_cnt::numeric   as value
            from top_actions_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                    as attribute_name,
                   null::int                    as attribute_value,
                   'alertview_session_cnt' as metric,
                   alertview_session_cnt::numeric   as value
            from top_actions_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'action type'        as attribute_name,
                   action_type::int          as attribute_value,
                   'session_action_cnt' as metric,
                   session_action_cnt::numeric   as value
            from session_action_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'action type'                as attribute_name,
                   action_type::int                  as attribute_value,
                   'session_action_session_cnt' as metric,
                   session_action_session_cnt::numeric   as value
            from session_action_agg
            ------------------------
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                        as attribute_name,
                   null::int                        as attribute_value,
                   'session_filter_action_cnt' as metric,
                   session_filter_action_cnt::numeric   as value
            from top_actions_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                                as attribute_name,
                   null::int                                as attribute_value,
                   'session_filter_action_session_cnt' as metric,
                   session_filter_action_session_cnt::numeric   as value
            from top_actions_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                        as attribute_name,
                   null::int                        as attribute_value,
                   'session_filter_search_cnt' as metric,
                   session_filter_search_cnt::numeric   as value
            from filter_search_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                                as attribute_name,
                   null::int                                as attribute_value,
                   'session_filter_search_session_cnt' as metric,
                   session_filter_search_session_cnt::numeric   as value
            from filter_search_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null      as attribute_name,
                   null::int      as attribute_value,
                   'jdp_cnt' as metric,
                   jdp_cnt::numeric   as value
            from jdp_metrics_agg
            union
              select groups::varchar,
                     date_diff,
                     is_returned,
                     device_type_id,
                     is_local::int,
                     session_create_page_type::int,
                     id_traffic_source,
                     id_current_traffic_source,
                     is_anomalistic,
                     null      as attribute_name,
                     null      as attribute_value,
                     'apply_jdp_cnt' as metric,
                     apply_jdp_cnt::numeric   as value
              from jdp_metrics_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null              as attribute_name,
                   null::int              as attribute_value,
                   'jdp_session_cnt' as metric,
                   jdp_session_cnt::numeric   as value
            from jdp_metrics_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null        as attribute_name,
                   null::int        as attribute_value,
                   'apply_cnt' as metric,
                   apply_cnt::numeric   as value
            from jdp_metrics_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                as attribute_name,
                   null::int                as attribute_value,
                   'apply_session_cnt' as metric,
                   apply_session_cnt::numeric   as value
            from jdp_metrics_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                     as attribute_name,
                   null::int                     as attribute_value,
                   'session_jdp_action_cnt' as metric,
                   session_jdp_action_cnt::numeric   as value
            from jdp_metrics_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                             as attribute_name,
                   null::int                             as attribute_value,
                   'session_jdp_action_session_cnt' as metric,
                   session_jdp_action_session_cnt::numeric   as value
            from jdp_metrics_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement' as attribute_name,
                   placement::int   as attribute_value,
                   'away_cnt'  as metric,
                   away_cnt::numeric    as value
            from away_agg
            -------------
            union
            select groups::varchar,
                   date_diff::int,
                   is_returned::int,
                   device_type_id::int,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source::int,
                   id_current_traffic_source::int,
                   is_anomalistic::int,
                   'placement'        as attribute_name,
                   placement::int          as attribute_value,
                   'away_session_cnt' as metric,
                   away_session_cnt::int   as value
            from away_agg
            ----------------------
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'    as attribute_name,
                   placement::int      as attribute_value,
                   'jdp_away_cnt' as metric,
                   jdp_away_cnt::numeric   as value
            from away_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'            as attribute_name,
                   placement::int              as attribute_value,
                   'jdp_away_session_cnt' as metric,
                   jdp_away_session_cnt::numeric   as value
            from away_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'      as attribute_name,
                   placement::int        as attribute_value,
                   'conversion_cnt' as metric,
                   conversion_cnt::numeric   as value
            from away_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'              as attribute_name,
                   placement::int                as attribute_value,
                   'conversion_session_cnt' as metric,
                   conversion_session_cnt::numeric   as value
            from away_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'              as attribute_name,
                   placement::int                as attribute_value,
                   'conversion_away_cnt' as metric,
                   conversion_away_cnt::numeric   as value
            from away_agg
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'                as attribute_name,
                   placement::int                  as attribute_value,
                   'session_revenue_cnt'      as metric,
                   session_with_revenue_count::numeric as value
            from revenue
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement' as attribute_name,
                   placement::int   as attribute_value,
                   'revenue'   as metric,
                   revenue_usd::numeric as value
            from revenue
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   'placement'                as attribute_name,
                   placement::int                  as attribute_value,
                   'conv_revenue_usd'      as metric,
                   conv_revenue_usd::numeric as value
            from revenue
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                     as attribute_name,
                   null::int                     as attribute_value,
                   'intention_to_contact_from_jdp_cnt' as metric,
                   intention_to_contact_from_jdp_cnt::numeric   as value
            from intention_to_contact
            union
            select groups::varchar,
                   date_diff,
                   is_returned,
                   device_type_id,
                    is_local::int,
                   session_create_page_type::int,
                   id_traffic_source,
                   id_current_traffic_source,
                   is_anomalistic,
                   null                     as attribute_name,
                   null::int                     as attribute_value,
                   'intention_to_contact_from_serp_cnt' as metric,
                   intention_to_contact_from_serp_cnt::numeric   as value
            from intention_to_contact
        )

        select  _country_id as country_id,
                groups::varchar as group_num,
                date_diff as action_datediff,
                is_returned as is_returned,
                device_type_id as device_type_id,
                is_local,
                session_create_page_type,
                id_traffic_source as traffic_source_id,
                id_current_traffic_source as current_traffic_source_id,
                is_anomalistic,
                attribute_name,
                attribute_value,
                metric as metric_name,
                value as metric_value
        from final
        where value != 0;
