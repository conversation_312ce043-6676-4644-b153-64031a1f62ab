declare @date_diff int = ${dt_begin},
		@country_id int = ${country_id};

with
    t as (
        select distinct
            si.date                                                                                                 as date_diff,
            coalesce(si.id_search, si.id_alertview)                                                                 as id_search,
            si.position,
            coalesce(jr.id_campaign, sc.id_campaign)                                                                as id_campaign,
            round(si.click_price, 3)                                                                                as click_price_usd,
            round(si.serp_click_value, 3)                                                                           as serp_click_value,
            iif(sis.id_impression is not null, 1, 0)                                                                as is_on_screen,
            iif(sc.id is not null, 1, 0)                                                                            as has_click,
            row_number() over (partition by coalesce(si.id_search, si.id_alertview), si.position order by si.score) as row_num
        from dbo.session_impression si with (nolock)
             join dbo.session s with (nolock)
                  on s.date_diff = si.date and s.id = si.id_session
             left join dbo.session_impression_on_screen sis with (nolock)
                       on sis.date_diff = si.date and sis.id_impression = si.id
             left join dbo.job_region jr with (nolock)
                       on jr.uid = si.uid_job
             left join dbo.job j with (nolock)
                       on j.id = jr.id_job
             left join dbo.job_history jh with (nolock)
                       on jh.uid = si.uid_job
             left join dbo.session_click sc with (nolock)
                       on sc.date_diff = si.date and sc.id_impression = si.id
        where si.date = @date_diff
          and si.position <= 50
          and s.flags & 1 = 0
          and (si.id_search is not null or si.id_alertview is not null)
    )
select
    date_diff,
    id_search,
    position,
    id_campaign,
    click_price_usd,
    serp_click_value,
    is_on_screen,
    has_click
into #serp_project_stat
from t
where row_num = 1;

with
    clients_list as (
        select distinct
            id_campaign
        from #serp_project_stat
        where click_price_usd > 0 and
              id_campaign != 0
    )
select
    iif(id_campaign in (select * from clients_list), id_campaign, 0) as rival_campaign_id,
    date_diff,
    id_search,
    position,
    click_price_usd,
    serp_click_value,
    is_on_screen,
    has_click
into #serp_project_stat2
from #serp_project_stat;

with
    serp_keys as (
        select distinct
            rival_campaign_id,
            id_search
        from #serp_project_stat2
    )
select
    t1.rival_campaign_id as target_campaign_id,
    t2.rival_campaign_id,
    t2.id_search
into #serp_keys
from serp_keys t1
     join serp_keys t2
          on t1.id_search = t2.id_search
where t1.rival_campaign_id != 0;

with
    t as (
        select
            #serp_project_stat2.date_diff,
            target_campaign_id,
            #serp_project_stat2.rival_campaign_id,
            click_price_usd,
            serp_click_value,
            count(distinct #serp_keys.id_search) as search_cnt,
            count(*)                             as impression_cnt,
            sum(is_on_screen)                    as impression_on_screen_cnt,
            sum(has_click)                       as click_cnt,
            min(position)                        as position_min,
            max(position)                        as position_max,
            avg(1.0 * position)                  as position_mean
        from #serp_keys
             join #serp_project_stat2
                  on #serp_keys.rival_campaign_id = #serp_project_stat2.rival_campaign_id
                      and #serp_keys.id_search = #serp_project_stat2.id_search
        group by
            #serp_project_stat2.date_diff,
            target_campaign_id,
            #serp_project_stat2.rival_campaign_id,
            click_price_usd,
            serp_click_value
    ),
    total as (
        select
            target_campaign_id,
            #serp_project_stat2.rival_campaign_id,
            count(distinct #serp_keys.id_search) as total_search_cnt
        from #serp_keys
             join #serp_project_stat2
                  on #serp_keys.rival_campaign_id = #serp_project_stat2.rival_campaign_id
                      and #serp_keys.id_search = #serp_project_stat2.id_search
        group by
            target_campaign_id,
            #serp_project_stat2.rival_campaign_id
    )
select
    date_diff,
    t.target_campaign_id,
    t.rival_campaign_id,
    click_price_usd,
    serp_click_value,
    total_search_cnt,
    search_cnt,
    impression_cnt,
    impression_on_screen_cnt,
    click_cnt,
    position_min,
    position_max,
    position_mean
into #result_serp_rivals
from t
     left join total
               on t.rival_campaign_id = total.rival_campaign_id and
                  t.target_campaign_id = total.target_campaign_id;


with
    t as (
        select
            date_diff,
            target_campaign_id,
            rival_campaign_id,
            click_price_usd,
            serp_click_value,
            total_search_cnt,
            search_cnt,
            impression_cnt,
            impression_on_screen_cnt,
            click_cnt,
            position_mean,
            position_min,
            position_max,
            dense_rank() over (partition by target_campaign_id order by total_search_cnt desc) as rival_rank
        from #result_serp_rivals
    )
select
    @country_id as country_id,
    date_diff,
    target_campaign_id,
    rival_campaign_id,
    click_price_usd,
    serp_click_value,
    total_search_cnt,
    search_cnt,
    impression_cnt,
    impression_on_screen_cnt,
    click_cnt,
    position_mean,
    position_min,
    position_max
from t
where rival_rank <= 11;

drop table #serp_project_stat;
drop table #serp_project_stat2;
drop table #serp_keys;
drop table #result_serp_rivals
