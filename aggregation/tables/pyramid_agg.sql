-- moved to archive

declare @dd_start int = datediff(day,'1900-01-01', dbo.ex_getdate()-1),
		@prev_s_start date = dbo.ex_getdate()-1,
		@d_start date = dbo.ex_getdate(),
		@country_id int = ${country_id};

-- Google sessions
with session_google_cnt as
 (
   SELECT
         cast(dateadd(day,s.date_diff,'1900-01-01') as Date) as date,
         count(s.id)                                         as value
   from  dbo.session s with(nolock)
   join  dbo.u_traffic_source u with(nolock)
         on s.id_current_traf_source = u.id
   where s.date_diff = @dd_start
         and isnull(s.flags, 0) & 1 = 0
         and u.name = 'Google'
   group by s.date_diff
  ),

--Paid sessions
session_paid_cnt as
 (
   SELECT
         cast(dateadd(day,s.date_diff,'1900-01-01') as Date) as date,
         count(s.id)                                         as value
   from  dbo.session s with(nolock)
   join  dbo.u_traffic_source u with(nolock)
         on s.id_current_traf_source = u.id
   where s.date_diff = @dd_start
         and isnull(s.flags, 0) & 1 = 0
         and u.is_paid = 1
   group by s.date_diff
 ),

-- Other sessions
session_other_cnt as
 (
   SELECT
         cast(dateadd(day,s.date_diff,'1900-01-01') as Date) as date,
         count(s.id)                                         as value
   from  dbo.session s with(nolock)
   join  dbo.u_traffic_source u with(nolock)
         on s.id_current_traf_source = u.id
   where s.date_diff = @dd_start
         and isnull(s.flags, 0) & 1 = 0
         and u.name <> 'Google'
         and u.is_paid <> 1
   group by s.date_diff
 ),

 -- Active accounts
account_active_cnt as
 (
SELECT
      cast(dbo.ex_getdate()-1 as Date)                      as date,
      count(distinct ai.id_account)                         as value
from  dbo.account a with(nolock)
join  dbo.account_info ai with(nolock)
      on a.id = ai.id_account
join  dbo.account_contact ac with(nolock)
      on a.id = ac.id_account
where ac.verify_date is not null
      and (cast(ai.unsub_date as Date) = @d_start
           or ai.unsub_date is null)
),

-- Accounts added
account_added_cnt as
 (
  SELECT
        cast(a.date_add as Date)                            as date,
        count(a.id)                                         as value
  from  dbo.account a with(nolock)
  where cast(a.date_add as Date) = @prev_s_start
  group by cast(a.date_add as Date)
  ),

-- New Users
user_new_cnt as
 (
  SELECT
        cast(dateadd(day, s.first_visit_date_diff,'1900-01-01') as Date) as date,
        count(distinct s.cookie_label)                                   as value
  from  dbo.session s with(nolock)
  where s.first_visit_date_diff = @dd_start
        and s.first_visit_date_diff = s.date_diff
        and isnull(s.flags, 0) & 1 = 0
  group by s.first_visit_date_diff
  ),

-- Users
user_cnt as
 (
  SELECT
        cast(dateadd(day, s.date_diff,'1900-01-01') as Date)  as date,
        count(distinct cookie_label)                          as value
  from  dbo.session s with(nolock)
  where s.date_diff = @dd_start
        and isnull(s.flags, 0) & 1 = 0
  group by s.date_diff
  ),

-- Returned Users
user_returned_cnt as
 (
  SELECT
        cast(dateadd(day, s.date_diff,'1900-01-01') as Date)  as date,
        count(distinct cookie_label)                          as value
  from  dbo.session s with(nolock)
  where s.date_diff = @dd_start
        and isnull(s.flags, 0) & 1 = 0
        and s.flags & 2 = 2
  group by s.date_diff
  ),

-- Sessions
session_cnt as
 (
  SELECT
        cast(dateadd(day, s.date_diff,'1900-01-01') as Date)  as date,
        count(*)                                              as value
  from  dbo.session s with(nolock)
  where s.date_diff = @dd_start
        and isnull(s.flags, 0) & 1 = 0
  group by s.date_diff
  ),

-- SERP Session
session_serp_cnt as
 (
  SELECT
        cast(dateadd(day, ss.date_diff,'1900-01-01') as Date) as date,
        count(distinct ss.id_session)                         as value
  from  dbo.session_search ss with(nolock)
  join  dbo.session s with(nolock)
        on s.id = ss.id_session
        and s.date_diff = ss.date_diff
  where ss.date_diff = @dd_start
        and isnull(s.flags, 0) & 1 = 0
  group by ss.date_diff
  ),

-- Alert Session
session_alert_cnt as
 (
  SELECT
        cast(dateadd(day, sa.date_diff,'1900-01-01') as Date) as date,
        count(distinct sa.id_session)                         as value
  from  dbo.session_alertview sa with(nolock)
  join  dbo.session s with(nolock)
        on s.id = sa.id_session
        and s.date_diff = sa.date_diff
  where sa.date_diff = @dd_start
        and isnull(s.flags, 0) & 1 = 0
  group by sa.date_diff
  ),

-- JDP Session
session_jdp_cnt as
 (
  SELECT
        cast(dateadd(day, sj.date_diff,'1900-01-01') as Date) as date,
        count(distinct sj.id_session)                         as value
  from  dbo.session_jdp sj with(nolock)
  join  dbo.session s with(nolock)
        on s.id = sj.id_session
        and s.date_diff = sj.date_diff
  where sj.date_diff = @dd_start
        and isnull(s.flags, 0) & 1 = 0
  group by sj.date_diff
  ),

--Alerts added
alert_added_cnt as
 (
  SELECT
        cast(ea.date_add as Date)                            as date,
        count(ea.id)                                         as value
  from dbo.email_alert ea
  where cast(ea.date_add as Date) = @prev_s_start
  group by cast(ea.date_add as Date)
  ),

--Paid email sent
email_paid_sent_cnt as
 (
  SELECT
        cast(dateadd(day, es.date_diff,'1900-01-01') as Date) as date,
        count(es.id)                                          as value
  from dbo.email_sent es
  where es.letter_type in (1,8)                           --PaidJobList
        and es.date_diff = @dd_start
  group by es.date_diff
 ),

--Paid Email opened
email_paid_open_cnt as
 (
  SELECT
        cast(dateadd(day, es.date_diff,'1900-01-01') as Date)  as date,
        count(distinct es.id_message)                          as value
  from dbo.email_sent es
  join dbo.email_open eo
        on  eo.id_message = es.id_message
  where es.letter_type in (1,8)                             --PaidJobList
        and es.date_diff = @dd_start
  group by es.date_diff
  ),

--Jobs added
job_added_cnt as
 (
	select cast(j.date_created as Date)                      as date,
		   count(j.id)                                     as value
	from dbo.job j with(nolock)
	join  dbo.job_region jr  with(nolock) on jr.id_job = j.id
	where jr.inactive = 0 and cast(j.date_created as Date) = @prev_s_start
	group by cast(j.date_created as Date)
  ),

--Active jobs
job_active_cnt as
 (
  SELECT
        cast(dbo.ex_getdate()-1 as Date)                       as date,
        count(distinct j.id)                                   as value
  from  dbo.job j  with(nolock)
  join  dbo.job_region jr  with(nolock)
        on jr.id_job = j.id
  join  dbo.info_project ip with(nolock)
        on ip.id = j.id_project
  where jr.inactive = 0
        and ip.is_active = 1
        and (j.date_expired is null
             or cast(j.date_expired as Date) = @d_start)
  ),

-- Total Aways
aways_cnt as  
  (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)     as date,
          count(sa.id)                                             as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   where  sa.flags & 2 = 0
          and isnull(s.flags, 0) & 1 = 0
          and sa.date_diff = @dd_start
   group by sa.date_diff
   ),

-- Aways from Email Letter Type 8
aways_email_lt8_cnt as  
  (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)      as date,
	      count(sa.id)                                            as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   where  sa.flags & 2 = 0
          and isnull(s.flags, 0) & 1 = 0
	      and sa.letter_type = 8
          and sa.date_diff = @dd_start
   group by sa.date_diff
  ),  
  
-- Aways from Alerts
aways_alert_cnt as
  (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)       as date,
	      count(sa.id)                                             as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   join dbo.session_click sc
	      on sa.date_diff = sc.date_diff
          and sa.id_click = sc.id  
   where  sa.flags & 2 = 0
          and isnull(s.flags, 0) & 1 = 0
	      and sc.id_alertview is not null
          and sa.date_diff = @dd_start
   group by sa.date_diff
   ), 
   
-- Aways from JDP
aways_jdp_cnt as
  (
    select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)      as date,
	     count(sa.id_jdp)                                          as value
    from   dbo.session_away sa
    join   dbo.session s
           on sa.date_diff = s.date_diff
           and sa.id_session = s.id
     where sa.flags & 2 = 0
           and isnull(s.flags, 0) & 1 = 0
           and sa.date_diff = @dd_start
    group by sa.date_diff
   ),  
   
-- Aways from SERP
aways_serp_cnt as 
   (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)       as date,
          count(sa.id_click)                                         as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   join dbo.session_click sc
	      on sa.date_diff = sc.date_diff
          and sa.id_click = sc.id
   where sa.flags & 2 = 0
          and sc.id_alertview is null
          and sc.id_search is not null
          and isnull(s.flags, 0) & 1 = 0
          and sa.date_diff = @dd_start
   group by sa.date_diff
    ),
   
-- Sessions with Away
session_aways_cnt as  
  (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)       as date,
          count(distinct sa.id_session)                              as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   where  sa.flags & 2 = 0
          and isnull(s.flags, 0) & 1 = 0
          and sa.date_diff = @dd_start
   group by sa.date_diff
   ),
   
-- Sessions with Aways from Email Letter Type 8
session_aways_email_lt8_cnt as  
   (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)       as date,
	    count(distinct sa.id_session)                              as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   where sa.flags & 2 = 0
	     and sa.letter_type = 8
         and isnull(s.flags, 0) & 1 = 0
         and sa.date_diff = @dd_start
   group by sa.date_diff
  ),  
  
-- Sessions with Aways from Alerts
session_aways_alert_cnt as
  (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)      as date,
	    count(distinct sa.id_session)                             as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   join dbo.session_click sc
	      on sa.date_diff = sc.date_diff
          and sa.id_click = sc.id
   where  sa.flags & 2 = 0
          and sc.id_alertview is not null
          and isnull(s.flags, 0) & 1 = 0
          and sa.date_diff = @dd_start
   group by sa.date_diff
   ), 
   
-- Sessions with Aways from JDP
session_aways_jdp_cnt as
  (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)      as date,
	    count(distinct sa.id_session)                             as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   where sa.flags & 2 = 0
	   and sa.id_jdp is not null
         and isnull(s.flags, 0) & 1 = 0
         and sa.date_diff = @dd_start
    group by sa.date_diff
   ),  
   
-- Sessions with Aways from SERP
session_aways_serp_cnt as 
   (
   select cast(dateadd(day,sa.date_diff,'1900-01-01') as Date)      as date,
          count(distinct sa.id_session)                             as value
   from   dbo.session_away sa
   join   dbo.session s
          on sa.date_diff = s.date_diff
          and sa.id_session = s.id
   join dbo.session_click sc
	      on sa.date_diff = sc.date_diff
          and sa.id_click = sc.id
   where sa.flags & 2 = 0
          and sc.id_alertview is null
          and sc.id_search is not null
          and isnull(s.flags, 0) & 1 = 0
          and sa.date_diff = @dd_start
   group by sa.date_diff
    )
    

SELECT
@country_id as country, cast(sg.date as datetime) as date, sg.value, 'Google Sessions' as metric
from session_google_cnt sg
union all
select
@country_id as country, cast(sp.date as datetime) as date, sp.value, 'Paid Sessions' as metric
from session_paid_cnt sp
union all
select
@country_id as country, cast(so.date as datetime) as date, so.value, 'Other Sessions' as metric
from session_other_cnt so
union all
select
@country_id as country, cast(aa.date as datetime) as date, aa.value, 'Active Accounts' as metric
from account_active_cnt aa
union all
select
@country_id as country, cast(ad.date as datetime) as date, ad.value, 'Accounts Created' as metric
from account_added_cnt ad
union all
select
@country_id as country, cast(un.date as datetime) as date, un.value, 'New Users' as metric
from user_new_cnt un
union all
select
@country_id as country, cast(u.date as datetime) as date, u.value, 'Users' as metric
from user_cnt u
union all
select
@country_id as country, cast(ur.date as datetime) as date, ur.value, 'Returned Users' as metric
from user_returned_cnt ur
union all
select
@country_id as country, cast(s.date as datetime) as date, s.value, 'Sessions' as metric
from session_cnt s
union all
select
@country_id as country, cast(ss.date as datetime) as date, ss.value, 'SERP Sessions' as metric
from session_serp_cnt ss
union all
select
@country_id as country, cast(sa.date as datetime) as date, sa.value, 'Alert Sessions' as metric
from session_alert_cnt sa
union all
select
@country_id as country, cast(sj.date as datetime) as date, sj.value, 'JDP Sessions' as metric
from session_jdp_cnt sj
union all
select
@country_id as country, cast(a.date as datetime) as date, a.value, 'Alerts Created' as metric
from alert_added_cnt a
union all
select
@country_id as country, cast(es.date as datetime) as date, es.value, 'Paid Email Sent' as metric
from email_paid_sent_cnt es
union all
select
@country_id as country, cast(eo.date as datetime) as date, eo.value, 'Paid Email Open' as metric
from email_paid_open_cnt eo
union all
select
@country_id as country, cast(ja.date as datetime) as date, ja.value, 'Jobs Created' as metric
from job_added_cnt ja
union all
select
@country_id as country, cast(jac.date as datetime) as date, jac.value, 'Active Jobs' as metric
from job_active_cnt jac

union all
select
@country_id as country, cast(ac.date as datetime) as date, ac.value, 'Aways' as metric
from aways_cnt ac
union all
select
@country_id as country, cast(aelc.date as datetime) as date, aelc.value, 'Aways from Email LT8' as metric
from aways_email_lt8_cnt aelc
union all
select
@country_id as country, cast(aac.date as datetime) as date, aac.value, 'Aways from Alerts' as metric
from aways_alert_cnt aac
union all
select
@country_id as country, cast(ajc.date as datetime) as date, ajc.value, 'Aways from JDP' as metric
from aways_jdp_cnt ajc
union all
select
@country_id as country, cast(asec.date as datetime) as date, asec.value, 'Aways from SERP' as metric
from aways_serp_cnt asec
union all 
select
@country_id as country, cast(sac.date as datetime) as date, sac.value, 'Aways Sessions' as metric
from session_aways_cnt sac
union all
select
@country_id as country, cast(saelc.date as datetime) as date, saelc.value, 'Aways Sessions from Email LT8' as metric
from session_aways_email_lt8_cnt saelc
union all
select
@country_id as country, cast(saac.date as datetime) as date, saac.value, 'Aways Sessions from Alerts' as metric
from session_aways_alert_cnt saac
union all
select
@country_id as country, cast(sajc.date as datetime) as date, sajc.value, 'Aways Sessions from JDP' as metric
from session_aways_jdp_cnt sajc
union all
select
@country_id as country, cast(sasc.date as datetime) as date, sasc.value, 'Aways Sessions from SERP' as metric
from session_aways_serp_cnt sasc

