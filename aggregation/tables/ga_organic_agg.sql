SELECT ga.country_code                  AS country_cc,
       cc.name_country_eng              AS country,
       ga.date,
       date_part('year'::text, ga.date) AS year,
       CASE
           WHEN ga.sourcemedium::text = 'google / organic'::text THEN 'google / organic'::text
           WHEN ga.sourcemedium::text = 'google_jobs_apply / organic'::text THEN 'google / jobs apply / organic'::text
           WHEN ga.sourcemedium::text = 'google_jobs_salary / organic'::text THEN 'google / jobs salary / organic'::text
           WHEN ga.sourcemedium::text = 'bing / organic'::text THEN 'bing / organic'::text
           WHEN ga.sourcemedium::text = 'yahoo / organic'::text THEN 'yahoo / organic'::text
           WHEN ga.sourcemedium::text = 'ecosia.org / organic'::text THEN 'ecosia.org / organic'::text
           WHEN ga.sourcemedium::text = 'duckduckgo / organic'::text THEN 'duckduckgo / organic'::text
           ELSE 'other / organic'::text
           END                          AS sourcemedium,
       sum(ga.session_cnt)              AS session_cnt
FROM aggregation.v_ga_session_data ga
         LEFT JOIN dimension.countries cc ON lower(cc.alpha_2::text) = ga.country_code
WHERE ga.date between '2020-01-01'::date and current_date::date
  and ga.channelgrouping::text = 'Organic Search'::text
GROUP BY ga.country_code,
         cc.name_country_eng,
         ga.date,
         (date_part('year'::text, ga.date)),
         CASE
             WHEN ga.sourcemedium::text = 'google / organic'::text THEN 'google / organic'::text
             WHEN ga.sourcemedium::text = 'google_jobs_apply / organic'::text THEN 'google / jobs apply / organic'::text
             WHEN ga.sourcemedium::text = 'google_jobs_salary / organic'::text THEN 'google / jobs salary / organic'::text
             WHEN ga.sourcemedium::text = 'bing / organic'::text THEN 'bing / organic'::text
             WHEN ga.sourcemedium::text = 'yahoo / organic'::text THEN 'yahoo / organic'::text
             WHEN ga.sourcemedium::text = 'ecosia.org / organic'::text THEN 'ecosia.org / organic'::text
             WHEN ga.sourcemedium::text = 'duckduckgo / organic'::text THEN 'duckduckgo / organic'::text
             ELSE 'other / organic'::text
             END
UNION ALL
SELECT NULL::text    AS country_cc,
       NULL::text    AS country,
       c.dt          AS date,
       c.dt_year     AS year,
       NULL::text    AS sourcemedium,
       NULL::numeric AS session_cnt

FROM dimension.info_calendar c
WHERE c.dt between '2022-01-01'::date AND '2023-12-31'::date;
