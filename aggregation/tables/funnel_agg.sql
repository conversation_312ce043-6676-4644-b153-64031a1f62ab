with
     Session_cte as (
         SELECT cast(session.start_date as date)                                   as date,
                case when session.flags & 16 = 16 then 'Mobile' else 'Desktop' end as device,
                session.date_diff,
                session.id                                                         as id_session,
                u_traffic_source.name                                              as traffic_source,
                u_traffic_source.is_paid                                           as traffic_source_is_paid,
                session.session_create_page_type
         FROM dbo.session with (nolock)
                  left join dbo.u_traffic_source with (nolock)
                            on coalesce(session.id_current_traf_source, session.id_traf_source) = u_traffic_source.id
         where session.flags & 1 = 0
           and session.date_diff = @start_date_diff
     ),


     Search as (
         SELECT cast(session_search.date as date) as date,
                Session_cte.device,
                Session_cte.traffic_source,
                Session_cte.traffic_source_is_paid,
                session_search.id                 as id_search,
                session_search.id_session,
                Session_cte.session_create_page_type
         FROM dbo.session_search with (nolock)
                  join Session_cte
                       on session_search.date_diff = Session_cte.date_diff
                           and session_search.id_session = Session_cte.id_session
         where session_search.date_diff = @start_date_diff
     ),


     AlertView as (
         SELECT cast(session_alertview.date as date) as date,
                Session_cte.device,
                Session_cte.traffic_source,
                Session_cte.traffic_source_is_paid,
                session_alertview.id                 as id_alertview,
                session_alertview.id_session,
                Session_cte.session_create_page_type
         FROM dbo.session_alertview with (nolock)
                  join Session_cte
                       on session_alertview.date_diff = Session_cte.date_diff
                           and session_alertview.id_session = Session_cte.id_session
         where session_alertview.date_diff = @start_date_diff
     ),

     JDP as
         (
             Select cast(session_jdp.date as date) as date,
                    Session_cte.device,
                    Session_cte.traffic_source,
                    Session_cte.traffic_source_is_paid,
                    session_jdp.id                 as id_jdp,
                    session_jdp.id_session,
                    Session_cte.session_create_page_type,
                    coalesce(coalesce(session_click.id_search,session_click.id_alertview),session_recommend.id_search) as id_search,
                    info_project.name              as project_name,
                    session_jdp.letter_type,
                    case  when session_click.id_alertview is not null and session_jdp.letter_type is not null then concat('Letter type ',session_jdp.letter_type,'->AlertView->JDP')
                          when session_click.id_alertview is not null and session_jdp.letter_type is null then 'AlertView->JDP'
                          when session_jdp.letter_type is not null and session_click.id_alertview is null then concat('Letter type ',session_jdp.letter_type,'->JDP')
                          when session_click.id_search is not null then 'Serp->JDP'
                        else 'Other->JDP'
                        end as jdp_type
             from dbo.session_jdp with (nolock)
                      left join dbo.info_project with (nolock)
                                on session_jdp.job_id_project = info_project.id
                       join Session_cte with (nolock)
                                on session_jdp.date_diff = Session_cte.date_diff
                                    and session_jdp.id_session = Session_cte.id_session
                       left join dbo.session_click
                                on session_jdp.date_diff = session_click.date_diff
                                     and   session_jdp.id_click =  session_click.id
                       left join dbo.session_recommend
                                on session_click.date_diff = session_recommend.date_diff
                                     and session_click.id_recommend = session_recommend.id
             where session_jdp.date_diff = @start_date_diff
         ),


     Aways as (
         Select cast(session_away.date as date)                       as date,
                session_away.id_session,
                session_away.date_diff,
                info_project.name                                     as project_name,
                case
 	                when session_click_jdp.id_alertview is not null and session_jdp.letter_type is not null and session_away.id_jdp  is not null then concat('Letter type', session_jdp.letter_type,'->AlertView->JDP->Away')
                    when session_click.id_alertview is not null and session_away.letter_type is not null then concat('Letter type', session_jdp.letter_type,'->AlertView->Away')
                    when session_away.id_jdp is not null and session_click_jdp.id_alertview is not null then 'AlertView->JDP->Away'
                    when session_away.id_jdp is not null and session_jdp.letter_type is not null then concat('Letter type', session_jdp.letter_type,'->JDP->Away')
                    when session_away.id_jdp is not null then 'Serp->JDP->Away'
                    when session_click.id_alertview is not null then 'AlertView->Away'
                    when session_click.id_alertview is null and session_click.id_search is not null then 'Serp->Away'
                    when session_away.letter_type is not null then concat('Letter type ', session_away.letter_type,'->Away')
                    when session_away.id_click_no_serp is not null then 'No Serp->Away'
                    when session_away.id_cdp is not null then 'CDP->Away'
                    else 'Other->Away' end                                  as away_type,
                session_away.click_price * info_currency.value_to_usd as away_revenue_usd,
                session_away.id as id_away,
                session_away.id_jdp,
                coalesce(coalesce(coalesce(coalesce(coalesce(session_click.id_search,session_click.id_alertview),session_click_jdp.id_search),session_click_jdp.id_alertview),
                session_recommend.id_search),session_recommend_jdp.id_search)    as id_search,
                conversion_away_connection.id_session_away as id_conversion
         from dbo.session_away with (nolock)
                  left join dbo.session_click with (nolock)
                            on session_away.date_diff = session_click.date_diff
                            and session_away.id_click = session_click.id
                  left join dbo.session_jdp with (nolock)
                            on session_away.date_diff = session_jdp.date_diff
                            and session_away.id_jdp = session_jdp.id
                  left join dbo.session_click as session_click_jdp with (nolock)
                            on session_jdp.date_diff = session_click_jdp.date_diff
                            and session_jdp.id_click = session_click_jdp.id
                  left join dbo.session_recommend
                                on session_click.date_diff = session_recommend.date_diff
                                     and session_click.id_recommend = session_recommend.id
                  left join dbo.session_recommend session_recommend_jdp
                                on session_click_jdp.date_diff = session_recommend_jdp.date_diff
                                     and session_click_jdp.id_recommend = session_recommend_jdp.id
                  left join dbo.info_currency with (nolock)
                            on session_away.id_currency = info_currency.id
                  left join dbo.info_project with (nolock)
                            on session_away.id_project = info_project.id
                  left join (select distinct date_diff, id_session_away from 
                            auction.conversion_away_connection with (nolock) ) conversion_away_connection
                            on session_away.date_diff = conversion_away_connection.date_diff
                            and session_away.id = conversion_away_connection.id_session_away
         where session_away.flags & 2 = 0
           and session_away.date_diff = @start_date_diff
     ),

         Apply as (
         SELECT cast(session_apply.apply_date as date) as date,
                JDP.device,
                JDP.traffic_source,
                JDP.traffic_source_is_paid,
                JDP.session_create_page_type,
                JDP.project_name,
                session_apply.id                 as id_click,
                JDP.id_session,
                JDP.id_search as id_search,
                concat(JDP.jdp_type,'->Apply') as apply_type
         FROM dbo.session_apply with (nolock)
              join dbo.session_jdp_action with (nolock)
             on session_apply.date_diff = session_jdp_action.date_diff
             and session_apply.id_src_jdp_action = session_jdp_action.id
             join JDP
                       on  session_jdp_action.id_jdp = JDP.id_jdp
         where session_apply.date_diff = @start_date_diff
     ),

     Group_Sessions as (
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                count(Session_cte.id_session) as sessions
         from Session_cte
         group by date,
                  device,
                  traffic_source,
                  traffic_source_is_paid,
                  session_create_page_type
     ),
     Group_Searches as (
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                count(distinct Search.id_session) as sessions,
                count(Search.id_search) as searches
         from Search
         group by date,
                  device,
                  traffic_source,
                  traffic_source_is_paid,
                  session_create_page_type
     ),
     Group_Impressions as (
         SELECT
                dateadd(day,impression_statistic.date_diff,'1900-01-01') as date,
                info_project.name as project_name,
                sum(impressions_count) as impressions_count
        FROM auction.impression_statistic with(nolock)
         left join auction.campaign with(nolock)
         on impression_statistic.id_campaign = campaign.id
         left join dbo.info_project with(nolock)
         on campaign.id_project = info_project.id
         where impression_statistic.date_diff =  @start_date_diff
         group by dateadd(day,impression_statistic.date_diff,'1900-01-01'),
                  info_project.name
     ),
     Group_Alertviews as (
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                count(distinct Alertview.id_session) as sessions,
                count(Alertview.id_alertview) as alertViews
         from Alertview
         group by date,
                  device,
                  traffic_source,
                  traffic_source_is_paid,
                  session_create_page_type
     ),

     Group_JDPs as (
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                jdp_type,
                count(distinct JDP.id_session) as sessions,
                count(distinct JDP.id_search) as searches,
                count(id_jdp) as jdps
         from JDP
         group by date,
                  device,
                  traffic_source,
                  traffic_source_is_paid,
                  project_name,
                  jdp_type,
                  session_create_page_type
     ),
     Group_Aways as (
         SELECT Aways.date,
                Session_cte.device,
                Session_cte.traffic_source,
                Session_cte.traffic_source_is_paid,
                Session_cte.session_create_page_type,
                project_name,
                Aways.away_type,
                count(distinct Aways.id_session) as sessions,
                count(distinct Aways.id_search) as searches,
                count(distinct Aways.id_jdp) as jdps,
                count(distinct id_away) as aways,
                count(distinct id_conversion) as conversions,
                sum(Aways.away_revenue_usd) as away_revenue_usd
         from Aways
                   join Session_cte
                            on Aways.date_diff = Session_cte.date_diff
                                and Aways.id_session = Session_cte.id_session
         group by Aways.date,
                  Session_cte.device,
                  Session_cte.traffic_source,
                  Session_cte.traffic_source_is_paid,
                  Session_cte.session_create_page_type,
                  project_name,
                  Aways.away_type
     ),
          Group_Applies as (
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                apply_type,
                count(distinct id_session) as sessions,
                count(distinct id_search) as searches,
                count(id_click) as applies
         from Apply
         group by date,
                  device,
                  traffic_source,
                  traffic_source_is_paid,
                  session_create_page_type,
                  project_name,
                  apply_type
     ),
     Group_Total_Aways as (
         SELECT Aways.date,
                Session_cte.device,
                Session_cte.traffic_source,
                Session_cte.traffic_source_is_paid,
                Session_cte.session_create_page_type,
                project_name,
                case when Aways.away_type like 'Serp->%' then 'Serp Away Total'
                     when Aways.away_type like 'AlertView->%' then 'AlertView Away Total' else 'Other Away Total' end as away_type ,
                count(distinct Aways.id_session) as sessions,
                count(distinct Aways.id_search) as searches,
                count(distinct Aways.id_jdp) as jdps,
                count(id_away) as aways,
                count(id_conversion) as conversions,
                sum(Aways.away_revenue_usd) as away_revenue_usd
         from Aways
                   join Session_cte
                            on Aways.date_diff = Session_cte.date_diff
                                and Aways.id_session = Session_cte.id_session
         group by Aways.date,
                  Session_cte.device,
                  Session_cte.traffic_source,
                  Session_cte.traffic_source_is_paid,
                  Session_cte.session_create_page_type,
                  project_name,
                  Aways.away_type
     ),
         Group_JDPs_Aways as (
          Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                case when jdp_type in ('Serp->Away','Serp->JDP') then 'Serp->JDP/Away'
                     when jdp_type in ('AlertView->Away','AlertView->JDP') then 'AlertView->JDP/Away' else 'Other->JDP/Away' end as away_type,
                count(distinct id_session) as sessions ,
                count(distinct id_search) as searches
           from (
                    Select date,
                           device,
                           traffic_source,
                           traffic_source_is_paid,
                           session_create_page_type,
                           project_name,
                           jdp_type,
                           JDP.id_session,
                           JDP.id_search

                    from JDP
                    union all
                    SELECT Aways.date,
                           Session_cte.device,
                           Session_cte.traffic_source,
                           Session_cte.traffic_source_is_paid,
                           session_create_page_type,
                           project_name,
                           Aways.away_type,
                           Aways.id_session,
                           Aways.id_search
                    from Aways
                             join Session_cte
                                  on Aways.date_diff = Session_cte.date_diff
                                      and Aways.id_session = Session_cte.id_session
                )JdpAways
             group by
                date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                case when jdp_type in ('Serp->Away','Serp->JDP') then 'Serp->JDP/Away'
                     when jdp_type in ('AlertView->Away','AlertView->JDP') then 'AlertView->JDP/Away' else 'Other->JDP/Away' end
     ),
     Unions as (
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                null as project_name,
                'Session->' as step,
                Sessions as value,
                0 as searches,
                Sessions as events,
                0 as away_revenue_usd
         from Group_Sessions
         union all
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                null as project_name,
                'Serp->' as step,
                Sessions as value,
                searches as searches,
                searches as events,
                0 as away_revenue_usd
         from Group_Searches
         union all
         SELECT date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                null as project_name,
                'Alert View->' as step,
                Sessions as value,
                alertViews as searches,
                alertViews as events,
                0 as away_revenue_usd
         from Group_Alertviews
         union all
         Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                jdp_type    as step,
                sessions as value,
                searches as searches,
                jdps as events,
                0 as away_revenue_usd
         from Group_JDPs
         union all
         SELECT date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                away_type as step,
                Sessions as value,
                case when away_type like '%JDP%' then jdps else searches end  as searches,
                aways as events,
                away_revenue_usd
         from Group_Aways
         union all
         SELECT date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                away_type as step,
                Sessions as value,
                case when away_type like '%JDP%' then jdps else searches end  as searches,
                aways as events,
                away_revenue_usd
         from Group_Total_Aways
         union all
              SELECT date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                away_type as step,
                Sessions as value,
                searches,
                0 as events,
                0 as away_revenue_usd
         from Group_JDPs_Aways
         union all
         SELECT date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                concat(away_type,'->Conversion') as step,
                sessions as value,
                searches as searches,
                conversions as events,
                0 as away_revenue_usd
         from Group_Aways
         where conversions > 0 and conversions is not null
                union all
         SELECT date,
                null as device,
                null as traffic_source,
                null as traffic_source_is_paid,
                null as session_create_page_type,
                project_name,
                'Impressions->'as step,
                0 as value,
                0 as searches,
                impressions_count as events,
                0 as away_revenue_usd
         from Group_Impressions
          union all
                Select date,
                device,
                traffic_source,
                traffic_source_is_paid,
                session_create_page_type,
                project_name,
                apply_type    as step,
                sessions as value,
                searches as searches,
                applies as events,
                0 as away_revenue_usd
         from Group_Applies

     ),
Final as (

Select date,
       device,
       traffic_source,
       traffic_source_is_paid,
       session_create_page_type,
       project_name,
       step,
       sum(value)         as value,
       sum(searches)      as searches,
       sum(events)        as events,
       sum(away_revenue_usd) as away_revenue_usd
from Unions
group by date,
         device,
         traffic_source,
         traffic_source_is_paid,
         session_create_page_type,
         project_name,
         step
)
Select    @country_id as country_id,
	   cast([date] as datetime) as [date],
	   device,
	   traffic_source,
	   traffic_source_is_paid,
       session_create_page_type,
	   project_name,
	   step,
	   value,
	   searches,
	   events,
	   away_revenue_usd
from Final
