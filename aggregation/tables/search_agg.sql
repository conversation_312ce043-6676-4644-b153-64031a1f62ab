     create temp table tmp_currency as
     SELECT distinct info_currency.id as id,
                        info_currency.value_to_usd as value_to_usd
     FROM link_dbo.info_currency;
​
        create temp table tmp_au_campaign as
        SELECT distinct campaign.id as id,
                        id_site as id_site
        FROM link_auction.campaign;
​
        create temp table tmp_au_site as
        SELECT distinct site.id as id,
                        id_user as id_user
        FROM link_auction.site;
​
        create temp table tmp_au_user as
        SELECT *
        FROM link_auction."user" u;
​
​
       --delete from an.rpl_search_agg
        delete from an.rpl_search_agg
        where date_diff = _start_date
        ;
​
​
        -- create final result
       insert into an.rpl_search_agg(country_id, date_diff, id_current_traf_source, id_traf_source, user_device, is_returned,
                                     is_local, session_create_page_type, serp_results_total, search_flags, search_source, q_flags, q_age,
                                     q_job_type, q_remote_type, q_kw, q_id_region, empty_searches, search_cnt, click_cnt, revenue_usd,
                                     destination_away_click, destination_jdp_away_click, destination_jdp_apply_click, click_paid_cnt,
                                     click_premium_cnt, click_free_cnt, with_only_free_jobs, with_paid_jobs,
                                     with_paid_clicks, without_paid_clicks, without_any_clicks)
       with paid as (select ss.date_diff,
                            ss.id,
                            max(case
                                    when si.click_price = 0 then 0
                                    when si.click_price > 0 then 1 end)                            as is_paid_search,
                            count(distinct case when sc.click_price <> 0 then sc.id else null end) as paid_click_cnt,
                            count(distinct case when sc.click_price = 0 then sc.id else null end)  as free_click_cnt
                     from public.session_search ss
                     inner join public.session s
                                on ss.date_diff = s.date_diff
                                    and ss.id_session = s.id
                     left join public.session_click sc
                               on sc.date_diff = ss.date_diff
                                   and sc.id_search = ss.id
                                   and sc.flags & 4096 = 0 /*dublicated*/
                                   and sc.flags & 16 = 0 /*test campaign*/
                     left join public.session_impression si
                               on si.date = ss.date_diff
                                   and si.id_search = ss.id
                                   and si.position <= 50
                     where ss.date_diff = _start_date
                       and coalesce(s.flags, 0) & 1 = 0 /*session bot*/
                     -- and country_id <> 1
                     group by ss.date_diff, ss.id)
       select _country_id                                                               as country_id,
              ss.date_diff,
              s.id_current_traf_source,
              s.id_traf_source,
              case
                  when s.flags & 16 = 16 then 1
                  when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                  else 0 end                                                            as user_device,
              sign(s.flags & 2)                                                         as is_returned,
              case
                  when s.ip_cc = lower(current_database())
                      or s.ip_cc = 'gb' and lower(current_database()) = 'uk'
                      then 1
                  else 0 end                                                            as is_local,
              s.session_create_page_type,
              case
                  when ss.results_total = 0 then 0
                  when ss.results_total between 1 and 10 then 1
                  when ss.results_total between 11 and 20 then 2
                  when ss.results_total between 21 and 50 then 3
                  when ss.results_total between 51 and 100 then 4
                  when ss.results_total between 101 and 250 then 5
                  when ss.results_total between 251 and 500 then 6
                  when ss.results_total between 501 and 1000 then 7
                  when ss.results_total between 1001 and 5000 then 8
                  when ss.results_total > 5001 then 9
                  end                                                                   as serp_results_total,
              ss.service_flags                                                          as search_flags,
              ss.search_source,
              ss.q_flags,
              ss.q_age,
              ss.q_job_type,
              ss.q_remote_type,
              case when ss.q_kw = '' then NULL else q_kw end                            AS q_kw,
              ss.q_id_region,
              case
                  when ss.q_id_region <> - 1 and trim(ss.q_kw) = '' then 0
                  when ss.q_id_region = -1 and trim(ss.q_kw) <> '' then 1
                  when ss.q_id_region = -1 and trim(ss.q_kw) = '' then 2
                  when ss.q_id_region <> -1 and trim(ss.q_kw) <> '' then 3
                  else 4 end                                                            as empty_searches,
              count(distinct ss.id)                                                     as search_cnt,
              count(distinct sc.id)                                                     as click_cnt,
              sum(case
                      when sj.id is null then sc.click_price * coalesce(ic.value_to_usd, 0)
                      when sj.id is not null and au.flags & 2 = 0 then sa.click_price * coalesce(ic.value_to_usd, 0)
                      when sj.id is not null and au.flags & 2 = 2 then sa.click_price * coalesce(ic.value_to_usd, 0)
                  end)                                                                  as revenue_usd,
              count(distinct case when sc.job_destination = 1 then sc.id else null end) as destination_away_click,
              count(distinct case when sc.job_destination = 2 then sc.id else null end) as destination_jdp_away_click,
              count(distinct case when sc.job_destination = 3 or sc.job_destination = 4 then sc.id else null end)
                                                                                        as destination_jdp_apply_click,
              count(distinct case when sc.click_price <> 0 then sc.id else null end)    as click_paid_cnt,
              count(distinct case
                                 when sc.flags & 128 = 128 or sj.flags & 256 = 256 then sc.id
                                 else null end)                                         as click_premium_cnt,
              count(distinct case when sc.click_price = 0 then sc.id else null end)     as click_free_cnt,
              sum(case when ps.is_paid_search = 0 then 1 end)                           as with_only_free_jobs,
              sum(case when ps.is_paid_search = 1 then 1 end)                           as with_paid_jobs,
              sum(case when ps.paid_click_cnt = 1 then 1 end)                           as with_paid_clicks,
              sum(case when ps.free_click_cnt = 1 and ps.paid_click_cnt = 0 then 1 end) as without_paid_clicks,
              sum(case when ps.paid_click_cnt = 0 and ps.free_click_cnt = 0 then 1 end) as without_any_clicks
       from public.session_search ss
       inner join public.session s
                  on ss.date_diff = s.date_diff
                      and ss.id_session = s.id
       left join public.session_click sc
                 on sc.date_diff = ss.date_diff
                     and sc.id_search = ss.id
                     and sc.flags & 4096 = 0 /*dublicated*/
                     and sc.flags & 16 = 0 /*test campaign*/
       left join tmp_currency ic
                 on sc.id_currency = ic.id
       left join public.session_jdp sj
                 on sj.date_diff = sc.date_diff
                     and sj.id_click = sc.id
       left join public.session_away sa
                 on sj.date_diff = sa.date_diff
                     and sj.id = sa.id_jdp
                     and sa.flags & 512 = 0 /*dublicated*/
                     and sa.flags & 2 = 0 /*test campaign*/
       left join tmp_au_campaign ac
                 on ac.id = sc.id_campaign
       left join tmp_au_site ast
                 on ac.id_site = ast.id
       left join tmp_au_user au
                 on au.id = ast.id_user
       left join paid ps
                 on ps.date_diff = ss.date_diff and ps.id = ss.id
​
       where ss.date_diff = _start_date
         and coalesce(s.flags, 0) & 1 = 0 /*session bot*/
       -- and country_id <> 1
​
       group by
           -- _country_id as country_id,
           ss.date_diff,
           s.id_current_traf_source,
           s.id_traf_source,
           case
                  when s.flags & 16 = 16 then 1
                  when s.flags & 64 = 64 or s.flags & 128 = 128 then 2
                  else 0 end,
           sign(s.flags & 2),
           case
               when s.ip_cc = lower(current_database())
                   or s.ip_cc = 'gb' and lower(current_database()) = 'uk'
                   then 1
               else 0 end,
           s.session_create_page_type,
           case
               when ss.results_total = 0 then 0
               when ss.results_total between 1 and 10 then 1
               when ss.results_total between 11 and 20 then 2
               when ss.results_total between 21 and 50 then 3
               when ss.results_total between 51 and 100 then 4
               when ss.results_total between 101 and 250 then 5
               when ss.results_total between 251 and 500 then 6
               when ss.results_total between 501 and 1000 then 7
               when ss.results_total between 1001 and 5000 then 8
               when ss.results_total > 5001 then 9
               end,
           ss.service_flags,
           ss.search_source,
           ss.q_flags,
           ss.q_age,
           ss.q_job_type,
           ss.q_remote_type,
           case when ss.q_kw = '' then NULL else q_kw end,
           ss.q_id_region,
           case
                  when ss.q_id_region <> - 1 and trim(ss.q_kw) = '' then 0
                  when ss.q_id_region = -1 and trim(ss.q_kw) <> '' then 1
                  when ss.q_id_region = -1 and trim(ss.q_kw) = '' then 2
                  when ss.q_id_region <> -1 and trim(ss.q_kw) <> '' then 3
                  else 4 end ;
