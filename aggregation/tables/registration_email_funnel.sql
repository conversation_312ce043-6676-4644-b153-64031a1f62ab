delete
from aggregation.registration_email_funnel
where is_complete_data = 0;

-- for each country it could be different value
@date_diff = (select max(session_datediff)
              from free_job_click_revenue_agg
              where is_complete_data = 1);
@country_id = ;

-- dimension with SERP test
drop table if exists temp_session_serp_test_agg;
create temp table temp_session_serp_test_agg as
select id_session                                                as session_id,
       session_test.date_diff                                    as session_datediff,
       array_to_string(array_agg(id_test order by id_test), ' ') as serp_test_list,
       array_to_string(
               array_agg(concat((id_test), '-', session_test."group", '_', (iteration)) order by id_test, iteration),
               ' ')                                              as serp_test_group_lists
from public.session_test
where date_diff > @date_diff
  and iteration >= 0
group by id_session,
         session_test.date_diff;

analyse temp_session_serp_test_agg;

-- dimension with Email test
drop table if exists temp_account_email_test_agg;
create temp table temp_account_email_test_agg as
select date_diff                                                                          as email_test_datediff,
       id_account                                                                         as account_id,
       array_to_string(array_agg(id_test order by id_test), ' ')                          as email_test_list,
       array_to_string(array_agg(concat((id_test), '-', id_group) order by id_test), ' ') as email_test_group_lists

from link_dbo.email_account_test
where flags = 1 /*only for new accounts*/
  and date_diff > @date_diff
group by date_diff,
         id_account
;

-- only unregistered sessions
drop table if exists temp_session_unauthorized;
create temporary table temp_session_unauthorized as
select s.date_diff                             as session_datediff,
       s.id                                    as session_id,
       s.id_current_traf_source                as current_traffic_source_id,
       case
           when s.flags & 2 = 2 then 1
           else 0
           end                                 as is_returned,
       case
           when s.flags & 64 = 64 then 2 /*mobile app*/
           when s.flags & 16 = 16 then 1 /*mobile web*/
           else 0 /*desktop web*/
           end                                 as device_type_id,
       case
           when s.ip_cc = current_database()
               or s.ip_cc = 'gb' and current_database() = 'uk'
               then 1
           else 0 end                          as is_local,
       coalesce(s.session_create_page_type, 0) as session_create_page_type

from session s
where s.date_diff > @date_diff
  and s.flags & 64 = 0
  and s.flags & 1 = 0
  and exists(select 1
             from link_dbo.email_account_interactions eai
             where eai.date_diff = s.date_diff
               and eai.id_session = s.id
               and eai.interaction_type = 0)
union
select s.date_diff                             as session_datediff,
       s.id                                    as session_id,
       s.id_current_traf_source                as current_traffic_source_id,
       case
           when s.flags & 2 = 2 then 1
           else 0
           end                                 as is_returned,
       case
           when s.flags & 64 = 64 then 2 /*mobile app*/
           when s.flags & 16 = 16 then 1 /*mobile web*/
           else 0 /*desktop web*/
           end                                 as device_type_id,
       case
           when s.ip_cc = current_database()
               or s.ip_cc = 'gb' and current_database() = 'uk'
               then 1
           else 0 end                          as is_local,
       coalesce(s.session_create_page_type, 0) as session_create_page_type

from session s
where s.date_diff > @date_diff
  and s.flags & 64 = 0
  and s.flags & 1 = 0
  and not exists(select 1
                 from session_account sa
                 where sa.date_diff = s.date_diff
                   and sa.id_session = s.id)
;

alter table temp_session_unauthorized
    add primary key (session_datediff, session_id);
analyse temp_session_unauthorized;


drop table if exists temp_session_registration_trigger;
create temporary table temp_session_registration_trigger as
select distinct date_diff  as session_datediff,
                id_session as session_id,
                source     as registration_source_id
from session_auth
where date_diff > @date_diff

union

select distinct date_diff  as session_datediff,
                id_session as session_id,
                case
                    when sa.type = 201 then 69
                    when sa.type = 1401 then 20
                    end    as registration_source_id
from session_action sa
where date_diff > @date_diff
;

drop table if exists temp_session_registration_trigger_agg;
create temporary table temp_session_registration_trigger_agg as
select session_datediff,
       session_id,
       count(distinct registration_source_id) as registration_trigger_cnt
from temp_session_registration_trigger
group by session_datediff,
         session_id
;


drop table if exists temp_job_account;
create temp table temp_job_account as
select eai.date_diff       as session_datediff,
       eai.id_session      as session_id,
       max(eai.id_account) as account_id,
       a.source            as registration_source_id
from link_dbo.email_account_interactions eai
join link_dbo.account a
     on a.id = eai.id_account
where eai.interaction_type = 0
  and eai.date_diff > @date_diff
group by eai.date_diff,
         eai.id_session,
         a.source
;


drop table if exists temp_session_registration_ltv;
create temporary table temp_session_registration_ltv as
select ja.session_id,
       ja.session_datediff,
       ja.account_id,
       ja.registration_source_id,
       max((ac.verify_date::date - '1900-01-01'::date)) as verify_datediff,
       max(case
               when (ac.verify_date::date - '1900-01-01'::date) <= ja.session_datediff + 7 then 1
               else 0 end)                              as is_verified,
       sum(ar.email_revenue)                            as verified_email_revenue,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) and (ac.verify_date::date - '1900-01-01'::date) + 3
                   then ar.email_revenue end)           as verified_email_revenue_0_3,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 4 and (ac.verify_date::date - '1900-01-01'::date) + 7
                   then ar.email_revenue end)           as verified_email_revenue_4_7,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 8 and (ac.verify_date::date - '1900-01-01'::date) + 14
                   then ar.email_revenue end)           as verified_email_revenue_8_14,

       sum(case
               when ar.date_diff >= (ac.verify_date::date - '1900-01-01'::date) + 1
                   then ar.total_revenue end)           as verified_total_revenue,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 1 and (ac.verify_date::date - '1900-01-01'::date) + 3
                   then ar.total_revenue end)           as verified_total_revenue_0_3,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 4 and (ac.verify_date::date - '1900-01-01'::date) + 7
                   then ar.total_revenue end)           as verified_total_revenue_4_7,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 8 and (ac.verify_date::date - '1900-01-01'::date) + 14
                   then ar.total_revenue end)           as verified_total_revenue_8_14,

       sum(case
               when ar.date_diff >= (ac.verify_date::date - '1900-01-01'::date) + 1
                   then ar.away_clicks_premium end)     as verified_away_clicks_premium,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 1 and (ac.verify_date::date - '1900-01-01'::date) + 3
                   then ar.away_clicks_premium end)     as verified_away_clicks_premium_0_3,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 4 and (ac.verify_date::date - '1900-01-01'::date) + 7
                   then ar.away_clicks_premium end)     as verified_away_clicks_premium_4_7,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 8 and (ac.verify_date::date - '1900-01-01'::date) + 14
                   then ar.away_clicks_premium end)     as verified_away_clicks_premium_8_14,

       sum(case
               when ar.date_diff >= (ac.verify_date::date - '1900-01-01'::date) + 1
                   then ar.away_clicks_free end)        as verified_clicks_free,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 1 and (ac.verify_date::date - '1900-01-01'::date) + 3
                   then ar.away_clicks_free end)        as verified_away_clicks_free_0_3,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 4 and (ac.verify_date::date - '1900-01-01'::date) + 7
                   then ar.away_clicks_free end)        as verified_away_clicks_free_4_7,
       sum(case
               when ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) + 8 and (ac.verify_date::date - '1900-01-01'::date) + 14
                   then ar.away_clicks_free end)        as verified_away_clicks_free_8_14

from temp_job_account ja
left join link_dbo.account_contact ac
          on ja.account_id = ac.id_account
left join link_dbo.account_revenue ar
          on ar.id_account = ac.id_account and
             ar.date_diff between (ac.verify_date::date - '1900-01-01'::date) and (ac.verify_date::date - '1900-01-01'::date) + 14 and
             (ac.verify_date::date - '1900-01-01'::date) <= ja.session_datediff + 7
group by ja.session_id,
         ja.session_datediff,
         ja.account_id,
         ja.registration_source_id
;

                    create temp table temp_email_open as
                    select *
                    from link_dbo.email_open;

                    create index temp_email_open_pk
                        on temp_email_open (id_account, letter_type, date_diff);

-- кількість верифікованих, які відкриили хоча б 1 лист
drop table if exists temp_account_email_open;
create temp table temp_account_email_open as
select id_account,
       count(distinct case when letter_type = 1 then eo.id_message end)  as letter_1_open_cnt_0_14,
       count(distinct case
                          when letter_type = 1 and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff + 3
                              then eo.id_message end)                    as letter_1_open_cnt_0_3,
       count(distinct case
                          when letter_type = 1 and
                               eo.date_diff between ujr.verify_datediff + 4 and ujr.verify_datediff + 7
                              then eo.id_message end)                    as letter_1_open_cnt_4_7,
       count(distinct case
                          when letter_type = 1 and
                               eo.date_diff between ujr.verify_datediff + 8 and ujr.verify_datediff + 14
                              then eo.id_message end)                    as letter_1_open_cnt_8_14,

       count(distinct case when letter_type = 8 then eo.id_message end)  as letter_8_open_cnt_0_14,
       count(distinct case
                          when letter_type = 8 and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff + 3
                              then eo.id_message end)                    as letter_8_open_cnt_0_3,
       count(distinct case
                          when letter_type = 8 and
                               eo.date_diff between ujr.verify_datediff + 4 and ujr.verify_datediff + 7
                              then eo.id_message end)                    as letter_8_open_cnt_4_7,
       count(distinct case
                          when letter_type = 8 and
                               eo.date_diff between ujr.verify_datediff + 8 and ujr.verify_datediff + 14
                              then eo.id_message end)                    as letter_8_open_cnt_8_14,

       count(distinct case when letter_type = 71 then eo.id_message end) as letter_71_open_cnt_0_14,
       count(distinct case
                          when letter_type = 71 and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff + 3
                              then eo.id_message end)                    as letter_71_open_cnt_0_3,
       count(distinct case
                          when letter_type = 71 and
                               eo.date_diff between ujr.verify_datediff + 4 and ujr.verify_datediff + 7
                              then eo.id_message end)                    as letter_71_open_cnt_4_7,
       count(distinct case
                          when letter_type = 71 and
                               eo.date_diff between ujr.verify_datediff + 8 and ujr.verify_datediff + 14
                              then eo.id_message end)                    as letter_71_open_cnt_8_14
from temp_session_registration_ltv ujr
left join temp_email_open eo
          on eo.id_account = ujr.account_id
              and eo.letter_type in (1, 71, 8)
              and eo.date_diff between ujr.verify_datediff and ujr.verify_datediff + 14
where ujr.is_verified = 1
group by id_account
;

                    create temp table temp_email_visit as
                    select *
                    from link_dbo.email_visit;

                    create index temp_email_visit_pk
                        on temp_email_visit (id_account, letter_type, date_diff);

-- кількість верифікованих, які зробили хоча б 1 клік з імейлу
drop table if exists temp_account_email_visit;
create temp table temp_account_email_visit as
select id_account,
       count(distinct case when letter_type = 1 then ev.id_message end)  as letter_1_interact_cnt_0_14,
       count(distinct case
                          when letter_type = 1 and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff + 3
                              then ev.id_message end)                    as letter_1_interact_cnt_0_3,
       count(distinct case
                          when letter_type = 1 and
                               ev.date_diff between ujr.verify_datediff + 4 and ujr.verify_datediff + 7
                              then ev.id_message end)                    as letter_1_interact_cnt_4_7,
       count(distinct case
                          when letter_type = 1 and
                               ev.date_diff between ujr.verify_datediff + 8 and ujr.verify_datediff + 14
                              then ev.id_message end)                    as letter_1_interact_cnt_8_14,

       count(distinct case when letter_type = 8 then ev.id_message end)  as letter_8_interact_cnt_0_14,
       count(distinct case
                          when letter_type = 8 and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff + 3
                              then ev.id_message end)                    as letter_8_interact_cnt_0_3,
       count(distinct case
                          when letter_type = 8 and
                               ev.date_diff between ujr.verify_datediff + 4 and ujr.verify_datediff + 7
                              then ev.id_message end)                    as letter_8_interact_cnt_4_7,
       count(distinct case
                          when letter_type = 8 and
                               ev.date_diff between ujr.verify_datediff + 8 and ujr.verify_datediff + 14
                              then ev.id_message end)                    as letter_8_interact_cnt_8_14,

       count(distinct case when letter_type = 71 then ev.id_message end) as letter_71_interact_cnt_0_14,
       count(distinct case
                          when letter_type = 71 and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff + 3
                              then ev.id_message end)                    as letter_71_interact_cnt_0_3,
       count(distinct case
                          when letter_type = 71 and
                               ev.date_diff between ujr.verify_datediff + 4 and ujr.verify_datediff + 7
                              then ev.id_message end)                    as letter_71_interact_cnt_4_7,
       count(distinct case
                          when letter_type = 71 and
                               ev.date_diff between ujr.verify_datediff + 8 and ujr.verify_datediff + 14
                              then ev.id_message end)                    as letter_71_interact_cnt_8_14
from temp_session_registration_ltv ujr
left join temp_email_visit ev
          on ev.id_account = ujr.account_id
              and ev.letter_type in (1, 71, 8)
              and ev.date_diff between ujr.verify_datediff and ujr.verify_datediff + 14
where ujr.is_verified = 1
group by id_account;


drop table if exists temp_session_registration_ltv_funnel;
create temporary table temp_session_registration_ltv_funnel as
select su.session_datediff,
       su.session_id,
       su.current_traffic_source_id,
       su.is_returned,
       su.device_type_id,
       su.is_local,
       su.session_create_page_type,
       coalesce(trig.registration_trigger_cnt, 0)             as registration_trigger_cnt,

       serp.serp_test_group_lists,
       reg_ltv.account_id,
       et.email_test_group_lists,
       reg_ltv.registration_source_id,
       reg_ltv.is_verified,

       coalesce(reg_ltv.verified_email_revenue, 0)            as verified_email_revenue,
       coalesce(reg_ltv.verified_email_revenue_0_3, 0)        as verified_email_revenue_0_3,
       coalesce(reg_ltv.verified_email_revenue_4_7, 0)        as verified_email_revenue_4_7,
       coalesce(reg_ltv.verified_email_revenue_8_14, 0)       as verified_email_revenue_8_14,

       coalesce(reg_ltv.verified_total_revenue, 0)            as verified_total_revenue,
       coalesce(reg_ltv.verified_total_revenue_0_3, 0)        as verified_total_revenue_0_3,
       coalesce(reg_ltv.verified_total_revenue_4_7, 0)        as verified_total_revenue_4_7,
       coalesce(reg_ltv.verified_total_revenue_8_14, 0)       as verified_total_revenue_8_14,

       coalesce(reg_ltv.verified_away_clicks_premium, 0)      as verified_away_clicks_premium,
       coalesce(reg_ltv.verified_away_clicks_premium_0_3, 0)  as verified_away_clicks_premium_0_3,
       coalesce(reg_ltv.verified_away_clicks_premium_4_7, 0)  as verified_away_clicks_premium_4_7,
       coalesce(reg_ltv.verified_away_clicks_premium_8_14, 0) as verified_away_clicks_premium_8_14,

       coalesce(reg_ltv.verified_clicks_free, 0)              as verified_clicks_free,
       coalesce(reg_ltv.verified_away_clicks_free_0_3, 0)     as verified_away_clicks_free_0_3,
       coalesce(reg_ltv.verified_away_clicks_free_4_7, 0)     as verified_away_clicks_free_4_7,
       coalesce(reg_ltv.verified_away_clicks_free_8_14, 0)    as verified_away_clicks_free_8_14,

       coalesce(letter_1_open_cnt_0_14, 0)                    as letter_1_open_cnt_0_14,
       coalesce(letter_1_open_cnt_0_3, 0)                     as letter_1_open_cnt_0_3,
       coalesce(letter_1_open_cnt_4_7, 0)                     as letter_1_open_cnt_4_7,
       coalesce(letter_1_open_cnt_8_14, 0)                    as letter_1_open_cnt_8_14,

       coalesce(letter_8_open_cnt_0_14, 0)                    as letter_8_open_cnt_0_14,
       coalesce(letter_8_open_cnt_0_3, 0)                     as letter_8_open_cnt_0_3,
       coalesce(letter_8_open_cnt_4_7, 0)                     as letter_8_open_cnt_4_7,
       coalesce(letter_8_open_cnt_8_14, 0)                    as letter_8_open_cnt_8_14,

       coalesce(letter_71_open_cnt_0_14, 0)                   as letter_71_open_cnt_0_14,
       coalesce(letter_71_open_cnt_0_3, 0)                    as letter_71_open_cnt_0_3,
       coalesce(letter_71_open_cnt_4_7, 0)                    as letter_71_open_cnt_4_7,
       coalesce(letter_71_open_cnt_8_14, 0)                   as letter_71_open_cnt_8_14,

       coalesce(letter_1_interact_cnt_0_14, 0)                as letter_1_interact_cnt_0_14,
       coalesce(letter_1_interact_cnt_0_3, 0)                 as letter_1_interact_cnt_0_3,
       coalesce(letter_1_interact_cnt_4_7, 0)                 as letter_1_interact_cnt_4_7,
       coalesce(letter_1_interact_cnt_8_14, 0)                as letter_1_interact_cnt_8_14,

       coalesce(letter_8_interact_cnt_0_14, 0)                as letter_8_interact_cnt_0_14,
       coalesce(letter_8_interact_cnt_0_3, 0)                 as letter_8_interact_cnt_0_3,
       coalesce(letter_8_interact_cnt_4_7, 0)                 as letter_8_interact_cnt_4_7,
       coalesce(letter_8_interact_cnt_8_14, 0)                as letter_8_interact_cnt_8_14,

       coalesce(letter_71_interact_cnt_0_14, 0)               as letter_71_interact_cnt_0_14,
       coalesce(letter_71_interact_cnt_0_3, 0)                as letter_71_interact_cnt_0_3,
       coalesce(letter_71_interact_cnt_4_7, 0)                as letter_71_interact_cnt_4_7,
       coalesce(letter_71_interact_cnt_8_14, 0)               as letter_71_interact_cnt_8_14,

       now()                                                  as update_datetime
from temp_session_unauthorized su
left join temp_session_registration_trigger_agg trig
          on trig.session_datediff = su.session_datediff and
             trig.session_id = su.session_id
left join temp_session_serp_test_agg serp
          on serp.session_datediff = su.session_datediff and
             serp.session_id = su.session_id
left join temp_session_registration_ltv reg_ltv
          on su.session_datediff = reg_ltv.session_datediff and
             su.session_id = reg_ltv.session_id
left join temp_account_email_test_agg et
          on et.email_test_datediff = reg_ltv.session_datediff and
             et.account_id = reg_ltv.account_id
left join temp_account_email_open aeo
          on aeo.id_account = reg_ltv.account_id
left join temp_account_email_visit aev
          on aev.id_account = reg_ltv.account_id
;


SELECT @country_id                                                   AS country_id,
  update_datetime,
  session_datediff,
  CASE
    WHEN session_datediff <= update_datetime::date - '1900-01-01'::date - 15 THEN 1
    ELSE 0
END::smallint                                                   AS is_complete_data,
  current_traffic_source_id,
  is_returned,
  device_type_id,
  is_local,
  session_create_page_type,
  registration_trigger_cnt,
  registration_source_id,
  serp_test_group_lists,
  email_test_group_lists,
  account_id,
  COUNT(DISTINCT session_id)                                                 AS session_cnt,
  COUNT(DISTINCT CASE WHEN registration_trigger_cnt > 0 THEN session_id END) AS session_with_registration_trigger_cnt,
  COUNT(DISTINCT account_id)                                                 AS new_account_cnt,
  COUNT(DISTINCT CASE WHEN is_verified = 1 THEN account_id END)              AS new_verified_account_cnt,
  COUNT(DISTINCT CASE
    WHEN is_verified = 1 AND
         (letter_1_open_cnt_0_3 + letter_8_open_cnt_0_3 +
          letter_71_open_cnt_0_3 + letter_1_open_cnt_4_7 +
          letter_8_open_cnt_4_7 + letter_71_open_cnt_4_7) >= 1
    THEN account_id END)                                AS account_with_at_least_1_open_letter_cnt_0_7,
  COUNT(DISTINCT CASE
    WHEN is_verified = 1 AND
         (letter_1_open_cnt_0_3 + letter_8_open_cnt_0_3 +
          letter_71_open_cnt_0_3 + letter_1_open_cnt_4_7 +
          letter_8_open_cnt_4_7 + letter_71_open_cnt_4_7) >= 2
    THEN account_id END)                                AS account_with_at_least_2_open_letter_cnt_0_7,
  COUNT(DISTINCT CASE
    WHEN is_verified = 1 AND
         (letter_1_interact_cnt_0_3 + letter_8_interact_cnt_0_3 +
          letter_71_interact_cnt_0_3 + letter_1_interact_cnt_4_7 +
          letter_8_interact_cnt_4_7 + letter_71_interact_cnt_4_7) >= 1
    THEN account_id END)                                AS account_with_at_least_1_interact_letter_cnt_0_7,
  COUNT(DISTINCT CASE
    WHEN is_verified = 1 AND
         (letter_1_interact_cnt_0_3 + letter_8_interact_cnt_0_3 +
          letter_71_interact_cnt_0_3 + letter_1_interact_cnt_4_7 +
          letter_8_interact_cnt_4_7 + letter_71_interact_cnt_4_7) >= 2
    THEN account_id END)                                AS account_with_at_least_2_interact_letter_cnt_0_7,
  SUM(verified_email_revenue)                                                AS verified_email_revenue,
  SUM(verified_email_revenue_0_3)                                            AS verified_email_revenue_0_3,
  SUM(verified_email_revenue_4_7)                                            AS verified_email_revenue_4_7,
  SUM(verified_email_revenue_8_14)                                           AS verified_email_revenue_8_14,
  SUM(verified_total_revenue)                                                AS verified_total_revenue,
  SUM(verified_total_revenue_0_3)                                            AS verified_total_revenue_0_3,
  SUM(verified_total_revenue_4_7)                                            AS verified_total_revenue_4_7,
  SUM(verified_total_revenue_8_14)                                           AS verified_total_revenue_8_14,
  SUM(verified_away_clicks_premium)                                          AS verified_away_clicks_premium,
  SUM(verified_away_clicks_premium_0_3)                                      AS verified_away_clicks_premium_0_3,
  SUM(verified_away_clicks_premium_4_7)                                      AS verified_away_clicks_premium_4_7,
  SUM(verified_away_clicks_premium_8_14)                                     AS verified_away_clicks_premium_8_14,
  SUM(verified_clicks_free)                                                  AS verified_clicks_free,
  SUM(verified_away_clicks_free_0_3)                                         AS verified_away_clicks_free_0_3,
  SUM(verified_away_clicks_free_4_7)                                         AS verified_away_clicks_free_4_7,
  SUM(verified_away_clicks_free_8_14)                                        AS verified_away_clicks_free_8_14,
  SUM(letter_1_open_cnt_0_14)                                                AS letter_1_open_cnt_0_14,
  SUM(letter_1_open_cnt_0_3)                                                 AS letter_1_open_cnt_0_3,
  SUM(letter_1_open_cnt_4_7)                                                 AS letter_1_open_cnt_4_7,
  SUM(letter_1_open_cnt_8_14)                                                AS letter_1_open_cnt_8_14,
  SUM(letter_8_open_cnt_0_14)                                                AS letter_8_open_cnt_0_14,
  SUM(letter_8_open_cnt_0_3)                                                 AS letter_8_open_cnt_0_3,
  SUM(letter_8_open_cnt_4_7)                                                 AS letter_8_open_cnt_4_7,
  SUM(letter_8_open_cnt_8_14)                                                AS letter_8_open_cnt_8_14,
  SUM(letter_71_open_cnt_0_14)                                               AS letter_71_open_cnt_0_14,
  SUM(letter_71_open_cnt_0_3)                                                AS letter_71_open_cnt_0_3,
  SUM(letter_71_open_cnt_4_7)                                                AS letter_71_open_cnt_4_7,
  SUM(letter_71_open_cnt_8_14)                                               AS letter_71_open_cnt_8_14,
  SUM(letter_1_interact_cnt_0_14)                                            AS letter_1_interact_cnt_0_14,
  SUM(letter_1_interact_cnt_0_3)                                             AS letter_1_interact_cnt_0_3,
  SUM(letter_1_interact_cnt_4_7)                                             AS letter_1_interact_cnt_4_7,
  SUM(letter_1_interact_cnt_8_14)                                            AS letter_1_interact_cnt_8_14,
  SUM(letter_8_interact_cnt_0_14)                                            AS letter_8_interact_cnt_0_14,
  SUM(letter_8_interact_cnt_0_3)                                             AS letter_8_interact_cnt_0_3,
  SUM(letter_8_interact_cnt_4_7)                                             AS letter_8_interact_cnt_4_7,
  SUM(letter_8_interact_cnt_8_14)                                            AS letter_8_interact_cnt_8_14,
  SUM(letter_71_interact_cnt_0_14)                                           AS letter_71_interact_cnt_0_14,
  SUM(letter_71_interact_cnt_0_3)                                            AS letter_71_interact_cnt_0_3,
  SUM(letter_71_interact_cnt_4_7)                                            AS letter_71_interact_cnt_4_7,
  SUM(letter_71_interact_cnt_8_14)                                           AS letter_71_interact_cnt_8_14,

  COUNT(DISTINCT CASE
                  WHEN is_verified = 1 AND verified_email_revenue_0_3 + verified_email_revenue_4_7 > 0 THEN account_id END)   AS account_with_verified_email_revenue_cnt_0_7

FROM temp_session_registration_ltv_funnel s
GROUP BY @country_id,
         update_datetime,
         session_datediff,
         current_traffic_source_id,
         is_returned,
         device_type_id,
         is_local,
         session_create_page_type,
         registration_trigger_cnt,
         registration_source_id,
         serp_test_group_lists,
         email_test_group_lists,
         account_id;
