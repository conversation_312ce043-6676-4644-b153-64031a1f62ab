declare @date_diff int = ${dt_begin},
        @country_id int = ${country_id};

select         @country_id                                as country_id,
               s.first_visit_date_diff                    as first_visit_date_diff,
               st.groups                                  as groups,
               case when s.flags & 16 = 16 then 1 else 0  end   as is_mobile,
               case when s.ip_cc = lower(substring(db_name(), 5, 2)) or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                       then 1 else 0 end                as is_local,
               coalesce(s.session_create_page_type, 0) as session_create_page_type,
               s.id_traf_source                        as id_traffic_source,
               s.id_current_traf_source                as id_current_traffic_source,
               count(s.id )                            as session_cnt,
               count( distinct s.cookie_label )        as user_cnt,
               count( distinct case when s.date_diff - s.first_visit_date_diff between 1 and 7 then s.cookie_label end) as week_retention_user_cnt,
               count( distinct case when s.date_diff - s.first_visit_date_diff between 8 and 14 then s.cookie_label end) as second_week_retention_user_cnt,
               count( distinct case when s.date_diff - s.first_visit_date_diff between 15 and 21 then s.cookie_label end) as third_week_retention_user_cnt,
               count( distinct case when s.date_diff - s.first_visit_date_diff between 22 and 30 then s.cookie_label end) as fourth_week_retention_user_cnt,
               count( distinct case when s.date_diff - s.first_visit_date_diff between 1 and 30 then s.cookie_label end) as month_retention_user_cnt
        from dbo.session s with (nolock)
        left join dbo.session_test_agg st with (nolock)on st.date_diff = s.date_diff and s.id = st.id_session
        where s.flags & 1 = 0 and s.first_visit_date_diff = @date_diff
group by       st.groups                             ,
               s.first_visit_date_diff                ,
               case
                   when s.flags & 16 = 16 then 1
                   else 0
                   end                                ,
               case
                   when s.ip_cc = lower(substring(db_name(), 5, 2))
                       or s.ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'
                       then 1
                   else 0 end ,
               coalesce(s.session_create_page_type, 0),
               s.id_traf_source  ,
               s.id_current_traf_source ;
