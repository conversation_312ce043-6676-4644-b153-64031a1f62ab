SET NOCOUNT ON;


Select 
       cast(getdate() as datetime) as date,
       job.id_project,
       job_region.id_campaign,
       sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end)     as paid_job_count,
       sum(case when campaign.click_price = 0 and coalesce(job_region_billing_info.click_price, 0) = 0 then 1 else 0 end)    as organic_job_count,
       coalesce(sum(case when campaign.click_price = 0 and campaign.is_price_per_job = 1 then coalesce(job_region_billing_info.click_price, 0)* info_currency.value_to_usd else campaign.click_price* info_currency.value_to_usd end) / nullif(sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end),0),0) as avg_cpc_for_paid_job_count_usd,
       coalesce(sum(case when campaign.click_price = 0 and campaign.is_price_per_job = 1 then coalesce(job_region_billing_info.click_price, 0) else campaign.click_price end) / nullif(sum(case when campaign.click_price > 0 or coalesce(job_region_billing_info.click_price, 0) > 0 then 1 else 0 end),0),0) as avg_cpc_for_paid_job_count,
       sum(case when campaign.flags & 32 = 32 then 1 else 0 end) as flag_32_jobs_count,
       campaign.budget*info_currency.value_to_usd as campaign_budget,
       campaign.daily_budget *info_currency.value_to_usd as campaign_daily_budget,
       campaign.is_price_per_job,
       campaign.flags as campaign_flags,
       campaign.campaign_status,
       sum(case when job_region_billing_info.inactive_reason & 2 = 2  then 1 else 0 end)                                           as min_cpc_job_count,
       sum(case when job_region_billing_info.inactive_reason & 4 = 4  then 1 else 0 end)                                           as max_cpc_job_count,
       job.id_category                                                                                                              as job_category_id
from dbo.job with (nolock)
         join dbo.job_region with (nolock)
              on job.id = job_region.id_job
         join auction.campaign with (nolock)
              on job_region.id_campaign = campaign.id
          join dbo.info_currency with (nolock)
               on info_currency.id = campaign.currency
         left join dbo.job_region_billing_info with (nolock)
               on job_region.uid = job_region_billing_info.uid_job
where (job.date_expired is null or cast(job.date_expired as date) > getdate())
  and job_region.inactive = 0
  and (campaign.date_end is null or cast(campaign.date_end as date) > getdate())
  and campaign.campaign_status = 0
group by job.id_project,
         job_region.id_campaign,
         campaign.budget*info_currency.value_to_usd,
         campaign.daily_budget *info_currency.value_to_usd,
         campaign.is_price_per_job,
         campaign.flags,
         campaign.campaign_status,
         job.id_category ;




-- last updated by ono, 2/8/2023 - added new columns min/max cpc --
-- ono 31/5/23 add id_category
-- R&P 06/07/23 - add avg_cpc_for_paid_job_count
