SELECT e.id                     as employer_id,
       date(e.date_created)     as date_created_emp_account,
       ec.company_name,
       ec.industry              as company_industry,
       so.date_ordered          as date_ordered,
       date(so.date_paid)       as date_paid,
       so.price,
       so.currency_iso_code,
       ps.id_packet             as packet_id,
       ps.status                as payment_status,
       e.id_traffic_source,
       uts.name                 as traffic_source
FROM employer_account.billing_agreement ba
inner join employer_account.billing_info bi on bi.id = ba.id_billing_info
inner join employer_account.subscription_order so on ba.id = so.id_agreement
inner join employer_account.employer e on e.id = bi.id_employer
inner join employer_account.employer_cdp ec on ec.id = e.id_cdp
left join employer_account.u_traffic_source uts on uts.id = e.id_traffic_source
left join employer_account.packet_subscription ps on ps.id_agreement = so.id_agreement and ps.id_packet <> 49 /* except Free packet */ and ps.status = 2 /* only Active: successful payment */
where e.country_code = 'rs'
      and date(so.date_paid) between :to_sqlcode_date_start and :to_sqlcode_date_end
;
