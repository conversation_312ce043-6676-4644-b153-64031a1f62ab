declare @start_datediff int = ${dt_begin},
    @end_dt_diff int = ${dt_end},
    @country_id int = ${country_id};

with clicks as (
    select date_diff,
           id_session,
           count(*) as clicks
    from (
             select date_diff,
                    id_session,
                    id as id_click
             from dbo.session_click with (nolock)
             where date_diff between @start_datediff and @end_dt_diff
             union
             select date_diff,
                    id_session,
                    id as id_click
             from dbo.session_click_no_serp with (nolock)
             where date_diff between @start_datediff and @end_dt_diff
         ) click
    group by date_diff,
             id_session
),
     sessions as (
         select session.date_diff,
                id                                                                                     as id_session,
                id_traf_source,
                id_current_traf_source,
                session_create_page_type,
                cookie_label,
                sum(case when flags & 1 = 1 or user_agent_hash64 = 5981440342270357754 then 1 end)     as bot_session_cnt,
                sum(case
                        when (ip_cc = lower(substring(db_name(), 5, 2))
                            or (ip_cc = 'gb' and lower(substring(db_name(), 5, 2)) = 'uk'))
                            and flags & 1 = 0 and user_agent_hash64 != 5981440342270357754
                            then 1 end)                                                                as local_session_nobot_cnt,
                sum(case
                        when flags & 16 = 16
                            and flags & 1 = 0 and user_agent_hash64 != 5981440342270357754
                            then 1 end)                                                                as mobile_session_nobot_cnt,
                sum(case
                        when flags & 2 = 2
                            and flags & 1 = 0 and user_agent_hash64 != 5981440342270357754
                            then 1 end)                                                                as returned_session_nobot_cnt,
                sum(case
                        when (flags & 64 = 64 or flags & 128 = 128) and flags & 1 = 0
                            and  user_agent_hash64 != 5981440342270357754
                            then 1 end)                                                                as mobile_app_session_nobot_cnt,
                sum(clicks)                                                                            as clicks
         from dbo.session with (nolock)
                  left join clicks
                            on session.date_diff = clicks.date_diff
                                and session.id = clicks.id_session
         where session.date_diff between @start_datediff and @end_dt_diff
           and flags & 4 = 0
         group by session.date_diff,
                  id_traf_source,
                  id_current_traf_source,
                  session_create_page_type,
                  id,
                  cookie_label
     )

Select @country_id                                                             as country_id,
       date_diff                                                         as action_datediff,
       id_traf_source,
       id_current_traf_source,
       session_create_page_type,
       case when coalesce(clicks, 0) < 3 then coalesce(clicks, 0) else 3 end   as click_type,
       count(distinct case when bot_session_cnt is null then cookie_label end) as user_no_bot_cnt,
       count(id_session)                                                       as total_session_cnt,
       sum(bot_session_cnt)                                                    as bot_session_cnt,
       sum(local_session_nobot_cnt)                                            as local_session_nobot_cnt,
       sum(mobile_session_nobot_cnt)                                           as mobile_session_nobot_cnt,
       sum(returned_session_nobot_cnt)                                         as returned_session_nobot_cnt,
       sum(mobile_app_session_nobot_cnt)                                       as mobile_app_session_nobot_cnt, 
       sum(case when bot_session_cnt is null then clicks end)                  as click_nobot_cnt

from sessions
group by date_diff,
         id_traf_source,
         id_current_traf_source,
         session_create_page_type,
         case when coalesce(clicks, 0) < 3 then coalesce(clicks, 0) else 3 end

