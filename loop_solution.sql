-- Loop solution to replace repetitive SQL statements
DO $$
DECLARE
    i INT;
BEGIN
    -- Drop the temporary table if it exists
    DROP TABLE IF EXISTS temp_session_test_agg;
    
    -- Create the temporary table with the first iteration (i=31)
    CREATE TEMP TABLE temp_session_test_agg AS
    SELECT id_session,
           session_test.date_diff,
           array_to_string(array_agg(id_test), ' ') AS id_tests,
           array_to_string(array_agg(concat((id_test), '-', session_test."group", '_', (iteration))), ' ') AS groups
    FROM public.session_test
    WHERE date_diff = 45797 - 31
      AND iteration >= 0
    GROUP BY id_session,
             session_test.date_diff;
    
    -- Loop from 30 down to 1
    FOR i IN REVERSE 30..1 LOOP
        -- Insert data for each date_diff value
        INSERT INTO temp_session_test_agg
        SELECT id_session,
               session_test.date_diff,
               array_to_string(array_agg(id_test), ' ') AS id_tests,
               array_to_string(array_agg(concat((id_test), '-', session_test."group", '_', (iteration))), ' ') AS groups
        FROM public.session_test
        WHERE date_diff = 45797 - i
          AND iteration >= 0
        GROUP BY id_session,
                 session_test.date_diff;
    END LOOP;
END $$;
