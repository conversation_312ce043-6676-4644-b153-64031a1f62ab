create or replace procedure affiliate.insert_statistics_daily_raw(_datediff integer)
    language plpgsql
as
$$
begin

    delete
    from
        affiliate.statistics_daily_raw
    where
        date_diff = _datediff;

    drop table if exists temp_click_data;
    drop table if exists temp_click_ids;
    drop table if exists temp_last_job_feed_changes;
    drop table if exists temp_click_afg_data_1;
    drop table if exists temp_click_afg_data_2;
    drop table if exists temp_cpc_changes;
    drop table if exists temp_click_afg_data_3;
    drop table if exists temp_api_request;
    drop table if exists temp_prod_click_settings;
    drop table if exists temp_prod_click_cpc_ratio;
    drop table if exists temp_cpc_change_curr_rate;
    drop table if exists temp_click_curr_rate;
    drop table if exists temp_result;
    drop table if exists temp_duplicated_clicks;
    drop table if exists temp_cpc_changes_total;
    drop table if exists temp_cpc_changes_unique;
    drop table if exists temp_result_data;
    drop table if exists temp_only_local_projects;
    drop table if exists temp_olpc;

    create temporary table temp_only_local_projects
    as (select distinct
            cobra_info_projects_nonlocal.request_date,
            cobra_info_projects_nonlocal.project_id,
            cobra_info_projects_nonlocal.country_code,
            1 as is_only_local,
            cobra_info_projects_nonlocal.allow_nonlocal_traffic
        from
            imp_api.cobra_info_projects_nonlocal
        where
            request_date = public.fn_get_timestamp_from_date_diff(_datediff));

    create temporary table temp_olpc --partially allowed
    as (select
            cobra_info_projects_nonlocal.request_date,
            cobra_info_projects_nonlocal.country_code,
            cobra_info_projects_nonlocal.project_id,
            cobra_info_projects_nonlocal.allow_nonlocal_traffic,
            cobra_info_projects_nonlocal.allowed_countries
        from
            imp_api.cobra_info_projects_nonlocal
        where
                cobra_info_projects_nonlocal.allow_nonlocal_traffic::text = 'partially allowed'::text
          and   request_date = public.fn_get_timestamp_from_date_diff(_datediff));

    -- JOIN PROD DATA WITH CURRENT PARTNER SETTINGS TO NOT ADD THESE JOINS IN COMPLEX QUERIES
    create temporary table temp_click_data as
    select
        c.country,
        date_diff,
        id_external_source,
        id_traffic_source,
        id_current_traffic_source,
        c.partner,
        coalesce(id_external, 0)                                          as id_external,
        id_away,
        id_jdp,
        id_jdp_away,
        datetime,
        coalesce((c.extra_query_params::jsonb ->> 'extra_prev_uid')::bigint,
                 c.uid_job)                                               as uid_job,
        c.id_project,
        revenue,
        revenue_usd,
        client_id_currency,
        partner_cost,
        user_country,
        is_bot,
        is_mobile,
        is_returned,
        source,
        external_flags,
        has_away_conversion,
        has_jdp_away_conversion,
        user_ip,
        is_external_stat_client,
        id_external_init,
        c.id_campaign,
        is_internal_duplicated,
        id_sub_client,
        p.source_id,
        p.id_local_partner,
        p.feed_type,
        p.is_api_publisher,
        p.partner_id_currency,
        p.is_rounded_cpc,
        c.session_flags,
        c.id_category,
        (((coalesce((c.extra_query_params::jsonb ->> 'extra_prev_uid')::bigint, c.uid_job) % 1000) + 1000) %
         1000)                                                            as uid_job_remainder,
        case
            when (c.extra_query_params::jsonb ->> 'extra_prev_uid')::bigint is not null
                then c.uid_job end                                        as redirected_to_uid,
        coalesce((c.extra_query_params::jsonb ->> 'extra_flags')::int, 0) as url_extra_flags,
        (c.extra_query_params::jsonb ->> 'extra_ars_request_id')::varchar as api_request_id,
        (c.extra_query_params::jsonb ->> 'extra_tid')::varchar            as pub_tracking_id
    from
        affiliate.temp_prod_click c
        left join affiliate.partner_settings p
                  on c.country = p.country
                      and lower(c.partner) = lower(p.partner);

    -- PREPARE KEYS FOR JOINING FOR THE NEXT TABLES
    create temporary table temp_click_ids as
    select distinct
        country,
        id_external_init,
        id_external,
        id_local_partner,
        uid_job,
        source_id,
        datetime
    from
        temp_click_data
    where
        is_api_publisher = 0;

    -- FIND MOST RECENT DATE WHEN JOB WAS DELETED FROM FEED (NEED TO DETERMINE IF CLICK IS BILLABLE)
    create temporary table temp_last_job_feed_changes as
    select distinct on (c.country, c.id_external_init, c.id_external, c.source_id)
        c.country,
        c.id_external_init,
        c.id_external,
        c.source_id,
        log.inactive,
        log.datetime as inactivation_date,
        log.click_price
    from
        temp_click_ids c
        join affiliate.short_job_in_feed_log log
             on c.source_id = log.source_id
                 and c.id_local_partner = log.id_local_partner
                 and c.uid_job = log.job_uid
                 and c.datetime > log.datetime
    order by
        c.country,
        c.id_external_init,
        c.id_external,
        c.source_id,
        log.datetime desc,
        log.inactive desc,
        log.click_price;

    delete
    from
        temp_last_job_feed_changes
    where
        inactive = false;

    -- ADD DATE OF JOB DELETING FROM FEED TO PROD CLICK KEYS
    -- (TO FIND MOST RECENT CLICK PRICE UNTIL upper_dt: EITHER JOB DELETING DATE IF AVAILABLE OR CLICK DATE)
    create temporary table temp_click_afg_data_1 as
    select
        c.id_external_init,
        c.id_external,
        c.country,
        c.source_id,
        c.id_local_partner,
        c.datetime,
        c.uid_job,
        tja.inactivation_date,
        tja.click_price                             as feed_log_click_price,
        coalesce(tja.inactivation_date, c.datetime) as upper_dt
    from
        temp_click_ids c
        left join temp_last_job_feed_changes tja
                  on c.source_id = tja.source_id
                      and c.country = tja.country
                      and c.id_external_init = tja.id_external_init
                      and c.id_external = tja.id_external;


    -- FIND LAST VALID FEED GATHER DT FOR EACH CLICK
    create temporary table temp_click_afg_data_2 as
    select
        id_external_init,
        id_external,
        country,
        t.source_id,
        id_local_partner,
        datetime,
        uid_job,
        inactivation_date,
        upper_dt,
        feed_log_click_price,
        max(tsl.task_datetime) as last_gather_dt
    from
        temp_click_afg_data_1 t
        left join affiliate.task_status_log tsl
                  on tsl.source_id = t.source_id
                      and tsl.id_partner = t.id_local_partner
                      and tsl.task_datetime < t.upper_dt
    group by
        id_external_init, id_external, country, t.source_id, id_local_partner, datetime, uid_job, upper_dt,
        inactivation_date, feed_log_click_price;

    -- FOR EACH CLICK FIND ALL CPC CHANGES BEFORE upper_dt
    create temporary table temp_cpc_changes_total as
    select
        t.id_external_init,
        t.id_external,
        t.country,
        t.source_id,
        log.datetime as click_price_change_dt,
        log.click_price_usd,
        t.id_local_partner,
        t.upper_dt,
        t.uid_job,
        log.id_campaign
    from
        temp_click_afg_data_2 t
        left join affiliate.short_click_price_change log
                  on log.source_id = t.source_id
                      and log.country_code = t.country
                      and log.job_uid = t.uid_job
                      and log.datetime < t.upper_dt;


    -- FOR EACH CLICK FIND THE FEED GENERATION DATE SUCH THAT:
    -- IT OCCURRED BEFORE upper_dt
    -- IT OCCURRED AFTER THE LAST POSSIBLE CLICK PRICE CHANGE
    create temporary table temp_cpc_changes_unique as
    select distinct on (tc.country, tc.id_external_init, tc.id_external)
        tc.id_external_init,
        tc.id_external,
        tc.country,
        tsl.task_datetime as cpc_change_date,
        tc.click_price_usd,
        tc.id_campaign,
        tc.uid_job
    from
        temp_cpc_changes_total tc
        join affiliate.task_status_log tsl
             on tsl.source_id = tc.source_id
                 and tsl.id_partner = tc.id_local_partner
                 and tsl.task_datetime >= tc.click_price_change_dt
                 and tsl.task_datetime < tc.upper_dt
    order by
        tc.country, tc.id_external_init, tc.id_external, tc.click_price_change_dt desc, tsl.task_datetime;


    create temporary table temp_click_afg_data_3 as
    select
        tc.id_external_init,
        tc.id_external,
        tc.country,
        tc.source_id,
        tc.id_local_partner,
        tcc.cpc_change_date,
        tcc.click_price_usd,
        tcc.id_campaign as gather_id_campaign,
        inactivation_date,
        feed_log_click_price,
        last_gather_dt,
        ocr.cpc_ratio   as optimal_cpc_ratio
    from
        temp_click_afg_data_2 tc
        left join temp_cpc_changes_unique tcc
                  on tcc.country = tc.country
                      and tcc.id_external_init = tc.id_external_init
                      and tcc.id_external = tc.id_external
        left join affiliate.partner_settings ps
                  on ps.source_id = tc.source_id and
                     ps.id_local_partner = tc.id_local_partner
        left join affiliate.optimal_cpc_ratio ocr
                  on ocr.source_id = tc.source_id
                      and ocr.id_local_partner = tc.id_local_partner
                      and ocr.click_price_usd = round(tcc.click_price_usd, 3)
                      and ocr.task_datetime = tc.last_gather_dt;

    -- FIND DT OF API REQUEST FOR EACH CLICK FROM API PUBLISHER
    create temporary table temp_api_request as
    select distinct
        id_external_init,
        id_external,
        country,
        t.source_id,
        id_local_partner,
        datetime,
        uid_job,
        arh.request_id,
        arh.request_datetime
    from
        temp_click_data t
        left join affiliate.api_request_history arh
                  on arh.source_id = t.source_id
                      and arh.partner_id = t.id_local_partner
                      and arh.request_id = t.api_request_id
    where
        t.is_api_publisher = 1;

    -- FOR EACH CLICK FIND RELEVANT FEED SETTINGS
    create temporary table temp_prod_click_settings as
    select
        tlg.country,
        tlg.id_external_init,
        tlg.id_external,
        tlg.source_id,
        null::timestamp                                                                      as request_datetime,
        max(case when vpscl.field_name = 'cpc_ratio' then vpscl.field_value end)             as cpc_ratio,
        max(case when vpscl.field_name = 'worst_cpc_ratio' then vpscl.field_value end)       as worst_cpc_ratio,
        max(case when vpscl.field_name = 'min_cpc_in_usd' then vpscl.field_value end)        as min_cpc_in_usd,
        max(case when vpscl.field_name = 'local_flags' then vpscl.field_value end)::integer  as local_flags,
        max(case when vpscl.field_name = 'static_min_cpc' then vpscl.field_value end)        as static_min_cpc,
        max(case when vpscl.field_name = 'update_gap' then vpscl.field_value end)            as update_gap,
        max(case when vpscl.field_name = 'optimized_cpc_part' then vpscl.field_value end)    as optimized_cpc_part,
        max(case when vpscl.field_name = 'reassembled_jobs_part' then vpscl.field_value end) as reassembled_jobs_part
    from
        temp_click_afg_data_3 tlg
        left join affiliate.v_partner_settings_change_log vpscl
                  on tlg.source_id = vpscl.source_id
                      and tlg.id_local_partner = vpscl.local_id
                      and tlg.last_gather_dt >= vpscl.min_gather_dt
                      and tlg.last_gather_dt < vpscl.max_gather_dt
    group by
        tlg.country, tlg.id_external_init, tlg.id_external, tlg.source_id

    union all

    select
        tar.country,
        tar.id_external_init,
        tar.id_external,
        tar.source_id,
        tar.request_datetime,
        max(case when vpscl.field_name = 'cpc_ratio' then vpscl.field_value end)             as cpc_ratio,
        max(case when vpscl.field_name = 'worst_cpc_ratio' then vpscl.field_value end)       as worst_cpc_ratio,
        max(case when vpscl.field_name = 'min_cpc_in_usd' then vpscl.field_value end)        as min_cpc_in_usd,
        max(case when vpscl.field_name = 'local_flags' then vpscl.field_value end)::integer  as local_flags,
        max(case when vpscl.field_name = 'static_min_cpc' then vpscl.field_value end)        as static_min_cpc,
        max(case when vpscl.field_name = 'update_gap' then vpscl.field_value end)            as update_gap,
        max(case when vpscl.field_name = 'optimized_cpc_part' then vpscl.field_value end)    as optimized_cpc_part,
        max(case when vpscl.field_name = 'reassembled_jobs_part' then vpscl.field_value end) as reassembled_jobs_part
    from
        temp_api_request tar
        left join affiliate.v_partner_settings_change_log vpscl
                  on tar.source_id = vpscl.source_id
                      and tar.id_local_partner = vpscl.local_id
                      and tar.request_datetime >= vpscl.min_dt
                      and tar.request_datetime < vpscl.max_dt
    group by
        tar.country, tar.id_external_init, tar.id_external, tar.source_id, tar.request_datetime;

    -- FOR EACH CLICK FIND RELEVANT PROJECT & CAMPAIGN CPC RATIOS
    create temporary table temp_prod_click_cpc_ratio as
    select
        tlg.country,
        tlg.id_external_init,
        tlg.id_external,
        tlg.source_id,
        vpcrcl.project_id,
        vpcrcl.campaign_id,
        max(case when vpcrcl.field_name = 'cpc_ratio' then vpcrcl.field_value end)             as project_cpc_ratio,
        max(case when vpcrcl.field_name = 'id_campaign_cpc_ratio' then vpcrcl.field_value end) as id_campaign_cpc_ratio,
        max(case when vpcrcl.field_name = 'optimized_cpc_part' then vpcrcl.field_value end)    as optimized_cpc_part,
        max(case when vpcrcl.field_name = 'reassembled_jobs_part' then vpcrcl.field_value end) as reassembled_jobs_part
    from
        temp_click_afg_data_3 tlg
        left join affiliate.v_project_cpc_ratio_change_log vpcrcl
                  on tlg.source_id = vpcrcl.source_id
                      and tlg.id_local_partner = vpcrcl.local_id
                      and tlg.last_gather_dt >= vpcrcl.min_gather_dt
                      and tlg.last_gather_dt < vpcrcl.max_gather_dt
    group by
        tlg.country, tlg.id_external_init, tlg.id_external, tlg.source_id, vpcrcl.project_id, vpcrcl.campaign_id

    union all

    select
        tar.country,
        tar.id_external_init,
        tar.id_external,
        tar.source_id,
        vpcrcl.project_id,
        vpcrcl.campaign_id,
        max(case when vpcrcl.field_name = 'cpc_ratio' then vpcrcl.field_value end)             as project_cpc_ratio,
        max(case when vpcrcl.field_name = 'id_campaign_cpc_ratio' then vpcrcl.field_value end) as id_campaign_cpc_ratio,
        max(case when vpcrcl.field_name = 'optimized_cpc_part' then vpcrcl.field_value end)    as optimized_cpc_part,
        max(case when vpcrcl.field_name = 'reassembled_jobs_part' then vpcrcl.field_value end) as reassembled_jobs_part
    from
        temp_api_request tar
        left join affiliate.v_project_cpc_ratio_change_log vpcrcl
                  on tar.source_id = vpcrcl.source_id
                      and tar.id_local_partner = vpcrcl.local_id
                      and tar.request_datetime >= vpcrcl.min_dt
                      and tar.request_datetime < vpcrcl.max_dt
    group by
        tar.country, tar.id_external_init, tar.id_external, tar.source_id, vpcrcl.project_id, vpcrcl.campaign_id;

-- JOIN CLICK DATA WITH DATES OF JOBS DELETING FROM FEEDS & APPROPRIATE CLICK PRICE
    create temporary table temp_result as
    select
        c.country,
        co.id                                                                                     as id_country,
        c.date_diff,
        c.partner,
        c.id_external_source,
        c.id_traffic_source,
        c.id_current_traffic_source,
        c.user_country,
        c.is_bot,
        c.is_mobile,
        c.is_returned,
        c.user_ip,
        c.uid_job                                                                                 as uid_job,
        c.id_project,
        c.id_campaign,
        0                                                                                         as is_duplicated,
        case
            when c.id_project is null or
                 (olp.is_only_local is null
                     or (c.user_country::text = lower(co.alpha_2::text)
                         or c.user_country::text = 'gb'::text and lower(co.alpha_2::text) = 'uk'::text
                      )
                     or olp.allow_nonlocal_traffic::text = 'partially allowed'::text
                      and (c.user_country is not null and not (c.user_country::text = lower(co.alpha_2::text)
                             or c.user_country::text = 'gb'::text and lower(co.alpha_2::text) = 'uk'::text))
                      and olpc.allowed_countries::text is not null)
                then 1
            when olp.allow_nonlocal_traffic::text = 'not allowed'::text
                     and (c.user_country is not null and not (c.user_country::text = lower(co.alpha_2::text)
                    or c.user_country::text = 'gb'::text and lower(co.alpha_2::text) = 'uk'::text))
                or olp.allow_nonlocal_traffic::text = 'partially allowed'::text
                     and (c.user_country is not null and not (c.user_country::text = lower(co.alpha_2::text)
                        or c.user_country::text = 'gb'::text and lower(co.alpha_2::text) = 'uk'::text))
                     and olpc.allowed_countries::text is null
                then 0
            else null::integer
            end                                                                                   as is_project_accepted_location,
        c.client_id_currency,
        c.is_external_stat_client,
        c.source,
        c.datetime                                                                                as click_datetime,
        c.id_external,
        c.id_external_init,
        c.external_flags,
        tcc.inactivation_date,
        c.id_away,
        c.id_jdp,
        c.id_jdp_away,
        c.has_away_conversion,
        c.has_jdp_away_conversion,
        tcc.cpc_change_date,
        c.partner_cost,
        null::numeric                                                                             as partner_cost_usd, -- will be updated on the next step using partner_cost with data on currency rates
        case
            when c.is_api_publisher = 1 then c.partner_cost
            -- for inactive jobs without cpc in click_price_change, take click_price from job_in_feed_log
            when coalesce(tpcs.static_min_cpc, 0) = 0 and coalesce(tcc.click_price_usd, 0) = 0
                then tcc.feed_log_click_price
            end                                                                                   as feed_cost,
        case
            when c.is_api_publisher = 1 then null
            -- optimal cpc ratio:
            -- if (project-level optimized_cpc_part is present
            -- or project-level optimized_cpc_part is null, but publisher-level optimized_cpc_part is present)
            -- and uid falls into the optimized group then use optimal_cpc_ratio * click_price_usd as feed_cost_usd
            when tcc.optimal_cpc_ratio is not null and
                 (coalesce(fm_cpc_proj.is_applied, fm_cpc_pub.is_applied) is true
                     or coalesce(cr_project.optimized_cpc_part, tpcs.optimized_cpc_part) = 1)
                then tcc.click_price_usd * tcc.optimal_cpc_ratio
            -- client_cpc < min_cpc and flag for static cpc is present
            when tpcs.local_flags & 16 = 16 and tcc.click_price_usd < tpcs.min_cpc_in_usd
                then tpcs.min_cpc_in_usd * tpcs.cpc_ratio
            -- client_cpc < min_cpc and flag for static cpc ratio is present
            when tpcs.local_flags & 32 = 32 and tcc.click_price_usd < tpcs.min_cpc_in_usd
                then tcc.click_price_usd * tpcs.worst_cpc_ratio
            when tpcs.static_min_cpc = 1
                then tpcs.min_cpc_in_usd * tpcs.cpc_ratio
            else tcc.click_price_usd *
                 coalesce(cr_campaign.id_campaign_cpc_ratio, cr_project.project_cpc_ratio, tpcs.cpc_ratio)
            end                                                                                   as feed_cost_usd,
        case
            when c.is_api_publisher = 1 then 0
            -- client_cpc < min_cpc and flag for static cpc is present
            when tpcs.local_flags & 16 = 16 and tcc.click_price_usd < tpcs.min_cpc_in_usd then 1
            -- client_cpc < min_cpc and flag for static cpc ratio is present
            when tpcs.local_flags & 32 = 32 and tcc.click_price_usd < tpcs.min_cpc_in_usd then 2
            else 0
            end + case
                      when tcc.optimal_cpc_ratio is not null
                          and (coalesce(fm_cpc_proj.is_applied, fm_cpc_pub.is_applied) is true
                              or coalesce(cr_project.optimized_cpc_part, tpcs.optimized_cpc_part) = 1)
                          then 8
                      else 0
            end + case
                      when coalesce(fm_jobad_proj.is_applied, fm_jobad_pub.is_applied) is true
                          or coalesce(cr_project.reassembled_jobs_part, tpcs.reassembled_jobs_part) = 1
                          then 16
                      else 0
            end                                                                                   as affiliate_flags,
        c.revenue,
        c.revenue_usd,
        c.id_sub_client,
        c.is_internal_duplicated,
        c.session_flags,
        tpcs.update_gap,
        c.feed_type,
        tpcs.request_datetime,
        c.is_api_publisher,
        c.id_local_partner,
        c.partner_id_currency,
        c.is_rounded_cpc,
        tpcs.min_cpc_in_usd,
        tpcs.local_flags,
        coalesce(cr_campaign.id_campaign_cpc_ratio, cr_project.project_cpc_ratio, tpcs.cpc_ratio) as cpc_ratio,
        c.id_category,
        tcc.gather_id_campaign,
        tcc.last_gather_dt,
        ac.id_project                                                                             as gather_id_project,
        c.redirected_to_uid,
        c.pub_tracking_id,
        c.api_request_id,
        c.url_extra_flags
    from
        temp_click_data c
        join dimension.countries co
             on lower(co.alpha_2) = c.country
        left join temp_click_afg_data_3 tcc
                  on tcc.source_id = c.source_id
                      and c.country = tcc.country
                      and c.id_external_init = tcc.id_external_init
                      and c.id_external = tcc.id_external
        left join imp.auction_campaign ac
                  on co.id = ac.country_id
                      and tcc.gather_id_campaign = ac.id
        left join temp_prod_click_settings tpcs
                  on c.source_id = tpcs.source_id
                      and c.country = tpcs.country
                      and c.id_external_init = tpcs.id_external_init
                      and c.id_external = tpcs.id_external
        left join temp_prod_click_cpc_ratio cr_project
                  on c.source_id = cr_project.source_id
                      and c.country = cr_project.country
                      and c.id_external_init = cr_project.id_external_init
                      and c.id_external = cr_project.id_external
                      and coalesce(ac.id_project, c.id_project) = cr_project.project_id
                      and cr_project.campaign_id = 0
        left join temp_prod_click_cpc_ratio cr_campaign
                  on c.source_id = cr_campaign.source_id
                      and c.country = cr_campaign.country
                      and c.id_external_init = cr_campaign.id_external_init
                      and c.id_external = cr_campaign.id_external
                      and coalesce(ac.id_project, c.id_project) = cr_campaign.project_id
                      and coalesce(tcc.gather_id_campaign, c.id_campaign) = cr_campaign.campaign_id
                      and cr_campaign.campaign_id != 0
        left join affiliate.functionality_matrix fm_cpc_proj
                  on fm_cpc_proj.functionality = 'cpc optimizer'
                      and c.uid_job_remainder = fm_cpc_proj.remainder
                      and cr_project.optimized_cpc_part = fm_cpc_proj.percent
        left join affiliate.functionality_matrix fm_cpc_pub
                  on fm_cpc_pub.functionality = 'cpc optimizer'
                      and c.uid_job_remainder = fm_cpc_pub.remainder
                      and tpcs.optimized_cpc_part = fm_cpc_pub.percent
        left join affiliate.functionality_matrix fm_jobad_proj
                  on fm_jobad_proj.functionality = 'job reassembler'
                      and c.uid_job_remainder = fm_jobad_proj.remainder
                      and cr_project.reassembled_jobs_part = fm_jobad_proj.percent
        left join affiliate.functionality_matrix fm_jobad_pub
                  on fm_jobad_pub.functionality = 'job reassembler'
                      and c.uid_job_remainder = fm_jobad_pub.remainder
                      and tpcs.reassembled_jobs_part = fm_jobad_pub.percent
        left join temp_only_local_projects olp
                  on olp.request_date = cast(c.datetime as date)
                      and olp.project_id = c.id_project
                      and olp.country_code::text = c.country
        left join temp_olpc olpc
                  on olpc.request_date = cast(c.datetime as date)
                      and olpc.project_id = c.id_project
                      and olpc.country_code::text = c.country
                      and olpc.allowed_countries::text = c.user_country::text;


    -- CALCULATE FEED COST FROM FEED COST USD
    create temporary table temp_cpc_change_curr_rate as
    with
        currency_dict as (
            select distinct
                tr.id_country,
                cast(coalesce(tr.cpc_change_date, tr.click_datetime) as date) as date,
                tr.id_local_partner,
                tr.partner_id_currency                                        as id_currency,
                tr.is_rounded_cpc
            from
                temp_result tr
        )
    select distinct on (t.date, t.id_country, t.id_currency, t.id_local_partner, t.is_rounded_cpc)
        t.date,
        t.id_country,
        t.id_currency,
        t.id_local_partner,
        t.is_rounded_cpc,
        ich.value_to_usd
    from
        currency_dict t
        left join dimension.info_currency_history ich
                  on ich.country = t.id_country
                      and ich.id_currency = t.id_currency
                      and cast(ich.date as date) <= t.date
    where
        value_to_usd != 0
    order by
        t.date, t.id_country, t.id_currency, t.id_local_partner, t.is_rounded_cpc, ich.date desc;


    update temp_result tr
    set
        feed_cost = case
                        when rate.is_rounded_cpc = 1
                            then floor((feed_cost_usd / rate.value_to_usd) * 100) / 100
                        else feed_cost_usd / rate.value_to_usd end
    from
        temp_cpc_change_curr_rate rate
    where
          tr.id_country = rate.id_country
      and tr.id_local_partner = rate.id_local_partner
      and cast(coalesce(tr.cpc_change_date, tr.click_datetime) as date) = rate.date
      and feed_cost is null;

    -- TRANSFORM COST (FEED, PARTNER) FROM PARTNER CURRENCY TO USD
    create temporary table temp_click_curr_rate as
    with
        currency_dict as (
            select distinct
                tr.id_country,
                public.fn_get_timestamp_from_date_diff(tr.date_diff)::date as date,
                tr.id_local_partner,
                tr.partner_id_currency                                     as id_currency
            from
                temp_result tr
        )
    select distinct on (t.date, t.id_country, t.id_currency, t.id_local_partner)
        t.date,
        t.id_country,
        t.id_currency,
        t.id_local_partner,
        ich.value_to_usd
    from
        currency_dict t
        left join dimension.info_currency_history ich
                  on ich.country = t.id_country
                      and ich.id_currency = t.id_currency
                      and cast(ich.date as date) <= t.date
    where
        value_to_usd != 0
    order by
        t.date, t.id_country, t.id_currency, t.id_local_partner, ich.date desc;

    update temp_result tr
    set
        feed_cost_usd = feed_cost * rate.value_to_usd
    from
        temp_click_curr_rate rate
    where
          tr.id_country = rate.id_country
      and tr.id_local_partner = rate.id_local_partner
      and public.fn_get_date_from_date_diff(tr.date_diff)::date = rate.date;

    update temp_result tr
    set
        partner_cost_usd = partner_cost * rate.value_to_usd
    from
        temp_click_curr_rate rate
    where
          tr.id_country = rate.id_country
      and tr.id_local_partner = rate.id_local_partner
      and public.fn_get_date_from_date_diff(tr.date_diff)::date = rate.date;

    update temp_result
    set
        affiliate_flags = affiliate_flags +
                          case
                              when local_flags & 16 = 16 then 1
                              when local_flags & 32 = 32 then 2
                              else 0 end
    where
          is_api_publisher = 1
      and cpc_ratio > 0
      and (feed_cost_usd / cpc_ratio) < min_cpc_in_usd;

    create temporary table temp_duplicated_clicks as
    with
        t as (
            select distinct
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                is_bot
            from
                affiliate.statistics_daily_raw a
            where
                  a.date_diff = _datediff - 1
              and is_duplicated = 0

            union all

            select distinct
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                is_bot
            from
                temp_result temp
        ),
        t1 as (
            select
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                is_bot,
                extract(epoch from (click_datetime - lag(click_datetime, 1)
                                                     over (partition by id_country, partner, uid_job, user_ip order by click_datetime))) as diff
            from
                t
            where
                is_bot != 1
        ),
        t2 as (
            select
                date_diff,
                id_country,
                partner,
                id_external_init,
                id_external,
                click_datetime,
                uid_job,
                user_ip,
                diff,
                case
                    when sum(diff) over (partition by id_country, partner, uid_job, user_ip
                        order by click_datetime) - (3600 * 24) >= 0
                        then 1
                    else 0
                    end as is_more_than_24h
            from
                t1
        ),
        t3 as (
            select
                t2.*,
                coalesce(is_more_than_24h -
                         lag(is_more_than_24h) over (partition by id_country, partner, uid_job, user_ip
                             order by click_datetime), 0) as is_next_day_valid_click
            from
                t2
        )
    select
        id_country,
        date_diff,
        id_external_init,
        id_external
    from
        t3
    where
          diff <= (3600 * 24)
      and is_next_day_valid_click = 0;

    update temp_result tmp
    set
        is_duplicated = 1
    from
        temp_duplicated_clicks dc
    where
          tmp.date_diff = dc.date_diff
      and tmp.id_country = dc.id_country
      and tmp.id_external = dc.id_external
      and tmp.id_external is not null
      and tmp.date_diff = _datediff;

    update temp_result tmp
    set
        is_duplicated = 1
    from
        temp_duplicated_clicks dc
    where
          tmp.date_diff = dc.date_diff
      and tmp.id_country = dc.id_country
      and tmp.id_external_init = dc.id_external_init
      and tmp.id_external is null
      and tmp.date_diff = _datediff;

    create temporary table temp_result_data as
    select
        date_diff,
        id_country,
        country,
        partner,
        id_external_source,
        id_traffic_source,
        id_current_traffic_source,
        user_country,
        is_bot,
        is_mobile,
        is_returned,
        uid_job,
        id_project,
        client_id_currency,
        source,
        click_datetime,
        case when id_external != 0 then id_external end as id_external,
        external_flags,
        is_project_accepted_location,
        inactivation_date,
        id_away,
        id_jdp,
        id_jdp_away,
        has_away_conversion,
        has_jdp_away_conversion,
        cpc_change_date,
        partner_cost,
        partner_cost_usd,
        feed_cost,
        feed_cost_usd,
        revenue,
        revenue_usd,
        user_ip,
        is_external_stat_client,
        id_external_init,
        id_campaign,
        is_duplicated,
        is_internal_duplicated,
        id_sub_client,
        affiliate_flags + case
                              when session_flags & 8 = 8 or session_flags & 32 = 32 then 4
                              else 0 end                as affiliate_flags,
        case
            when is_api_publisher = 1 and
                 ((request_datetime + interval '1 hour' * coalesce(update_gap, 0)) < click_datetime)
                then 0
            when is_api_publisher = 1 then 1
            when ((inactivation_date + interval '1 hour' * coalesce(update_gap, 0)) < click_datetime
                and (inactivation_date > cpc_change_date or cpc_change_date is null))
                or (feed_type = 'paid' and
                    (inactivation_date is null and cpc_change_date is null and feed_cost is null))
                then 0
            else 1
            end                                         as is_billable,
        id_category,
        gather_id_campaign,
        last_gather_dt,
        redirected_to_uid,
        pub_tracking_id,
        api_request_id,
        url_extra_flags
    from
        temp_result r;

    insert into
        affiliate.statistics_daily_raw(date_diff, id_country, partner, id_external_source, id_traffic_source,
                                       id_current_traffic_source, user_country, is_bot, is_mobile, is_returned, uid_job,
                                       id_project, client_id_currency, source, click_datetime, id_external,
                                       external_flags, inactivation_date, id_away, id_jdp, id_jdp_away,
                                       has_away_conversion, has_jdp_away_conversion, cpc_change_date, partner_cost,
                                       partner_cost_usd, feed_cost, feed_cost_usd, revenue, revenue_usd, user_ip,
                                       is_external_stat_client, id_external_init, id_campaign, is_duplicated,
                                       is_internal_duplicated, id_sub_client, affiliate_flags, is_billable, id_category,
                                       last_gather_id_campaign, last_gather_datetime, redirected_to_uid, click_type,
                                       is_project_accepted_location, pub_tracking_id, api_request_id, url_extra_flags)
    select
        date_diff,
        id_country,
        partner,
        id_external_source,
        id_traffic_source,
        id_current_traffic_source,
        user_country,
        is_bot,
        is_mobile,
        is_returned,
        uid_job,
        id_project,
        client_id_currency,
        source,
        click_datetime,
        id_external,
        external_flags,
        inactivation_date,
        id_away,
        id_jdp,
        id_jdp_away,
        has_away_conversion,
        has_jdp_away_conversion,
        cpc_change_date,
        partner_cost,
        partner_cost_usd,
        feed_cost,
        feed_cost_usd,
        revenue,
        revenue_usd,
        user_ip,
        is_external_stat_client,
        id_external_init,
        id_campaign,
        is_duplicated,
        is_internal_duplicated,
        id_sub_client,
        affiliate_flags,
        is_billable,
        id_category,
        gather_id_campaign,
        last_gather_dt,
        redirected_to_uid,
        case
            when r.affiliate_flags & 4 = 4
                and not (r.user_country::text = lower(r.country)
                    or r.user_country::text = 'gb'::text and lower(r.country) = 'uk'::text)
                then 4
            when r.is_bot = 1 then 1
            when r.is_duplicated = 1 then 2
            when r.is_billable = 0 then 3
            when r.is_project_accepted_location = 0 then 4
            else 0 end as click_type,
        is_project_accepted_location,
        pub_tracking_id,
        api_request_id,
        url_extra_flags
    from
        temp_result_data r;


    drop table if exists temp_click_data;
    drop table if exists temp_click_ids;
    drop table if exists temp_last_job_feed_changes;
    drop table if exists temp_click_afg_data_1;
    drop table if exists temp_click_afg_data_2;
    drop table if exists temp_cpc_changes;
    drop table if exists temp_click_afg_data_3;
    drop table if exists temp_api_request;
    drop table if exists temp_prod_click_settings;
    drop table if exists temp_prod_click_cpc_ratio;
    drop table if exists temp_cpc_change_curr_rate;
    drop table if exists temp_click_curr_rate;
    drop table if exists temp_result;
    drop table if exists temp_duplicated_clicks;
    drop table if exists temp_cpc_changes_total;
    drop table if exists temp_cpc_changes_unique;
    drop table if exists temp_result_data;
    drop table if exists temp_only_local_projects;
    drop table if exists temp_olpc;

end;

$$;

alter procedure affiliate.insert_statistics_daily_raw(integer) owner to ypr;
