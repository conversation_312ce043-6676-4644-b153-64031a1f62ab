create procedure insert_call_funnel_job_seeker(_datediff integer)
    language plpgsql
as
$$
begin

        create temp table temp_jdp_call_action as
        select sja.country as country_id,
               sja.date_diff as action_datediff,
               sja.id_jdp as jdp_id,
               max(case when sja.type in (13, 19, 21, 34) /* show phone contact click */ then 1 else 0 end)      as is_phone_show_click,
               max(case when sja.type = 56 /* call */ then 1 else 0 end)                                         as is_call,
               max(case when sja.type = 56 /* call */
                         and sja.flags & (64 + 32) <> 0  /* Busy or Voicemail = rejected */ then 1 else 0 end)   as is_call_rejected,
               max(case when sja.type = 56 /* call */
                         and sja.flags & 8 = 8  /* NoAnswer = missed */ then 1 else 0 end)                       as is_call_missed,
               max(case when sja.type = 56 /* call */ and sja.flags & 128 = 128 /* Failed */ then 1 else 0 end)  as is_call_failed,
               max(case when sja.type = 56 /* call */ and sja.flags & 4 = 4 /* Repeated */ then 1 else 0 end)    as is_call_repeated_after_proper,
               max(case when sja.type = 56 /* call */
                         and sja.flags & (1+2) <> 0 /* Answered or Answered Proper */  then 1 else 0 end)        as is_call_answered,
               max(case when sja.type = 56 /* call */
                         and sja.flags & 2 = 2 /* Answered Proper */ then 1 else 0 end)                          as is_call_answered_proper
        from  imp.session_jdp_action sja
        where sja.country = 1
          and sja.date_diff between _datediff and _datediff
            and sja.type in (13, 19, 21, 34, 56)
        group by sja.country,
                 sja.date_diff,
                 sja.id_jdp;


        alter table temp_jdp_call_action
           add constraint temp_jdp_call_action_pk
              primary key (country_id, action_datediff, jdp_id);


        insert into job_seeker.call_funnel_job_seeker(country_id, jdp_viewed_datediff, employer_id,
                                                      has_ukrainian_phone_on_jdp, jdp_view_cnt, phone_show_click_cnt,
                                                      call_cnt, call_failed_cnt, call_missed_cnt, call_rejected_cnt,
                                                      call_repeated_after_proper_cnt, call_answered_cnt,
                                                      call_answered_proper_cnt)
        select sj.country                         as country_id,
               sj.date_diff                       as jdp_viewed_datediff,
               e.id                               as employer_id,
               (case when left(split_part(phones, ';',1), 4) = '+380' then 1 else 0 end) as has_ukrainian_phone_on_jdp,
               count(distinct sj.id)              as jdp_view_cnt,
               sum(is_phone_show_click)           as phone_show_click_cnt,
               sum(is_call)                       as call_cnt,
               sum(is_call_failed)                as call_failed_cnt,
               sum(is_call_missed)                as call_missed_cnt,
               sum(is_call_rejected)              as call_rejected_cnt,
               sum(is_call_repeated_after_proper) as call_repeated_after_proper_cnt,
               sum(is_call_answered)              as call_answered_cnt,
               sum(is_call_answered_proper)       as call_answered_proper_cnt
        from imp.session_jdp sj
             left join temp_jdp_call_action sja
             on sja.country_id = sj.country
                and sja.action_datediff = sj.date_diff
                and sja.jdp_id = sj.id
              -- додавання роботодавця
             join  imp_employer.job_to_uid_mapping jtum
             on sj.country = 1
                and jtum.sources = 1
                and sj.uid_job = jtum.uid_job
             join imp_employer.job j
             on jtum.sources = j.sources
                and jtum.id_job = j.id
             join imp_employer.employer e
             on j.sources = e.sources
                and j.id_employer = e.id
        where sj.country = 1
          and sj.date_diff between _datediff and _datediff
          and sj.job_id_project = -1 /* DTE jobs only */
          and j.phones is not null   /* phone click is available  */
        group by sj.country,
                 sj.date_diff,
                 e.id,
                 (case when left(split_part(phones, ';',1), 4) = '+380' then 1 else 0 end);

        drop table temp_jdp_call_action;

    end;

$$;


