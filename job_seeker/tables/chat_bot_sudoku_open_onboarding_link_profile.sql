create table kpav.chat_bot_sudoku_open_onboarding_link_profile as
select 1                                     as country_id,

       ac.account_id,
       a.profile_id,
       min(fn_get_date_diff(sml.created_on)) as first_open_datediff
from imp_statistic.js_message_status_log msl
         join imp_statistic.js_message_status ms
              on msl.message_status_id = ms.id
         join imp_statistic.js_sent_message_log sml
              on msl.sent_message_log_id = sml.id
         join imp_statistic.js_account_communications ac
              on sml.account_communication_id = ac.id
         join imp_statistic.js_account a
              on ac.account_id = a.id

where template_id = 17
  and sml.created_on >= '2022-01-17'
  and msl.message_status_id = 4 /*open link*/
group by 1, 2, 3
