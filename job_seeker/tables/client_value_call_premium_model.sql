select date_trunc('month',ucc.jdp_viewed_datetime)::date as month_first_date,
        j.id_employer as employer_id,
       count(distinct ucc.jdp_id) as click_call_cnt
from job_seeker.user_call_click ucc
     join  imp_employer.job_to_uid_mapping jtum
     on ucc.country_id = 1
        and jtum.sources = 1
        and ucc.job_uid = jtum.uid_job
     join imp_employer.job j
     on jtum.sources = j.sources
        and jtum.id_job = j.id
where ucc.jdp_viewed_datediff between fn_get_date_diff('2021-01-01') and fn_get_date_diff('2021-07-31')
 group by j.id_employer,
          date_trunc('month',ucc.jdp_viewed_datetime);
