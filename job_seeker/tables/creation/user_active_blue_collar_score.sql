create table job_seeker.user_active_blue_collar_score
(
    user_id             varchar(255) not null,
    user_type_id        smallint     not null,
    score               double precision,
    activation_datediff integer,
    activation_datetime timestamp,
    country_id          integer      not null,
    constraint pk_user_blue_collar_score_id
        primary key (country_id, user_id, user_type_id)
);

alter table job_seeker.user_active_blue_collar_score
    owner to postgres;

create index ind_user_blue_collar_score_d
    on job_seeker.user_active_blue_collar_score (activation_datediff);
