create table job_seeker.user_address_show_on_map
(
    user_id              varchar(255) not null,
    user_type_id         smallint     not null,
    usage_first_datediff integer,
    usage_first_datetime timestamp,
    usage_cnt            integer,
    country_id           integer      not null,
    constraint user_address_show_on_map_pk
        primary key (country_id, user_id, user_type_id)
);

alter table job_seeker.user_address_show_on_map
    owner to postgres;

create index ind_user_address_show_on_map_u
    on job_seeker.user_address_show_on_map (user_id, user_type_id);
