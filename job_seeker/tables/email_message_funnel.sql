-- Воронка відкриття повідомлень
create table job_seeker.email_message_funnel as
select es.country                    as country_id,
       es.date_diff                  as sent_datediff,
       es.letter_type                as letter_type_id,
       count(distinct es.id_message) as emai_sent_cnt,
       count(distinct eo.id_message) as email_open_cnt,
       count(distinct es.id_account) as users_cnt

from imp.email_sent es
         left join imp.email_open eo
                   on es.country = eo.country
                       and es.id_message = eo.id_message
                       and es.id_account = eo.id_account

where es.country in (1, 10)
  and es.date_diff between 44407 and fn_get_date_diff(current_date) - 1
group by 1, 2, 3;
