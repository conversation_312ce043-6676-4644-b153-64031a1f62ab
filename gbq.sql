select load_date,
       count(*) as cnt
from `jooble-prj.auction_extended_statistics.conversions`
group by load_date
order by load_date desc
;


select date, count(*)
from `jooble-prj.auction_extended_statistics.budgets`
where date >= '2025-06-01'
group by date
order by date desc
;



select date, count(*)
from `jooble-prj.auction_extended_statistics.clicks`
where date >= '2025-06-15'
group by date
order by date desc
;


select date, count(*)
from `jooble-prj.auction_extended_statistics.jobs`
group by date
order by date desc
;


select date, count(*)
from `jooble-prj.auction_extended_statistics.info_currency_history`
group by date
order by date desc
;



select count(*)
from `jooble-prj.auction_extended_statistics.auction_user`
;