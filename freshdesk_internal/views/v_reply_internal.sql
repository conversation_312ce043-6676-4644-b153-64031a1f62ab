create view freshdesk_internal.v_reply_internal
            (ticket_system, ticket_id, agent_name, is_first_reply, reply_time_start_type, reply_time_start, reply_at,
             reply_time_min)
as
with
    working_hours as (
        select
                '00:00:00'::time without time zone +
                business_hours.start_hour::double precision * '01:00:00'::interval as start_time,
                '00:00:00'::time without time zone + business_hours.end_hour::double precision * '01:00:00'::interval -
                '00:00:01'::interval                                               as end_time
        from
            freshdesk_internal.business_hours
        where
            business_hours.team_id = 1
    ),
    work_calendar_prep as (
        select
            t.x     as datetime,
            case
                when t.x::time without time zone >= wh.start_time and t.x::time without time zone <= wh.end_time and
                     date_part('dow'::text, t.x) >= 1::double precision and
                     date_part('dow'::text, t.x) <= 5::double precision then 1
                else 0
                end as is_working_hours
        from
            generate_series((current_date - 401)::timestamp with time zone,
                            (current_date + 1)::timestamp with time zone, '01:00:00'::interval) t(x)
            cross join working_hours wh
    ),
    work_calendar as (
        select
            work_calendar_prep.datetime,
            work_calendar_prep.is_working_hours,
            lead(work_calendar_prep.is_working_hours) over () as is_next_working_hour,
            lag(work_calendar_prep.is_working_hours) over ()  as is_prev_working_hour
        from
            work_calendar_prep
    ),
    reply_data_raw as (
        select
            t.id                                                                          as ticket_id,
            a.name                                                                        as agent_name,
            a.team_id                                                                     as agent_team_id,
            timezone('europe/kiev'::text, timezone('utc'::text, t.created_at))            as ticket_created_at,
            timezone('europe/kiev'::text, timezone('utc'::text, r.requester_response_at)) as requester_response_at,
            timezone('europe/kiev'::text, timezone('utc'::text, r.created_at))            as reply_at
        from
            freshdesk_internal.replies r
            join freshdesk_internal.tickets t
                 on r.ticket_id = t.id
            left join freshdesk_internal.agents a
                      on r.agent_id = a.id
        where
                timezone('europe/kiev'::text, timezone('utc'::text, t.created_at))::date >= (current_date - 401)
    ),
    reply_data_prep as (
        select
            reply_data_raw.ticket_id,
            reply_data_raw.agent_name,
            reply_data_raw.ticket_created_at,
            reply_data_raw.requester_response_at,
            reply_data_raw.agent_team_id,
            lag(reply_data_raw.reply_at)
            over (partition by reply_data_raw.ticket_id, reply_data_raw.requester_response_at order by reply_data_raw.reply_at) as previous_reply_at,
            reply_data_raw.reply_at,
            case
                when row_number()
                     over (partition by reply_data_raw.ticket_id, reply_data_raw.requester_response_at order by reply_data_raw.reply_at) =
                     1 then
                    case
                        when reply_data_raw.requester_response_at is not null then 'author response'::text
                        else 'ticket created'::text
                        end
                else 'previous agent reply'::text
                end                                                                                                             as reply_time_start_type
        from
            reply_data_raw
    ),
    reply_data as (
        select
            reply_data_prep.ticket_id,
            reply_data_prep.agent_name,
            reply_data_prep.reply_at,
            case
                when row_number() over (partition by reply_data_prep.ticket_id order by reply_data_prep.reply_at) = 1
                    then 1
                else 0
                end as is_first_reply,
            reply_data_prep.reply_time_start_type,
            case
                when reply_data_prep.reply_time_start_type = 'author response'::text
                    then reply_data_prep.requester_response_at
                when reply_data_prep.reply_time_start_type = 'previous agent reply'::text
                    then reply_data_prep.previous_reply_at
                else reply_data_prep.ticket_created_at
                end as reply_time_start
        from
            reply_data_prep
        where
            reply_data_prep.agent_team_id = 1
    ),
    reply_calendar as (
        select
            rd.ticket_id,
            rd.agent_name,
            rd.reply_at,
            rd.reply_time_start_type,
            wc.datetime,
            wc.is_working_hours,
            rd.is_first_reply,
            rd.reply_time_start,
            case
                when wc.datetime = min(wc.datetime) over (partition by rd.ticket_id, rd.reply_at) then 1
                else 0
                end as is_first_row,
            case
                when wc.datetime = max(wc.datetime) over (partition by rd.ticket_id, rd.reply_at) then 1
                else 0
                end as is_last_row
        from
            reply_data rd
            left join work_calendar wc
                      on wc.datetime >= date_trunc('hour'::text, rd.reply_time_start) and wc.datetime <= rd.reply_at and
                         (wc.is_working_hours = 1 or wc.is_next_working_hour = 1 or wc.is_prev_working_hour = 1)
    ),
    reply_business_hours as (
        select
            reply_calendar.ticket_id,
            reply_calendar.agent_name,
            reply_calendar.reply_at,
            reply_calendar.datetime,
            reply_calendar.is_working_hours,
            reply_calendar.is_first_row,
            reply_calendar.is_last_row,
            reply_calendar.is_first_reply,
            reply_calendar.reply_time_start_type,
            reply_calendar.reply_time_start,
            (date_part('epoch'::text,
                       case
                           when reply_calendar.is_first_row = 1 then reply_calendar.is_working_hours::double precision *
                                                                     (coalesce(lead(reply_calendar.datetime)
                                                                               over (partition by reply_calendar.ticket_id, reply_calendar.reply_at order by reply_calendar.datetime),
                                                                               reply_calendar.reply_at::timestamp with time zone) -
                                                                      reply_calendar.reply_time_start::timestamp with time zone)
                           when reply_calendar.is_last_row = 1 then reply_calendar.is_working_hours::double precision *
                                                                    (reply_calendar.reply_at::timestamp with time zone -
                                                                     reply_calendar.datetime)
                           else reply_calendar.is_working_hours::double precision * (lead(reply_calendar.datetime)
                                                                                     over (partition by reply_calendar.ticket_id, reply_calendar.reply_at order by reply_calendar.datetime) -
                                                                                     reply_calendar.datetime)
                           end) / 60::double precision)::numeric as reply_time_min
        from
            reply_calendar
    )
select
    'internal'::text           as ticket_system,
    reply_business_hours.ticket_id,
    reply_business_hours.agent_name,
    reply_business_hours.is_first_reply,
    reply_business_hours.reply_time_start_type,
    reply_business_hours.reply_time_start,
    reply_business_hours.reply_at,
    round(sum(
                  case
                      when reply_business_hours.reply_time_min >= 0::numeric then reply_business_hours.reply_time_min
                      else 0::numeric
                      end), 2) as reply_time_min
from
    reply_business_hours
group by
    reply_business_hours.ticket_id, reply_business_hours.agent_name, reply_business_hours.reply_at,
    reply_business_hours.is_first_reply, reply_business_hours.reply_time_start_type,
    reply_business_hours.reply_time_start;