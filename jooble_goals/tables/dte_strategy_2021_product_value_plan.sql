insert into jooble_goals.dte_strategy_2021_product_value_plan (session_traffic_source_group_id, is_session_with_profile_submitted, month_first_date, apply_plan_cnt, click_call_plan_cnt, profile_created_plan_cnt, session_plan_cnt)
values
-- organic with profile
(1, 1, '2021-04-01', 12500, 29800, 0, 165410),
(1, 1, '2021-05-01', 12500, 29800, 0, 165410),
(1, 1, '2021-06-01', 12500, 29800, 0, 165410),
(1, 1, '2021-07-01', 12500, 29800, 0, 165410),


(1, 1, '2021-08-01', 12500, 29800, 0, 165410),
(1, 1, '2021-09-01', 12500, 29800, 0, 165410),

(1, 1, '2021-10-01', 15000, 36000, 0, 189040),
(1, 1, '2021-11-01', 15000, 36000, 0, 189040),
(1, 1, '2021-12-01', 15000, 36000, 0, 189040),

(1, 1, '2022-01-01', 21800, 52200, 0, 283600),
(1, 1, '2022-02-01', 21800, 52200, 0, 283600),
(1, 1, '2022-03-01', 21800, 52200, 0, 283600),

(1, 1, '2022-04-01', 26300, 63000, 0, 311960),
(1, 1, '2022-05-01', 26300, 63000, 0, 311960),
(1, 1, '2022-06-01', 26300, 63000, 0, 311960),

(1, 1, '2022-07-01', 38100, 91300, 0, 442390),

-- organic without profile
(1, 0, '2021-04-01', 16700, 219800, 18410, 2197590),
(1, 0, '2021-05-01', 16700, 219800, 18410, 2197590),
(1, 0, '2021-06-01', 16700, 219800, 18410, 2197590),
(1, 0, '2021-07-01', 16700, 219800, 18410, 2197590),


(1, 0, '2021-08-01', 16700, 219800, 18410, 2197590),
(1, 0, '2021-09-01', 16700, 219800, 18410, 2197590),

(1, 0, '2021-10-01', 16700, 217200, 18190, 2173960),
(1, 0, '2021-11-01', 16500, 217200, 18190, 2173960),
(1, 0, '2021-12-01', 16500, 217200, 18190, 2173960),

(1, 0, '2022-01-01', 16500, 257100, 21540, 2552400),
(1, 0, '2022-02-01', 19500, 257100, 21540, 2552400),
(1, 0, '2022-03-01', 19500, 257100, 21540, 2552400),

(1, 0, '2022-04-01', 19500, 253100, 21190, 2524040),
(1, 0, '2022-05-01', 19200, 253100, 21190, 2524040),
(1, 0, '2022-06-01', 19200, 253100, 21190, 2524040),

(1, 0, '2022-07-01', 19200, 298000, 24960, 2960610),


-- paid acquisition with profile
(2, 1, '2021-04-01', 8800, 19700, 0, 75180),
(2, 1, '2021-05-01', 8800, 19700, 0, 75180),
(2, 1, '2021-06-01', 8800, 19700, 0, 75180),
(2, 1, '2021-07-01', 8800, 19700, 0, 75180),


(2, 1, '2021-08-01', 8800, 19700, 0, 75180),
(2, 1, '2021-09-01', 8800, 19700, 0, 75180),

(2, 1, '2021-10-01', 10600, 23800, 0, 85920),
(2, 1, '2021-11-01', 10600, 23800, 0, 85920),
(2, 1, '2021-12-01', 10600, 23800, 0, 85920),

(2, 1, '2022-01-01', 12800, 28700, 0, 107400),
(2, 1, '2022-02-01', 12800, 28700, 0, 107400),
(2, 1, '2022-03-01', 12800, 28700, 0, 107400),

(2, 1, '2022-04-01', 15500, 34700, 0, 118140),
(2, 1, '2022-05-01', 15500, 34700, 0, 118140),
(2, 1, '2022-06-01', 15500, 34700, 0, 118140),

(2, 1, '2022-07-01', 18700, 41900, 0, 139620),


-- paid acquisition without profile
( 2, 0, '2021-04-01', 11400, 118600, 13230, 998820),
( 2, 0, '2021-05-01', 11400, 118600, 13230, 998820),
( 2, 0, '2021-06-01', 11400, 118600, 13230, 998820),
( 2, 0, '2021-07-01', 11400, 118600, 13230, 998820),


( 2, 0, '2021-08-01', 11400, 118600, 13230, 998820),
( 2, 0, '2021-09-01', 11400, 118600, 13230, 998820),

( 2, 0, '2021-10-01', 11300, 117200, 13080, 988080),
( 2, 0, '2021-11-01', 11300, 117200, 13080, 988080),
( 2, 0, '2021-12-01', 11300, 117200, 13080, 988080),

( 2, 0, '2022-01-01', 11200, 115600, 12900, 966600),
( 2, 0, '2022-02-01', 11200, 115600, 12900, 966600),
( 2, 0, '2022-03-01', 11200, 115600, 12900, 966600),

( 2, 0, '2022-04-01', 11000, 113700, 12690, 955860),
( 2, 0, '2022-05-01', 11000, 113700, 12690, 955860),
( 2, 0, '2022-06-01', 11000, 113700, 12690, 955860),

( 2, 0, '2022-07-01', 10800, 111600, 12450, 934380),


-- email alerts with profile
(3, 1, '2021-04-01', 9200, 20300, 0, 199020),
(3, 1, '2021-05-01', 9200, 20300, 0, 199020),
(3, 1, '2021-06-01', 9200, 20300, 0, 199020),
(3, 1, '2021-07-01', 9200, 20300, 0, 199020),


(3, 1, '2021-08-01', 9200, 20300, 0, 199020),
(3, 1, '2021-09-01', 9200, 20300, 0, 199020),

(3, 1, '2021-10-01', 11200, 24500, 0, 231120),
(3, 1, '2021-11-01', 11200, 24500, 0, 231120),
(3, 1, '2021-12-01', 11200, 24500, 0, 231120),

(3, 1, '2022-01-01', 13500, 29600, 0, 269640),
(3, 1, '2022-02-01', 13500, 29600, 0, 269640),
(3, 1, '2022-03-01', 13500, 29600, 0, 269640),

(3, 1, '2022-04-01', 16300, 35700, 0, 308160),
(3, 1, '2022-05-01', 16300, 35700, 0, 308160),
(3, 1, '2022-06-01', 16300, 35700, 0, 308160),

(3, 1, '2022-07-01', 19700, 43100, 0, 353100),


-- email alerts without profile
(3, 0, '2021-04-01', 1900, 22300, 1630, 442980),
(3, 0, '2021-05-01', 1900, 22300, 1630, 442980),
(3, 0, '2021-06-01', 1900, 22300, 1630, 442980),
(3, 0, '2021-07-01', 1900, 22300, 1630, 442980),


(3, 0, '2021-08-01', 1900, 22300, 1630, 442980),
(3, 0, '2021-09-01', 1900, 22300, 1630, 442980),

(3, 0, '2021-10-01', 1800, 20700, 1520, 410880),
(3, 0, '2021-11-01', 1800, 20700, 1520, 410880),
(3, 0, '2021-12-01', 1800, 20700, 1520, 410880),

(3, 0, '2022-01-01', 1700, 19000, 1390, 372360),
(3, 0, '2022-02-01', 1700, 19000, 1390, 372360),
(3, 0, '2022-03-01', 1700, 19000, 1390, 372360),

(3, 0, '2022-04-01', 1500, 16900, 1240, 333840),
(3, 0, '2022-05-01', 1500, 16900, 1240, 333840),
(3, 0, '2022-06-01', 1500, 16900, 1240, 333840),

(3, 0, '2022-07-01', 1300, 14600, 1070, 288900),


-- profile active retention (only with profile)
-- in BA is product marketing? yes
(4, 1, '2021-04-01', 12100, 11100, 0, 39000),
(4, 1, '2021-05-01', 12100, 11100, 0, 39000),
(4, 1, '2021-06-01', 12100, 11100, 0, 39000),
(4, 1, '2021-07-01', 12100, 11100, 0, 39000),


(4, 1, '2021-08-01', 12100, 11100, 0, 39000),
(4, 1, '2021-09-01', 12100, 11100, 0, 39000),

(4, 1, '2021-10-01', 12700, 11700, 0, 39000),
(4, 1, '2021-11-01', 12700, 11700, 0, 39000),
(4, 1, '2021-12-01', 12700, 11700, 0, 39000),

(4, 1, '2022-01-01', 20000, 18400, 0, 59000),
(4, 1, '2022-02-01', 20000, 18400, 0, 59000),
(4, 1, '2022-03-01', 20000, 18400, 0, 59000),

(4, 1, '2022-04-01', 31500, 29000, 0, 88000),
(4, 1, '2022-05-01', 31500, 29000, 0, 88000),
(4, 1, '2022-06-01', 31500, 29000, 0, 88000),

(4, 1, '2022-07-01', 49600, 45700, 0, 132000),


-- other with profile
(0, 1, '2021-04-01', 7400, 13400, 0, 93000),
(0, 1, '2021-05-01', 7400, 13400, 0, 93000),
(0, 1, '2021-06-01', 7400, 13400, 0, 93000),
(0, 1, '2021-07-01', 7400, 13400, 0, 93000),


(0, 1, '2021-08-01', 7400, 13400, 0, 93000),
(0, 1, '2021-09-01', 7400, 13400, 0, 93000),

(0, 1, '2021-10-01', 9000, 16200, 0, 111600),
(0, 1, '2021-11-01', 9000, 16200, 0, 111600),
(0, 1, '2021-12-01', 9000, 16200, 0, 111600),

(0, 1, '2022-01-01', 10800, 19600, 0, 130200),
(0, 1, '2022-02-01', 10800, 19600, 0, 130200),
(0, 1, '2022-03-01', 10800, 19600, 0, 130200),

(0, 1, '2022-04-01', 13100, 23600, 0, 148800),
(0, 1, '2022-05-01', 13100, 23600, 0, 148800),
(0, 1, '2022-06-01', 13100, 23600, 0, 148800),

(0, 1, '2022-07-01', 15800, 28600, 0, 167400),


-- other without profile
(0, 0, '2021-04-01', 5700, 60300, 4420, 837000),
(0, 0, '2021-05-01', 5700, 60300, 4420, 837000),
(0, 0, '2021-06-01', 5700, 60300, 4420, 837000),
(0, 0, '2021-07-01', 5700, 60300, 4420, 837000),


(0, 0, '2021-08-01', 5700, 60300, 4420, 837000),
(0, 0, '2021-09-01', 5700, 60300, 4420, 837000),

(0, 0, '2021-10-01', 5600, 59200, 4350, 818400),
(0, 0, '2021-11-01', 5600, 59200, 4350, 818400),
(0, 0, '2021-12-01', 5600, 59200, 4350, 818400),

(0, 0, '2022-01-01', 5500, 58000, 4260, 799800),
(0, 0, '2022-02-01', 5500, 58000, 4260, 799800),
(0, 0, '2022-03-01', 5500, 58000, 4260, 799800),

(0, 0, '2022-04-01', 5300, 56700, 4160, 781200),
(0, 0, '2022-05-01', 5300, 56700, 4160, 781200),
(0, 0, '2022-06-01', 5300, 56700, 4160, 781200),

(0, 0, '2022-07-01', 5200, 55100, 4050, 762600),

(4, 0, '2021-04-01', 0, 0, 0, 0),
(4, 0, '2021-05-01', 0, 0, 0, 0),
(4, 0, '2021-06-01', 0, 0, 0, 0),
(4, 0, '2021-07-01', 0, 0, 0, 0),
(4, 0, '2021-08-01', 0, 0, 0, 0),
(4, 0, '2021-09-01', 0, 0, 0, 0),
(4, 0, '2021-10-01', 0, 0, 0, 0),
(4, 0, '2021-11-01', 0, 0, 0, 0),
(4, 0, '2021-12-01', 0, 0, 0, 0),
(4, 0, '2022-01-01', 0, 0, 0, 0),
(4, 0, '2022-02-01', 0, 0, 0, 0),
(4, 0, '2022-03-01', 0, 0, 0, 0),
(4, 0, '2022-04-01', 0, 0, 0, 0),
(4, 0, '2022-05-01', 0, 0, 0, 0),
(4, 0, '2022-06-01', 0, 0, 0, 0),
(4, 0, '2022-07-01', 0, 0, 0, 0);

-- add session_plan 2022 Q1
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 1635034 WHERE month_first_date = '2022-01-01' and session_traffic_source_group_id = 1 and is_session_with_profile_submitted = 0;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 260240 WHERE month_first_date = '2022-01-01' and session_traffic_source_group_id = 1 and is_session_with_profile_submitted = 1;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 909069 WHERE month_first_date = '2022-01-01' and session_traffic_source_group_id = 2 and is_session_with_profile_submitted = 0;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 145657 WHERE month_first_date = '2022-01-01' and session_traffic_source_group_id = 2 and is_session_with_profile_submitted = 1;

UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 1718172 WHERE month_first_date = '2022-02-01' and session_traffic_source_group_id = 1 and is_session_with_profile_submitted = 0;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 273473 WHERE month_first_date = '2022-02-01' and session_traffic_source_group_id = 1 and is_session_with_profile_submitted = 1;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 955293 WHERE month_first_date = '2022-02-01' and session_traffic_source_group_id = 2 and is_session_with_profile_submitted = 0;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 153063 WHERE month_first_date = '2022-02-01' and session_traffic_source_group_id = 2 and is_session_with_profile_submitted = 1;

UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 1829021 WHERE month_first_date = '2022-03-01' and session_traffic_source_group_id = 1 and is_session_with_profile_submitted = 0;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 291116 WHERE month_first_date = '2022-03-01' and session_traffic_source_group_id = 1 and is_session_with_profile_submitted = 1;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 1016925 WHERE month_first_date = '2022-03-01' and session_traffic_source_group_id = 2 and is_session_with_profile_submitted = 0;
UPDATE jooble_goals.dte_strategy_2021_product_value_plan SET session_plan_cnt = 162938 WHERE month_first_date = '2022-03-01' and session_traffic_source_group_id = 2 and is_session_with_profile_submitted = 1;
