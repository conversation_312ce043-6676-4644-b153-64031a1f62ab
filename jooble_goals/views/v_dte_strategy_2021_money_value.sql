create view v_dte_strategy_2021_money_value
            (month_first_date, call_revenue_plan, apply_revenue_plan, profile_base_revenue_plan,
             digital_recruiter_revenue_plan, unutilized_revenue_plan, call_revenue, apply_revenue, profile_base_revenue,
             digital_recruiter_revenue, unutilized_revenue)
as
WITH money_value_fact AS (
    SELECT v_dte_strategy_2021_client_value.month_first_date,
           v_dte_strategy_2021_client_value.call_answered_30_sec_paid_packet_cnt::numeric * 19::numeric              AS call_revenue,
           v_dte_strategy_2021_client_value.apply_profile_open_contact_paid_packet_cnt::numeric *
           19::numeric                                                                                               AS apply_revenue,
           v_dte_strategy_2021_client_value.profile_base_profile_open_contact_paid_packet_cnt::numeric *
           19::numeric                                                                                               AS profile_base_revenue,
           v_dte_strategy_2021_client_value.digital_recruiter_profile_open_contact_paid_packet_cnt::numeric *
           19::numeric                                                                                               AS digital_recruiter_revenue,
           v_dte_strategy_2021_client_value.call_answered_30_sec_paid_packet_cnt::numeric * 19::numeric +
           v_dte_strategy_2021_client_value.apply_profile_open_contact_paid_packet_cnt::numeric * 19::numeric +
           v_dte_strategy_2021_client_value.profile_base_profile_open_contact_paid_packet_cnt::numeric * 19::numeric +
           v_dte_strategy_2021_client_value.digital_recruiter_profile_open_contact_paid_packet_cnt::numeric *
           19::numeric                                                                                               AS unutilized_revenue
    FROM jooble_goals.v_dte_strategy_2021_client_value
    WHERE v_dte_strategy_2021_client_value.month_first_date < '2021-08-01'::date
    UNION
    SELECT date_trunc('month'::text,
                      fn_get_timestamp_from_date_diff(jcoin_model_daily.action_datediff)) AS month_first_date,
           sum(jcoin_model_daily.call_revenue)                                            AS call_revenue,
           sum(jcoin_model_daily.apply_revenue)                                           AS apply_revenue,
           sum(jcoin_model_daily.profile_base_revenue)                                    AS profile_base_revenue,
           sum(jcoin_model_daily.digital_recruiter_revenue)                               AS digital_recruiter_revenue,
           sum(jcoin_model_daily.revenue_pnl_uah) - sum(jcoin_model_daily.call_revenue) -
           sum(jcoin_model_daily.apply_revenue) - sum(jcoin_model_daily.profile_base_revenue) -
           sum(jcoin_model_daily.digital_recruiter_revenue)                               AS unutilized_revenue
    FROM employer.jcoin_model_daily
    WHERE date_trunc('month'::text, fn_get_timestamp_from_date_diff(jcoin_model_daily.action_datediff)) >=
          '2021-08-01 00:00:00'::timestamp without time zone
    GROUP BY (date_trunc('month'::text, fn_get_timestamp_from_date_diff(jcoin_model_daily.action_datediff)))
)
SELECT mv_plan.month_first_date,
       mv_plan.call_revenue_plan,
       mv_plan.apply_revenue_plan,
       mv_plan.profile_base_revenue_plan,
       mv_plan.digital_recruiter_revenue_plan,
       mv_plan.unutilized_revenue_plan,
       sum(cv_fact.call_revenue)              AS call_revenue,
       sum(cv_fact.apply_revenue)             AS apply_revenue,
       sum(cv_fact.profile_base_revenue)      AS profile_base_revenue,
       sum(cv_fact.digital_recruiter_revenue) AS digital_recruiter_revenue,
       sum(cv_fact.unutilized_revenue)        AS unutilized_revenue
FROM jooble_goals.dte_strategy_2021_money_value_plan mv_plan
         LEFT JOIN money_value_fact cv_fact ON mv_plan.month_first_date = cv_fact.month_first_date
GROUP BY mv_plan.month_first_date, mv_plan.call_revenue_plan, mv_plan.apply_revenue_plan,
         mv_plan.profile_base_revenue_plan, mv_plan.digital_recruiter_revenue_plan, mv_plan.unutilized_revenue_plan;

alter table v_dte_strategy_2021_money_value
    owner to dap;

grant select on v_dte_strategy_2021_money_value to readonly;

