create or replace view jooble_goals.v_dte_strategy_2021_client_value_plan_daily
            (date, plan_datediff, call_plan_cnt, call_answered_30_sec_free_packet_plan_cnt,
             call_answered_30_sec_paid_packet_plan_cnt, apply_profile_viewed_plan_cnt,
             apply_profile_open_contact_free_packet_plan_cnt, apply_profile_open_contact_paid_packet_plan_cnt,
             profile_base_search_plan_cnt, profile_base_profile_viewed_plan_cnt,
             profile_base_profile_open_contact_free_packet_plan_cnt,
             profile_base_profile_open_contact_paid_packet_plan_cnt, digital_recruiter_profile_plan_cnt,
             digital_recruiter_profile_viewed_plan_cnt, digital_recruiter_profile_open_contact_free_packet_plan_cnt,
             digital_recruiter_profile_open_contact_paid_packet_plan_cnt, jcoin_utilized_plan_cnt, ajpu7_plan)
as
SELECT ic.dt                                                                                                       AS date,
       ic.date_diff                                                                                                AS plan_datediff,
       round(cvp.call_plan_cnt::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS call_plan_cnt,
       round(cvp.call_answered_30_sec_free_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS call_answered_30_sec_free_packet_plan_cnt,
       round(cvp.call_answered_30_sec_paid_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS call_answered_30_sec_paid_packet_plan_cnt,
       round(cvp.apply_profile_viewed_plan_cnt::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS apply_profile_viewed_plan_cnt,
       round(cvp.apply_profile_open_contact_free_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS apply_profile_open_contact_free_packet_plan_cnt,
       round(cvp.apply_profile_open_contact_paid_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS apply_profile_open_contact_paid_packet_plan_cnt,
       round(cvp.profile_base_search_plan_cnt::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS profile_base_search_plan_cnt,
       round(cvp.profile_base_profile_viewed_plan_cnt::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS profile_base_profile_viewed_plan_cnt,
       round(cvp.profile_base_profile_open_contact_free_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS profile_base_profile_open_contact_free_packet_plan_cnt,
       round(cvp.profile_base_profile_open_contact_paid_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS profile_base_profile_open_contact_paid_packet_plan_cnt,
       round(cvp.digital_recruiter_profile_plan_cnt::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS digital_recruiter_profile_plan_cnt,
       round(cvp.digital_recruiter_profile_viewed_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS digital_recruiter_profile_viewed_plan_cnt,
       round(cvp.digital_recruiter_profile_open_contact_free_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS digital_recruiter_profile_open_contact_free_packet_plan_cnt,
       round(cvp.digital_recruiter_profile_open_contact_paid_packet_plan_cnt::numeric /
             count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS digital_recruiter_profile_open_contact_paid_packet_plan_cnt,
       round(cvp.jcoin_utilized_plan::numeric / count(ic.dt) OVER (PARTITION BY ic.dt_month)::numeric,
             0)::integer                                                                                           AS jcoin_utilized_plan_cnt,
       cvp.ajpu7_plan
FROM jooble_goals.dte_strategy_2021_client_value_plan cvp
         LEFT JOIN dimension.info_calendar ic
                   ON date_trunc('month'::text, ic.dt::timestamp with time zone) = cvp.month_first_date;

alter table jooble_goals.v_dte_strategy_2021_client_value_plan_daily
    owner to dap;

grant select on jooble_goals.v_dte_strategy_2021_client_value_plan_daily to readonly;

