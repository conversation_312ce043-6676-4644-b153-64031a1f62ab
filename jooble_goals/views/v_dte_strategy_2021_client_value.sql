create view jooble_goals.v_dte_strategy_2021_client_value
            (country_id, month_first_date, call_plan_cnt, call_answered_30_sec_free_packet_plan_cnt,
             call_answered_30_sec_paid_packet_plan_cnt, apply_profile_viewed_plan_cnt,
             apply_profile_open_contact_free_packet_plan_cnt, apply_profile_open_contact_paid_packet_plan_cnt,
             profile_base_search_plan_cnt, profile_base_profile_viewed_plan_cnt,
             profile_base_profile_open_contact_free_packet_plan_cnt,
             profile_base_profile_open_contact_paid_packet_plan_cnt, digital_recruiter_profile_plan_cnt,
             digital_recruiter_profile_viewed_plan_cnt, digital_recruiter_profile_open_contact_free_packet_plan_cnt,
             digital_recruiter_profile_open_contact_paid_packet_plan_cnt, call_cnt,
             call_answered_30_sec_free_packet_cnt, call_answered_30_sec_paid_packet_cnt, apply_cnt,
             apply_profile_viewed_cnt, apply_profile_open_contact_free_packet_cnt,
             apply_profile_open_contact_paid_packet_cnt, apply_profile_message_cnt, profile_base_search_cnt,
             profile_base_profile_viewed_cnt, profile_base_profile_open_contact_free_packet_cnt,
             profile_base_profile_open_contact_paid_packet_cnt, profile_base_profile_message_cnt,
             digital_recruiter_profile_cnt, digital_recruiter_profile_viewed_cnt,
             digital_recruiter_profile_open_contact_free_packet_cnt,
             digital_recruiter_profile_open_contact_paid_packet_cnt, digital_recruiter_profile_message_cnt)
as
WITH client_value_fact AS (
    SELECT client_value_premium_model.month_first_date,
           client_value_premium_model.employer_id,
           round(0.27 * client_value_premium_model.click_call_cnt::numeric, 0)::integer                            AS call_cnt,
           round(0.27 * 0.5 * client_value_premium_model.click_call_cnt::numeric * 0.4,
                 0)::integer                                                                                       AS call_answered_30_sec_free_packet_cnt,
           round(0.27 * 0.5 * client_value_premium_model.click_call_cnt::numeric * 0.6,
                 0)::integer                                                                                       AS call_answered_30_sec_paid_packet_cnt,
           client_value_premium_model.apply_cnt,
           client_value_premium_model.apply_profile_viewed_cnt,
           round(client_value_premium_model.apply_profile_open_contact_cnt::numeric * 0.4,
                 0)::integer                                                                                       AS apply_profile_open_contact_free_packet_cnt,
           round(client_value_premium_model.apply_profile_open_contact_cnt::numeric * 0.6,
                 0)::integer                                                                                       AS apply_profile_open_contact_paid_packet_cnt,
           client_value_premium_model.apply_profile_message_cnt,
           client_value_premium_model.profile_base_search_cnt,
           client_value_premium_model.profile_base_profile_viewed_cnt,
           round(client_value_premium_model.profile_base_profile_open_contact_cnt::numeric * 0.7,
                 0)::integer                                                                                       AS profile_base_profile_open_contact_free_packet_cnt,
           round(client_value_premium_model.profile_base_profile_open_contact_cnt::numeric * 0.3,
                 0)::integer                                                                                       AS profile_base_profile_open_contact_paid_packet_cnt,
           client_value_premium_model.profile_base_profile_message_cnt,
           client_value_premium_model.digital_recruiter_profile_cnt,
           client_value_premium_model.digital_recruiter_profile_viewed_cnt,
           round(client_value_premium_model.digital_recruiter_profile_open_contact_cnt::numeric * 0.4,
                 0)::integer                                                                                       AS digital_recruiter_profile_open_contact_free_packet_cnt,
           round(client_value_premium_model.digital_recruiter_profile_open_contact_cnt::numeric * 0.6,
                 0)::integer                                                                                       AS digital_recruiter_profile_open_contact_paid_packet_cnt,
           client_value_premium_model.digital_recruiter_profile_message_cnt
    FROM employer.client_value_premium_model
    WHERE client_value_premium_model.month_first_date < '2021-08-01'::date
    UNION
    SELECT date_trunc('month'::text, jcoin_model_daily.action_date) AS month_first_date,
           jcoin_model_daily.employer_id,
           jcoin_model_daily.call_cnt,
           jcoin_model_daily.call_answered_30_sec_free_packet_cnt,
           jcoin_model_daily.call_answered_30_sec_paid_packet_cnt,
           jcoin_model_daily.apply_cnt,
           jcoin_model_daily.apply_profile_viewed_cnt,
           jcoin_model_daily.apply_profile_open_contact_free_packet_cnt,
           jcoin_model_daily.apply_profile_open_contact_paid_packet_cnt,
           jcoin_model_daily.apply_profile_message_cnt,
           jcoin_model_daily.profile_base_search_cnt,
           jcoin_model_daily.profile_base_profile_viewed_cnt,
           jcoin_model_daily.profile_base_profile_open_contact_free_packet_cnt,
           jcoin_model_daily.profile_base_profile_open_contact_paid_packet_cnt,
           jcoin_model_daily.profile_base_profile_message_cnt,
           jcoin_model_daily.digital_recruiter_profile_cnt,
           jcoin_model_daily.digital_recruiter_profile_viewed_cnt,
           jcoin_model_daily.digital_recruiter_profile_open_contact_free_packet_cnt,
           jcoin_model_daily.digital_recruiter_profile_open_contact_paid_packet_cnt,
           jcoin_model_daily.digital_recruiter_profile_message_cnt
    FROM employer.jcoin_model_daily
    WHERE date_trunc('month'::text, jcoin_model_daily.action_date) >= '2021-08-01 00:00:00'::timestamp without time zone
)
SELECT 1                                                                   AS country_id,
       cv_plan.month_first_date,
       cv_plan.call_plan_cnt,
       cv_plan.call_answered_30_sec_free_packet_plan_cnt,
       cv_plan.call_answered_30_sec_paid_packet_plan_cnt,
       cv_plan.apply_profile_viewed_plan_cnt,
       cv_plan.apply_profile_open_contact_free_packet_plan_cnt,
       cv_plan.apply_profile_open_contact_paid_packet_plan_cnt,
       cv_plan.profile_base_search_plan_cnt,
       cv_plan.profile_base_profile_viewed_plan_cnt,
       cv_plan.profile_base_profile_open_contact_free_packet_plan_cnt,
       cv_plan.profile_base_profile_open_contact_paid_packet_plan_cnt,
       cv_plan.digital_recruiter_profile_plan_cnt,
       cv_plan.digital_recruiter_profile_viewed_plan_cnt,
       cv_plan.digital_recruiter_profile_open_contact_free_packet_plan_cnt,
       cv_plan.digital_recruiter_profile_open_contact_paid_packet_plan_cnt,
       sum(cv_fact.call_cnt)                                               AS call_cnt,
       sum(cv_fact.call_answered_30_sec_free_packet_cnt)                   AS call_answered_30_sec_free_packet_cnt,
       sum(cv_fact.call_answered_30_sec_paid_packet_cnt)                   AS call_answered_30_sec_paid_packet_cnt,
       sum(cv_fact.apply_cnt)                                              AS apply_cnt,
       sum(cv_fact.apply_profile_viewed_cnt)                               AS apply_profile_viewed_cnt,
       sum(cv_fact.apply_profile_open_contact_free_packet_cnt)             AS apply_profile_open_contact_free_packet_cnt,
       sum(cv_fact.apply_profile_open_contact_paid_packet_cnt)             AS apply_profile_open_contact_paid_packet_cnt,
       sum(cv_fact.apply_profile_message_cnt)                              AS apply_profile_message_cnt,
       sum(cv_fact.profile_base_search_cnt)                                AS profile_base_search_cnt,
       sum(cv_fact.profile_base_profile_viewed_cnt)                        AS profile_base_profile_viewed_cnt,
       sum(cv_fact.profile_base_profile_open_contact_free_packet_cnt)      AS profile_base_profile_open_contact_free_packet_cnt,
       sum(cv_fact.profile_base_profile_open_contact_paid_packet_cnt)      AS profile_base_profile_open_contact_paid_packet_cnt,
       sum(cv_fact.profile_base_profile_message_cnt)                       AS profile_base_profile_message_cnt,
       sum(cv_fact.digital_recruiter_profile_cnt)                          AS digital_recruiter_profile_cnt,
       sum(cv_fact.digital_recruiter_profile_viewed_cnt)                   AS digital_recruiter_profile_viewed_cnt,
       sum(cv_fact.digital_recruiter_profile_open_contact_free_packet_cnt) AS digital_recruiter_profile_open_contact_free_packet_cnt,
       sum(cv_fact.digital_recruiter_profile_open_contact_paid_packet_cnt) AS digital_recruiter_profile_open_contact_paid_packet_cnt,
       sum(cv_fact.digital_recruiter_profile_message_cnt)                  AS digital_recruiter_profile_message_cnt
FROM jooble_goals.dte_strategy_2021_client_value_plan cv_plan
         LEFT JOIN client_value_fact cv_fact
                   ON cv_plan.month_first_date = cv_fact.month_first_date AND cv_fact.month_first_date < CURRENT_DATE
GROUP BY cv_plan.month_first_date, cv_plan.call_plan_cnt, cv_plan.call_answered_30_sec_free_packet_plan_cnt,
         cv_plan.call_answered_30_sec_paid_packet_plan_cnt, cv_plan.apply_profile_viewed_plan_cnt,
         cv_plan.apply_profile_open_contact_free_packet_plan_cnt,
         cv_plan.apply_profile_open_contact_paid_packet_plan_cnt, cv_plan.profile_base_search_plan_cnt,
         cv_plan.profile_base_profile_viewed_plan_cnt, cv_plan.profile_base_profile_open_contact_free_packet_plan_cnt,
         cv_plan.profile_base_profile_open_contact_paid_packet_plan_cnt, cv_plan.digital_recruiter_profile_plan_cnt,
         cv_plan.digital_recruiter_profile_viewed_plan_cnt,
         cv_plan.digital_recruiter_profile_open_contact_free_packet_plan_cnt,
         cv_plan.digital_recruiter_profile_open_contact_paid_packet_plan_cnt;

alter table v_dte_strategy_2021_client_value
    owner to dap;

grant select on v_dte_strategy_2021_client_value to readonly;

