------------------
-- ######################## CATOPUS 1_0

select *
from dwh_system.cat_saved_scripts
order by saved_on desc;


select *
from dwh_system.cat_search_history
where searched_on::date >= '2025-09-21'
order by searched_on desc
;


select *
from dwh_system.cat_login_creds
-- where username ~~ '%pbi%'
-- order by user_id
;

-- create table dwh_system.cat_login_creds1 as
-- select name, username, password
-- from dwh_system.cat_login_creds;


grant delete, insert, select, update on dwh_system.cat_login_creds1 to pentaho;


--username not in ('bdu', 'dap', 'kpav', 'npo', 'ono', 'pbi', 'rku', 'vnov', 'ypi', 'ypr', 'rlu', 'yiv');


insert into dwh_system.cat_login_creds1(username, password) values
('<EMAIL>', 'bacBIWDKhaNY')
;


-- <EMAIL>, N34gl#uG08
-- <EMAIL>, NwvdOQPv3Z
-- <EMAIL>, cqqS@T$Qo!



SELECT *
FROM dwh_system.cat_login_creds1
-- WHERE username IN ('mpo',
--                    'mfe',
--                    'evs',
--                    'ag')
;

--

select *
from dwh_system.cat_login_creds1
-- where username ~~ '%mb%'
;

delete from dwh_system.cat_login_creds1
where username = 'mfo,'
--     and password = ' NwvdOQPv3Z'
;



select id,
       username,
       (updated_on::timestamp - run_on::timestamp)::time as result_time,
       status,
       query,
--        step,
       table_name,
--        error,
--        countries_lists,
       countries,
--        run_on,
       updated_on
from dwh_system.cat_remote_run
where updated_on::date >= current_date-3
--     and username = 'yiv'
order by id desc
;



update dwh_system.cat_remote_run
set status = 'killed'
where id = 326;


--save dataframe of shared data
select *
from dwh_system.cat_shared_data
order by id desc;
-- -- ######################## CATOPUS 1_0 end
------------------


------------------
-- ######################## CATOPUS 2_0
select *
from dwh_system.cat_login_creds
order by user_id;


select *
from dwh_system.cat_search_result
order by id desc;


select *
from dwh_system.cat_remote_logs
order by id desc;


select *
from dwh_system.cat2_saved_scripts
order by id desc;


select *
from dwh_system.django_session;




-- ######################## CATOPUS 2_0 end
------------------
