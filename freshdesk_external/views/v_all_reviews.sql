create view freshdesk_external.v_all_reviews
            (id, date_published, date_replied, score, country_code, language_code, country_name, language_name,
             platform_type, is_deleted, review_text, review_translation, reply_time_min)
as
with
    working_hours as (
        select
                '00:00:00'::time without time zone +
                business_hours.start_hour::double precision * '01:00:00'::interval as start_time,
                '00:00:00'::time without time zone + business_hours.end_hour::double precision * '01:00:00'::interval -
                '00:00:01'::interval                                               as end_time
        from
            freshdesk_external.business_hours
    ),
    work_calendar_prep as (
        select
            t.x     as datetime,
            case
                when t.x::time without time zone >= wh.start_time and t.x::time without time zone <= wh.end_time and
                     date_part('dow'::text, t.x) >= 1::double precision and
                     date_part('dow'::text, t.x) <= 5::double precision then 1
                else 0
                end as is_working_hours
        from
            generate_series('2023-09-01 00:00:00+00'::timestamp with time zone,
                            (current_date + 1)::timestamp with time zone, '01:00:00'::interval) t(x)
            cross join working_hours wh
    ),
    work_calendar as (
        select
            work_calendar_prep.datetime,
            work_calendar_prep.is_working_hours,
            lead(work_calendar_prep.is_working_hours) over () as is_next_working_hour,
            lag(work_calendar_prep.is_working_hours) over ()  as is_prev_working_hour
        from
            work_calendar_prep
    ),
    reviews_raw as (
        select
            t.id,
            timezone('europe/kiev'::text, timezone('utc'::text, t.date_published)) as date_published,
            timezone('europe/kiev'::text, timezone('utc'::text, t.date_replied))   as date_replied,
            t.score,
            t.country_code,
            t.language_code,
            dc.country_name,
            dl.language_name,
            3                                                                      as platform_type,
            false                                                                  as is_deleted,
            null::text                                                             as review_text,
            null::text                                                             as review_translation
        from
            freshdesk_external.trustpilot_reviews t
            left join freshdesk_external.dic_country dc
                      on t.country_code::text = dc.country_code::text
            left join freshdesk_external.dic_language dl
                      on t.language_code::text = dl.language_code::text
        union all
        select
            m.review_id      as id,
            m.published_date as date_published,
            m.response_date  as date_replied,
            m.score,
            m.country_code,
            m.language_code,
            dc.country_name,
            dl.language_name,
            m.platform_type,
            m.is_deleted,
            m.review_text,
            m.review_translation
        from
            freshdesk_external.mobile_reviews m
            left join freshdesk_external.dic_country dc
                      on m.country_code::text = dc.country_code::text
            left join freshdesk_external.dic_language dl
                      on m.language_code::text = dl.language_code::text
    ),
    reviews_calendar as (
        select
            rr.id,
            rr.date_published,
            rr.date_replied,
            case
                when rr.date_published < '2023-09-01 00:00:00'::timestamp without time zone then
                        date_part('epoch'::text, rr.date_replied - rr.date_published) / 60.0::double precision
                else null::double precision
                end as cal_reply_time_min,
            rr.score,
            rr.country_code,
            rr.language_code,
            rr.country_name,
            rr.language_name,
            rr.platform_type,
            rr.is_deleted,
            rr.review_text,
            rr.review_translation,
            wc.datetime,
            wc.is_working_hours,
            case
                when wc.datetime = min(wc.datetime) over (partition by rr.platform_type, rr.id) then 1
                else 0
                end as is_first_row,
            case
                when wc.datetime = max(wc.datetime) over (partition by rr.platform_type, rr.id) then 1
                else 0
                end as is_last_row
        from
            reviews_raw rr
            left join work_calendar wc
                      on wc.datetime >= date_trunc('hour'::text, rr.date_published) and
                         wc.datetime <= rr.date_replied and
                         (wc.is_working_hours = 1 or wc.is_next_working_hour = 1 or wc.is_prev_working_hour = 1)
    ),
    review_business_hours as (
        select
            rc.id,
            rc.date_published,
            rc.date_replied,
            rc.cal_reply_time_min,
            rc.score,
            rc.country_code,
            rc.language_code,
            rc.country_name,
            rc.language_name,
            rc.platform_type,
            rc.is_deleted,
            rc.review_text,
            rc.review_translation,
            rc.datetime,
            rc.is_working_hours,
            rc.is_first_row,
            rc.is_last_row,
            (date_part('epoch'::text,
                       case
                           when rc.is_first_row = 1 then rc.is_working_hours::double precision * (coalesce(lead(
                                                                                                           rc.datetime)
                                                                                                           over (partition by rc.platform_type, rc.id order by rc.datetime),
                                                                                                           rc.date_replied::timestamp with time zone) -
                                                                                                  rc.date_published::timestamp with time zone)
                           when rc.is_last_row = 1 then rc.is_working_hours::double precision *
                                                        (rc.date_replied::timestamp with time zone - rc.datetime)
                           else rc.is_working_hours::double precision *
                                (lead(rc.datetime) over (partition by rc.platform_type, rc.id order by rc.datetime) -
                                 rc.datetime)
                           end) / 60::double precision)::numeric as reply_time_min
        from
            reviews_calendar rc
    ),
    result as (
        select
            rbh.id,
            rbh.date_published,
            rbh.date_replied,
            rbh.score,
            rbh.country_code,
            rbh.language_code,
            rbh.country_name,
            rbh.language_name,
            rbh.platform_type,
            rbh.is_deleted,
            rbh.review_text,
            rbh.review_translation,
            coalesce(round(avg(rbh.cal_reply_time_min)::numeric, 2), round(sum(
                                                                                   case
                                                                                       when rbh.reply_time_min >= 0::numeric
                                                                                           then rbh.reply_time_min
                                                                                       else 0::numeric
                                                                                       end), 2)) as reply_time_min
        from
            review_business_hours rbh
        group by
            rbh.id, rbh.date_published, rbh.date_replied, rbh.score, rbh.country_code, rbh.language_code,
            rbh.country_name, rbh.language_name, rbh.platform_type, rbh.is_deleted, rbh.review_text,
            rbh.review_translation
    )
select
    result.id,
    result.date_published,
    result.date_replied,
    result.score,
    result.country_code,
    result.language_code,
    result.country_name,
    result.language_name,
    result.platform_type,
    result.is_deleted,
    result.review_text,
    result.review_translation,
    case
        when result.date_replied is null or result.date_replied < result.date_published then null::numeric
        else result.reply_time_min
        end as reply_time_min
from
    result;