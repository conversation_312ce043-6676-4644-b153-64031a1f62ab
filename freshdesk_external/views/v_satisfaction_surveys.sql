create view freshdesk_external.v_satisfaction_surveys
            (ticket_system, review_id, ticket_id, agent_name, group_name, created_at, score, type_client, type_category,
             type_request)
as
select
    'external'::text                                                    as ticket_system,
    ss.id                                                               as review_id,
    ss.ticket_id,
    a.display_name                                                      as agent_name,
    g.display_name                                                      as group_name,
    timezone('europe/kiev'::text, timezone('utc'::text, ss.created_at)) as created_at,
    case
        when ss.score = '-103'::integer then 1
        when ss.score = 100 then 2
        when ss.score = 103 then 3
        else null::integer
        end                                                             as score,
    ct.name                                                             as type_client,
    c.name                                                              as type_category,
    rt.name                                                             as type_request
from
    freshdesk_external.satisfaction_surveys ss
    left join freshdesk_external.tickets t
              on ss.ticket_id = t.id
    left join freshdesk_external.agents a
              on coalesce(ss.agent_id, t.agent_id) = a.id
    left join freshdesk_external.groups g
              on coalesce(ss.group_id, t.group_id) = g.id
    left join freshdesk_external.client_types ct
              on t.type_client = ct.id
    left join freshdesk_external.category_types c
              on t.type_category = c.id
    left join freshdesk_external.request_types rt
              on t.type_request = rt.id;