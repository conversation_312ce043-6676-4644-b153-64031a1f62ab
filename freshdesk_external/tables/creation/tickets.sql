create table freshdesk_external.tickets
(
    id                  integer   primary key,
    agent_id            integer references freshdesk_external.agents(id),
    group_id            integer references freshdesk_external.groups(id),
    status_id           integer   not null references freshdesk_external.statuses(id),
    channel_id          integer   not null references freshdesk_external.channels(id),
    priority_id         integer   not null references freshdesk_external.priorities(id),
    tags                text,
    created_at          timestamp not null,
    resolved_at         timestamp,
    first_reply_at      timestamp,
    country             varchar(50),
    type_client         integer references freshdesk_external.client_types(id),
    type_category       integer references freshdesk_external.category_types(id),
    type_request        integer references freshdesk_external.request_types(id),
    ticket_type         integer references freshdesk_external.ticket_types(id),
    employer_project_id varchar(100)
);