select countries.name_country_eng                      as country,
       ac.name                                         as campaign_name,
       jsd.date,
       CAST(jsd.id_project as INTEGER),
       CAST(sum(jsd.organic_job_count) as INTEGER)                      as organic_job,
       CAST(sum(jsd.paid_job_count) as INTEGER)                        as paid_job,
       CAST(sum(jsd.paid_job_count + jsd.organic_job_count) AS INTEGER) as total_jobs,
       CAST(sum(min_cpc_job_count) as INTEGER)                         as min_cpc,
       CAST(sum(max_cpc_job_count) as INTEGER)                         as max_cpc,
       CAST(sum(imp.impressions_count) as INTEGER)                     as impressions,
       CAST(sum(jsd.avg_cpc_for_paid_job_count * jsd.paid_job_count ::numeric) /
            NULLIF(sum(jsd.paid_job_count), 0) ::numeric as NUMERIC) AS avg_cpc_for_paid_job_count,
       CAST(countries.alpha_2 || '_' || jsd.id_project as VARCHAR) as client_hash,
       CAST(jsd.job_category_id as INTEGER) as child_category_id,
       CAST(job_kaiju_category.child_name as VARCHAR) as child_category_name,
       CAST(coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name) as VARCHAR) as parent_category_name,
       CAST(CASE
           WHEN jsd.max_cpc_job_count is not null and jsd.max_cpc_job_count > 0 THEN 'Job cpc > max cpc'
           WHEN jsd.min_cpc_job_count is not null and jsd.min_cpc_job_count > 0 THEN 'Job cpc < min cpc'
           ELSE null
           END as varchar) as inactive_reason
from aggregation.jobs_stat_daily as jsd
         join dimension.countries as countries ON jsd.id_country = countries.id
         inner join imp.campaign as ac
                    on ac.country = countries.id and ac.id_project = jsd.id_project and jsd.id_campaign = ac.id
         inner join dimension.info_calendar cc on cast(jsd.date as date) = cc.dt
         inner join imp_statistic.impression_statistic imp
                    on jsd.id_campaign = imp.id_campaign and jsd.id_country = imp.country and
                       imp.date_diff = cc.date_diff
         left join (Select job_kaiju_category.id_child, child_name
                    from dimension.job_kaiju_category
                    union
                    Select job_kaiju_category.id_parent, parent_name
                    from dimension.job_kaiju_category) as job_kaiju_category
                   on jsd.job_category_id = job_kaiju_category.id_child
         left join aggregation.v_job_kaiju_category
                   on job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
where id_country in %(country_ids)s 
    and jsd.date::date BETWEEN %(action_date_start)s and %(action_date_end)s
group by countries.name_country_eng, ac.name, jsd.date, jsd.id_project, countries.alpha_2,
    job_kaiju_category.child_name,
    coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name), jsd.job_category_id, inactive_reason;
