select CAST(c.name_country_eng as VARCHAR) as country,
       CAST(project_id as INTEGER) as id_project,
       CAST(user_id as INTEGER) as id_user,
       CAST(campaign_name as VARCHAR) as campaign_name,
       CAST(id_campaign as INTEGER),
       CAST(public.fn_get_timestamp_from_date_diff(a.action_datediff) as DATE) as date,
       CAST(sum(case when a.away_type = 'JDP only'then a.jdp_away_count end ) as INTEGER) as jdp,
       CAST(sum(case when a.away_type != 'JDP only'then a.jdp_away_count end ) as INTEGER) as away,
       CAST(sum(duplicated_count) as INTEGER) as duplicated,
       CAST(sum(duplicated_count) as INTEGER) as unpaid,
       CAST(0 as INTEGER) jdp_bot,
       CAST(0 as INTEGER) away_bot,
       CAST(c.alpha_2 || '_' || project_id as VARCHAR) as client_hash,
       a.job_category_id                                                                  as child_category_id,
       job_kaiju_category.child_name                                                      as child_category_name,
       coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name) as parent_category_name,
       CAST(sum(case when a.away_type = 'Away from JDP' then a.jdp_away_count end) as INTEGER) as away_from_jdp,
       CAST(sum(revenue_usd) as NUMERIC)                                                                  as cost_usd
from aggregation.jdp_away_clicks_agg a
    inner join dimension.countries c
on a.country_id = c.id
    left join (Select job_kaiju_category.id_child, child_name
    from dimension.job_kaiju_category
    union
    Select job_kaiju_category.id_parent, parent_name
    from dimension.job_kaiju_category) as job_kaiju_category
    on a.job_category_id = job_kaiju_category.id_child
    left join aggregation.v_job_kaiju_category
    on job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE (public.fn_get_timestamp_from_date_diff(a.action_datediff)::DATE BETWEEN %(action_date_start)s and %(action_date_end)s)
  and country_id in %(country_ids)s
group by
    CAST(c.name_country_eng as VARCHAR),
    CAST(project_id as INTEGER) ,
    CAST(user_id as INTEGER) ,
    CAST(campaign_name as VARCHAR) ,
    CAST(id_campaign as INTEGER),
    CAST(public.fn_get_timestamp_from_date_diff(a.action_datediff) as DATE),
    CAST(c.alpha_2 || '_' || project_id as VARCHAR),
    a.job_category_id ,
    job_kaiju_category.child_name ,
    coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name)
union all
select CAST(c.name_country_eng as VARCHAR)   as country,
       CAST(b.id_project as INTEGER)         as id_project,
       CAST(b.id_user as INTEGER)            as id_user,
       CAST(b.campaign_name as VARCHAR)      as campaign_name,
       CAST(b.id_campaign as INTEGER)        as id_campaign,
       CAST(b.date as DATE)                  as date,
       CAST(0 as INTEGER)  as jdp,
       CAST(0 as INTEGER)  as away,
       CAST(0 as INTEGER)  as duplicated,
       CAST(0 as INTEGER)  as unpaid,
       CAST(sum(b.jdp_bot) as INTEGER)      as jdp_bot,
       CAST(sum(b.away_bot) as INTEGER)     as away_bot,
       CAST(c.alpha_2 || '_' || b.id_project as VARCHAR) as client_hash,
       b.child_category_id,
       job_kaiju_category.child_name                                                      as child_category_name,
       coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name) as parent_category_name,
       CAST(0 as INTEGER)   as away_from_jdp,
       CAST(0 as NUMERIC)   as cost_usd
from aggregation.bot_jdp_away_clicks_agg b
    inner join dimension.countries c on b.country_id = c.id
    left join (Select job_kaiju_category.id_child, child_name
    from dimension.job_kaiju_category
    union
    Select job_kaiju_category.id_parent, parent_name
    from dimension.job_kaiju_category) as job_kaiju_category
    on b.child_category_id = job_kaiju_category.id_child
    left join aggregation.v_job_kaiju_category
    on job_kaiju_category.id_child = v_job_kaiju_category.child_category_id
WHERE b.date::DATE BETWEEN %(action_date_start)s and %(action_date_end)s)
  and country_id in %(country_ids)s
group by
    CAST(c.name_country_eng as VARCHAR),
    CAST(b.id_project as INTEGER),
    CAST(b.id_user as INTEGER),
    CAST(b.campaign_name as VARCHAR),
    CAST(b.id_campaign as INTEGER),
    CAST(b.date as DATE),
    CAST(c.alpha_2 || '_' || b.id_project as VARCHAR),
    b.child_category_id,
    job_kaiju_category.child_name,
    coalesce(v_job_kaiju_category.parent_category_name, job_kaiju_category.child_name);